<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE sqlMapConfig PUBLIC "-//iBATIS.com//DTD SQL Map Config 2.0//EN" "http://www.ibatis.com/dtd/sql-map-config-2.dtd">
<sqlMapConfig>
	<!-- 配置属性文件 使用方法如: ${driver}-->
	<properties url="file:pfcAcctConfig/db.properties"/> 
	
	<!-- 全局配置选项 -->
	<settings cacheModelsEnabled="true"  
			  enhancementEnabled="true" 
			  lazyLoadingEnabled="true"  
			  maxRequests="32"        
			  maxSessions="10"         
			  maxTransactions="5"   
			  useStatementNamespaces="true" />
	<!-- 事务管理器 -->
	<transactionManager type="JDBC"> 
		<dataSource type="SIMPLE">
			<property name="JDBC.Driver" value="${tongbaoDataBaseDriver}" />
			<property name="JDBC.ConnectionURL" value="${tongbaoDataBaseUrl}" />
			<property name="JDBC.Username" value="${tongbaoDataBaseUsername}"/>
			<property name="JDBC.Password" value="${tongbaoDataBasePassword}" />
		</dataSource>
	</transactionManager>
	
	<!-- 对各SqlMap 文件的引用 -->
	<sqlMap resource ="config/gbs/pfc/sqlmap-bat.xml" /> 
	<sqlMap resource ="config/gbs/pfc/sqlmap-javaType.xml" /> 
	<sqlMap resource ="config/gbs/pfc/sqlmap-kpi.xml" /> 
	<sqlMap resource ="config/gbs/pfc/sqlmap-ruleAndStaff.xml" /> 
	<sqlMap resource ="config/gbs/pfc/sqlmap-runtime.xml" /> 
	<sqlMap resource ="config/gbs/pfc/sqlmap-util.xml" /> 
	<sqlMap resource ="config/gbs/pfc/sqlmap-acctJob.xml" /> 
	<sqlMap resource ="config/gbs/pfc/sqlmap-team.xml" /> 
</sqlMapConfig> 

