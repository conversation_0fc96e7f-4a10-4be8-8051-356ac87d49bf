@echo off
echo 创建BONC javax虚拟包...

REM 创建临时目录结构
mkdir temp-bonc-javax\com\bonc\javax 2>nul
mkdir temp-bonc-javax\com\bonc\javax\datastore 2>nul

REM 创建DataCenter类
echo package com.bonc.javax; > temp-bonc-javax\com\bonc\javax\DataCenter.java
echo public class DataCenter { >> temp-bonc-javax\com\bonc\javax\DataCenter.java
echo     public static DataCenter getInstance() { >> temp-bonc-javax\com\bonc\javax\DataCenter.java
echo         return new DataCenter(); >> temp-bonc-javax\com\bonc\javax\DataCenter.java
echo     } >> temp-bonc-javax\com\bonc\javax\DataCenter.java
echo } >> temp-bonc-javax\com\bonc\javax\DataCenter.java

REM 创建Datastore类
echo package com.bonc.javax.datastore; > temp-bonc-javax\com\bonc\javax\datastore\Datastore.java
echo public class Datastore { >> temp-bonc-javax\com\bonc\javax\datastore\Datastore.java
echo     public Object executeQuery(String sql) { >> temp-bonc-javax\com\bonc\javax\datastore\Datastore.java
echo         return null; >> temp-bonc-javax\com\bonc\javax\datastore\Datastore.java
echo     } >> temp-bonc-javax\com\bonc\javax\datastore\Datastore.java
echo } >> temp-bonc-javax\com\bonc\javax\datastore\Datastore.java

REM 编译Java文件
cd temp-bonc-javax
javac -cp . com\bonc\javax\*.java com\bonc\javax\datastore\*.java

REM 创建JAR包
jar cf ..\bonc-javax-1.0.jar com\bonc\javax\*.class com\bonc\javax\datastore\*.class

cd ..

REM 安装到Maven仓库
mvn install:install-file -Dfile=bonc-javax-1.0.jar -DgroupId=com.bonc -DartifactId=bonc-javax -Dversion=1.0 -Dpackaging=jar -DgeneratePom=true

REM 清理临时文件
rmdir /s /q temp-bonc-javax
del bonc-javax-1.0.jar

echo BONC javax包创建完成！
