# Maven化改造指南

## 概述

本文档详细说明如何将现有的传统Java Web项目改造为标准的Maven项目结构。

## 改造前准备

### 1. 环境要求
- JDK 1.8+
- Maven 3.6+
- IDE支持（推荐IntelliJ IDEA或Eclipse）

### 2. 备份原项目
```bash
# 创建项目备份
cp -r bcshb-mvn bcshb-mvn-backup
```

## 改造步骤

### 第一步：执行迁移脚本

#### Windows环境
```cmd
migrate-to-maven.bat
```

#### Linux/Mac环境
```bash
chmod +x migrate-to-maven.sh
./migrate-to-maven.sh
```

### 第二步：验证目录结构

改造后的标准Maven目录结构：
```
bcshb-mvn/
├── pom.xml                          # Maven项目配置文件
├── src/
│   ├── main/
│   │   ├── java/                    # Java源代码
│   │   │   ├── com/bonc/...        # 原src/com目录内容
│   │   │   ├── cn/...              # 原src/cn目录内容
│   │   │   └── org/...             # 原src/org目录内容
│   │   ├── resources/               # 配置文件和资源
│   │   │   ├── config/             # 原src/config目录内容
│   │   │   ├── pfcAcctConfig/      # 原pfcAcctConfig目录内容
│   │   │   ├── *.xml               # 原src根目录的XML文件
│   │   │   ├── *.properties        # 原src根目录的properties文件
│   │   │   ├── application-dev.properties
│   │   │   ├── application-test.properties
│   │   │   └── application-prod.properties
│   │   └── webapp/                  # Web应用文件
│   │       ├── WEB-INF/            # 原app/WEB-INF目录内容
│   │       ├── css/                # 原app/css目录内容
│   │       ├── js/                 # 原app/js目录内容
│   │       ├── images/             # 原app/images目录内容
│   │       ├── pages/              # 原app/pages目录内容
│   │       └── resources/          # 原app/resources目录内容
│   └── test/
│       ├── java/                    # 测试Java代码
│       └── resources/               # 测试资源文件
├── target/                          # Maven构建输出目录
├── .gitignore                       # Git忽略文件配置
└── README.md                        # 项目说明文档
```

### 第三步：处理依赖冲突

#### 1. 检查自定义jar包
原项目中的自定义jar包需要特殊处理：

```xml
<!-- 对于无法从Maven仓库获取的jar包，安装到本地仓库 -->
<dependency>
    <groupId>com.bonc</groupId>
    <artifactId>bonc-pure</artifactId>
    <version>1.0.2</version>
</dependency>
```

安装自定义jar包到本地仓库：
```bash
mvn install:install-file -Dfile=app/WEB-INF/lib/bonc-pure-1.0.2.jar \
    -DgroupId=com.bonc -DartifactId=bonc-pure -Dversion=1.0.2 -Dpackaging=jar
```

#### 2. 版本冲突解决
使用Maven的依赖管理功能解决版本冲突：
```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.1</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

### 第四步：配置文件调整

#### 1. 更新配置文件路径
由于目录结构变化，需要更新配置文件中的路径引用：

**原路径：** `classpath:config/gbs/util/sqlmap-module.xml`
**新路径：** `classpath:config/gbs/util/sqlmap-module.xml` (无需修改)

#### 2. 环境配置分离
使用Maven的profile功能实现多环境配置：

```xml
<!-- 在pom.xml中已配置了dev、test、prod三个环境 -->
```

激活不同环境：
```bash
# 开发环境（默认）
mvn clean package

# 测试环境
mvn clean package -Ptest

# 生产环境
mvn clean package -Pprod
```

### 第五步：构建验证

#### 1. 编译验证
```bash
mvn clean compile
```

#### 2. 测试验证
```bash
mvn clean test
```

#### 3. 打包验证
```bash
mvn clean package
```

#### 4. 本地运行测试
```bash
mvn jetty:run
```
访问：http://localhost:8080/bcshb

## 常见问题及解决方案

### 1. 编译错误

**问题：** 找不到某些类或包
**解决：** 
- 检查Java源文件是否正确迁移到src/main/java
- 确认包名和目录结构一致
- 检查依赖jar包是否在pom.xml中正确配置

### 2. 配置文件找不到

**问题：** 运行时找不到配置文件
**解决：**
- 确认配置文件已迁移到src/main/resources
- 检查代码中的配置文件路径引用
- 使用classpath:前缀引用资源文件

### 3. 依赖冲突

**问题：** Maven依赖版本冲突
**解决：**
```bash
# 查看依赖树
mvn dependency:tree

# 排除冲突依赖
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-core</artifactId>
    <version>3.0.3.RELEASE</version>
    <exclusions>
        <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 4. Web资源访问问题

**问题：** 静态资源（CSS、JS、图片）无法访问
**解决：**
- 确认静态资源已迁移到src/main/webapp
- 检查web.xml中的静态资源映射配置
- 验证Struts2的静态资源过滤配置

## 后续优化建议

### 1. 依赖版本升级
逐步升级框架版本，建议顺序：
1. Spring 3.0.3 → 3.2.18 → 4.3.30
2. Struts2 2.3.15.1 → 2.5.30
3. iBatis 2.3.4 → MyBatis 3.5.x

### 2. 构建优化
- 配置Maven多模块结构
- 添加代码质量检查插件
- 集成单元测试覆盖率报告

### 3. CI/CD集成
- 配置Jenkins或GitLab CI
- 自动化构建和部署
- 环境配置自动化

## 验收标准

### 1. 构建成功
- `mvn clean compile` 无错误
- `mvn clean package` 生成war包
- `mvn jetty:run` 能正常启动

### 2. 功能验证
- 登录功能正常
- 主要业务模块可访问
- 数据库连接正常
- 静态资源加载正常

### 3. 性能验证
- 启动时间无明显增加
- 内存使用无异常增长
- 响应时间保持正常

## 总结

Maven化改造是项目现代化的重要一步，通过标准化的项目结构和依赖管理，为后续的云原生改造奠定了基础。改造过程中要注意：

1. **渐进式改造**：先确保基本功能正常，再逐步优化
2. **充分测试**：每个步骤都要进行充分的测试验证
3. **文档记录**：记录改造过程中的问题和解决方案
4. **团队培训**：确保团队成员熟悉Maven的使用

完成Maven化改造后，项目将具备：
- 标准化的项目结构
- 自动化的依赖管理
- 多环境配置支持
- 持续集成基础
- 云原生改造准备
