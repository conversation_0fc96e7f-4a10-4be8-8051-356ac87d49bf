#!/bin/bash

echo "========================================"
echo "Maven化改造验证脚本"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 验证结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_item() {
    local description="$1"
    local condition="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查: $description ... "
    
    if eval "$condition"; then
        echo -e "${GREEN}✓ 通过${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}✗ 失败${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

echo "开始验证Maven化改造结果..."
echo

echo "1. 检查基本文件结构..."
check_item "pom.xml文件存在" "[ -f pom.xml ]"
check_item "src/main/java目录存在" "[ -d src/main/java ]"
check_item "src/main/resources目录存在" "[ -d src/main/resources ]"
check_item "src/main/webapp目录存在" "[ -d src/main/webapp ]"
check_item "src/test/java目录存在" "[ -d src/test/java ]"
check_item "src/test/resources目录存在" "[ -d src/test/resources ]"

echo
echo "2. 检查Java源代码迁移..."
check_item "com.bonc包结构存在" "[ -d src/main/java/com/bonc ]"
check_item "Java源文件存在" "find src/main/java -name '*.java' | head -1 | grep -q '.java'"

echo
echo "3. 检查配置文件迁移..."
check_item "Spring配置文件存在" "find src/main/webapp/WEB-INF -name 'applicationContext*.xml' | head -1 | grep -q '.xml'"
check_item "Struts配置文件存在" "find src/main/resources -name 'struts*.xml' | head -1 | grep -q '.xml'"
check_item "iBatis配置文件存在" "find src/main/resources -name 'sqlmap*.xml' | head -1 | grep -q '.xml'"
check_item "web.xml文件存在" "[ -f src/main/webapp/WEB-INF/web.xml ]"

echo
echo "4. 检查环境配置文件..."
check_item "开发环境配置存在" "[ -f src/main/resources/application-dev.properties ]"
check_item "测试环境配置存在" "[ -f src/main/resources/application-test.properties ]"
check_item "生产环境配置存在" "[ -f src/main/resources/application-prod.properties ]"

echo
echo "5. 检查Web资源文件..."
check_item "CSS文件存在" "find src/main/webapp -name '*.css' | head -1 | grep -q '.css'"
check_item "JavaScript文件存在" "find src/main/webapp -name '*.js' | head -1 | grep -q '.js'"
check_item "JSP文件存在" "find src/main/webapp -name '*.jsp' | head -1 | grep -q '.jsp'"
check_item "图片资源存在" "find src/main/webapp -name '*.png' -o -name '*.jpg' -o -name '*.gif' | head -1 | grep -q '\.(png\|jpg\|gif)'"

echo
echo "6. 检查Maven配置..."
if command -v mvn &> /dev/null; then
    check_item "Maven命令可用" "true"
    
    echo "   正在验证pom.xml语法..."
    if mvn help:effective-pom -q > /dev/null 2>&1; then
        check_item "pom.xml语法正确" "true"
    else
        check_item "pom.xml语法正确" "false"
        echo -e "   ${YELLOW}提示: 运行 'mvn help:effective-pom' 查看详细错误信息${NC}"
    fi
else
    check_item "Maven命令可用" "false"
    echo -e "   ${YELLOW}警告: 未安装Maven或Maven不在PATH中${NC}"
fi

echo
echo "7. 检查Git配置..."
check_item ".gitignore文件存在" "[ -f .gitignore ]"
if [ -f .gitignore ]; then
    check_item ".gitignore包含target目录" "grep -q 'target/' .gitignore"
    check_item ".gitignore包含IDE配置" "grep -q '.idea/' .gitignore"
fi

echo
echo "8. 检查目录权限..."
check_item "src目录可读写" "[ -r src ] && [ -w src ]"
check_item "target目录可创建" "mkdir -p target && [ -d target ]"

echo
echo "========================================"
echo "验证结果汇总"
echo "========================================"
echo "总检查项: $TOTAL_CHECKS"
echo -e "通过: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "失败: ${RED}$FAILED_CHECKS${NC}"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}✓ 所有检查项都通过！Maven化改造成功！${NC}"
    echo
    echo "建议的下一步操作："
    echo "1. 运行 'mvn clean compile' 验证编译"
    echo "2. 运行 'mvn clean package' 验证打包"
    echo "3. 运行 'mvn jetty:run' 启动应用测试"
    echo "4. 在IDE中导入Maven项目"
    exit 0
else
    echo -e "${RED}✗ 发现 $FAILED_CHECKS 个问题，请检查并修复${NC}"
    echo
    echo "常见问题解决方案："
    echo "1. 如果文件缺失，请重新运行迁移脚本"
    echo "2. 如果Maven不可用，请安装Maven 3.6+"
    echo "3. 如果pom.xml有语法错误，请检查XML格式"
    echo "4. 如果权限问题，请检查文件系统权限"
    exit 1
fi
