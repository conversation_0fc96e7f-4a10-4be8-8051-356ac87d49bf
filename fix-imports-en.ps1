# Batch fix JSON and StringUtils import issues

Write-Host "Starting batch import fix..." -ForegroundColor Green

# Find all Java files that need JSON import fix
Write-Host "Finding files that need JSON import fix..." -ForegroundColor Yellow
$jsonFiles = Get-ChildItem -Path "src\main\java" -Recurse -Filter "*.java" | Where-Object {
    $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue
    if ($content) {
        $content -match "JSON\." -and $content -notmatch "import.*JSON"
    }
}

Write-Host "Found $($jsonFiles.Count) files that need JSON import fix" -ForegroundColor Cyan

# Fix JSON imports
foreach ($file in $jsonFiles) {
    Write-Host "Fixing file: $($file.Name)" -ForegroundColor White
    try {
        $content = Get-Content $file.FullName -Encoding UTF8
        $newContent = @()
        $importAdded = $false
        
        for ($i = 0; $i -lt $content.Length; $i++) {
            $line = $content[$i]
            
            # Add import after package declaration
            if ($line -match "^package " -and -not $importAdded) {
                $newContent += $line
                # Find next import statement position
                $j = $i + 1
                while ($j -lt $content.Length -and $content[$j] -notmatch "^import " -and $content[$j].Trim() -ne "") {
                    $newContent += $content[$j]
                    $j++
                }
                # Add empty line if needed
                if ($j -lt $content.Length -and $content[$j-1].Trim() -ne "") {
                    $newContent += ""
                }
                # Add JSON import
                $newContent += "import com.bonc.product.dss.util.JSON;"
                $importAdded = $true
                $i = $j - 1
            } else {
                $newContent += $line
            }
        }
        
        # Write back to file
        $newContent | Set-Content $file.FullName -Encoding UTF8
    } catch {
        Write-Host "Error processing file $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Find all Java files that need StringUtils import fix
Write-Host "Finding files that need StringUtils import fix..." -ForegroundColor Yellow
$stringUtilsFiles = Get-ChildItem -Path "src\main\java" -Recurse -Filter "*.java" | Where-Object {
    $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue
    if ($content) {
        $content -match "StringUtils\." -and $content -notmatch "import.*StringUtils"
    }
}

Write-Host "Found $($stringUtilsFiles.Count) files that need StringUtils import fix" -ForegroundColor Cyan

# Fix StringUtils imports
foreach ($file in $stringUtilsFiles) {
    Write-Host "Fixing file: $($file.Name)" -ForegroundColor White
    try {
        $content = Get-Content $file.FullName -Encoding UTF8
        $newContent = @()
        $importAdded = $false
        
        for ($i = 0; $i -lt $content.Length; $i++) {
            $line = $content[$i]
            
            # Add import after package declaration
            if ($line -match "^package " -and -not $importAdded) {
                $newContent += $line
                # Find next import statement position
                $j = $i + 1
                while ($j -lt $content.Length -and $content[$j] -notmatch "^import " -and $content[$j].Trim() -ne "") {
                    $newContent += $content[$j]
                    $j++
                }
                # Add empty line if needed
                if ($j -lt $content.Length -and $content[$j-1].Trim() -ne "") {
                    $newContent += ""
                }
                # Add StringUtils import
                $newContent += "import org.apache.commons.lang3.StringUtils;"
                $importAdded = $true
                $i = $j - 1
            } else {
                $newContent += $line
            }
        }
        
        # Write back to file
        $newContent | Set-Content $file.FullName -Encoding UTF8
    } catch {
        Write-Host "Error processing file $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Batch fix completed!" -ForegroundColor Green
Write-Host "Fixed $($jsonFiles.Count) JSON import issues" -ForegroundColor Green
Write-Host "Fixed $($stringUtilsFiles.Count) StringUtils import issues" -ForegroundColor Green
