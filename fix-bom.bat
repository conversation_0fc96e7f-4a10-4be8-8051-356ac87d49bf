@echo off
echo Fixing BOM issues in Java files...

REM 使用PowerShell来处理BOM问题
powershell -Command "& {
    $files = Get-ChildItem -Path 'src\main\java' -Recurse -Filter '*.java' | Where-Object {
        $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue
        $content -and $content.StartsWith([char]0xFEFF)
    }
    
    foreach ($file in $files) {
        Write-Host 'Fixing BOM in:' $file.FullName
        $content = Get-Content $file.FullName -Raw
        $content = $content.TrimStart([char]0xFEFF)
        [System.IO.File]::WriteAllText($file.FullName, $content, [System.Text.Encoding]::UTF8)
    }
    
    Write-Host 'BOM fix completed for' $files.Count 'files'
}"

echo BOM fix completed!
pause
