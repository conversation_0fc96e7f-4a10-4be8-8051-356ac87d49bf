# 专门移除BOM字符的PowerShell脚本

param(
    [string]$FilePath = "src\main\java\com\bonc\product\mss\pointSalary\SalaryScoringDetailsAction.java"
)

Write-Host "Removing BOM from file: $FilePath" -ForegroundColor Green

if (Test-Path $FilePath) {
    try {
        # 读取文件的所有字节
        $bytes = [System.IO.File]::ReadAllBytes($FilePath)
        
        # 检查是否有UTF-8 BOM (EF BB BF)
        if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
            Write-Host "BOM detected, removing..." -ForegroundColor Yellow
            
            # 移除前3个字节（BOM）
            $newBytes = $bytes[3..($bytes.Length-1)]
            
            # 写回文件
            [System.IO.File]::WriteAllBytes($FilePath, $newBytes)
            
            Write-Host "BOM removed successfully!" -ForegroundColor Green
        } else {
            Write-Host "No BOM found in file" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "File not found: $FilePath" -ForegroundColor Red
}

Write-Host "Done!" -ForegroundColor Green
