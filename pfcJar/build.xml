<?xml version="1.0" ?>  
<project name="propertyPro" default="exportRunFiles">
	<property name="classesDir" value="../app/WEB-INF/classes"/>
	<property name="resourcesDir" value="../"/>
	<property name="nreportClassDir" value="com/bonc/product/gbs/pfc"/>
	
	<target name="clean" description="清理临时目录">
		
		<echo level="info">清理完毕</echo>	
	</target>
	<target name="jarFile" depends="clean" description="打包文件">
		<jar jarfile="jar/pfcAcct.jar">
			<manifest>
				<attribute name="Built-By" value="bonc.tanglei" />
				<attribute name="Main-Class" value="com.bonc.product.gbs.pfc.AcctMain" />
				<section name="common">
					<attribute name="Specification-Title" value="绩效考核核算程序" />
					<attribute name="Specification-Version" value="V1.0" />
					<attribute name="Specification-Vendor" value="BONC.TANGLEI" />
				</section>
				<!-- finally, use the magically generated libs path -->
				<attribute name="Class-Path" value=" ./lib/ ./lib/ibatis-2.3.4.726.jar ./lib/bsh-2.0b4.jar ./lib/ojdbc6.jar ./lib/log4j-1.2.15.jar ./lib/commons-logging-1.1.1.jar " />
			</manifest>
			
			<fileset dir="${classesDir}" includes="				
				${nreportClassDir}/,
				config/gbs/pfc/
				"/>
		</jar>
	</target>
	<target name="exportJar" depends="jarFile" description="依赖的jar文件">
		<copyfile dest="jar/lib/bsh-2.0b4.jar" src="../app/WEB-INF/lib/bsh-2.0b4.jar"/>
		<copyfile dest="jar/lib/ibatis-2.3.4.726.jar" src="../app/WEB-INF/lib/ibatis-2.3.4.726.jar"/>
		<copyfile dest="jar/lib/ojdbc6.jar" src="../app/WEB-INF/lib/ojdbc6.jar"/>
		<copyfile dest="jar/lib/log4j-1.2.15.jar" src="../app/WEB-INF/lib/log4j-1.2.15.jar"/>
		<copyfile dest="jar/lib/commons-logging-1.1.1.jar" src="../app/WEB-INF/lib/commons-logging-1.1.1.jar"/>
	</target>
	<target name="exportDoc" depends="exportJar" description="配置文件">
		<copydir dest="jar/pfcAcctConfig" src="../pfcAcctConfig"></copydir>
		<copyfile dest="jar/lib/log4j.properties" src="otherFile/log4j.properties"/>
	</target>
	<target name="exportRunFiles" depends="exportDoc" description="运行文件">
		<copyfile dest="jar/runAcctMonth.bat" src="otherFile/runAcctMonth.bat"/>
		<copyfile dest="jar/runAcctMonth.sh" src="otherFile/runAcctMonth.sh"/>
		<copyfile dest="jar/runAcctDay.bat" src="otherFile/runAcctDay.bat"/>
		<copyfile dest="jar/runAcctDay.sh" src="otherFile/runAcctDay.sh"/>
	</target>
</project>