log4j.rootCategory=INFO, pfcConsole , pfcFile

log4j.appender.pfcConsole=org.apache.log4j.ConsoleAppender
log4j.appender.pfcConsole.layout=org.apache.log4j.PatternLayout
log4j.appender.pfcConsole.layout.ConversionPattern=[%p] %d{yyyy-MM-dd HH:mm:ss} %l: %m%n
log4j.appender.pfcConsole.encoding=GBK

log4j.appender.pfcFile=org.apache.log4j.DailyRollingFileAppender
log4j.appender.pfcFile.File=../logs/rootLogger.log
log4j.appender.pfcFile.layout=org.apache.log4j.PatternLayout
log4j.appender.pfcFile.layout.ConversionPattern=[%p] %d{yyyy-MM-dd HH:mm:ss} %l: %m%n
log4j.appender.pfcFile.encoding=GBK

log4j.logger.com.neusoft=ERROR
log4j.logger.com.opensymphony.oscache=ERROR
log4j.logger.net.sf.navigator=ERROR
log4j.logger.org.apache.commons=ERROR
log4j.logger.org.apache.struts=ERROR
log4j.logger.org.displaytag=ERROR
log4j.logger.org.springframework=ERROR
log4j.logger.com.ibatis=ERROR
log4j.logger.java.sql.Connection=ERROR
log4j.logger.java.sql.Statement=ERROR
log4j.logger.java.sql.PreparedStatement=ERROR
log4j.logger.org.apache.velocity=ERROR
log4j.logger.com.canoo.webtest=ERROR
log4j.logger.org.hibernate.ps.PreparedStatementCache=ERROR
log4j.logger.org.hibernate=ERROR 
log4j.logger.org.logicalcobwebs=ERROR