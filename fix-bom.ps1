# 修复BOM字符问题的PowerShell脚本

Write-Host "Starting BOM fix..." -ForegroundColor Green

# 查找所有有BOM问题的Java文件
$bomFiles = Get-ChildItem -Path "src\main\java" -Recurse -Filter "*.java" | Where-Object {
    $bytes = [System.IO.File]::ReadAllBytes($_.FullName)
    $bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF
}

Write-Host "Found $($bomFiles.Count) files with BOM issues" -ForegroundColor Cyan

# 修复BOM问题
foreach ($file in $bomFiles) {
    Write-Host "Fixing BOM in file: $($file.Name)" -ForegroundColor White
    try {
        # 读取文件内容（不包含BOM）
        $content = Get-Content $file.FullName -Encoding UTF8
        
        # 写回文件（不带BOM）
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllLines($file.FullName, $content, $utf8NoBom)
    } catch {
        Write-Host "Error processing file $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "BOM fix completed!" -ForegroundColor Green
Write-Host "Fixed $($bomFiles.Count) files" -ForegroundColor Green
