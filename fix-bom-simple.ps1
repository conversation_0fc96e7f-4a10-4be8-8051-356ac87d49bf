# 简单的BOM修复脚本

Write-Host "Starting simple BOM fix..." -ForegroundColor Green

# 定义需要修复的文件列表
$filesToFix = @(
    "src\main\java\com\bonc\product\gbs\bet\ShowDeptAction.java",
    "src\main\java\com\bonc\product\gbs\pfc\manage\RuleAction.java",
    "src\main\java\com\bonc\product\gms\bom\VerifyUserNameAction.java",
    "src\main\java\com\bonc\product\gms\bom\bomAction.java",
    "src\main\java\com\bonc\product\gms\baseMobile\wo\BaiduAPI.java",
    "src\main\java\com\bonc\product\gms\util\JsonUtil.java"
)

foreach ($filePath in $filesToFix) {
    if (Test-Path $filePath) {
        Write-Host "Fixing file: $filePath" -ForegroundColor White
        try {
            # 读取文件的所有字节
            $bytes = [System.IO.File]::ReadAllBytes($filePath)
            
            # 检查是否有BOM
            if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
                # 移除BOM（前3个字节）
                $newBytes = $bytes[3..($bytes.Length-1)]
                [System.IO.File]::WriteAllBytes($filePath, $newBytes)
                Write-Host "  BOM removed successfully" -ForegroundColor Green
            } else {
                Write-Host "  No BOM found" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "File not found: $filePath" -ForegroundColor Red
    }
}

Write-Host "Simple BOM fix completed!" -ForegroundColor Green
