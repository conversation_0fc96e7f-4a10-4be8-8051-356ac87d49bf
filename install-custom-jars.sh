#!/bin/bash

echo "========================================"
echo "安装自定义JAR包到Maven本地仓库"
echo "========================================"

# 检查Maven是否可用
if ! command -v mvn &> /dev/null; then
    echo "错误: Maven命令不可用，请先安装Maven"
    exit 1
fi

# 定义自定义jar包列表（基于原项目app/WEB-INF/lib目录分析）
declare -A CUSTOM_JARS=(
    # BONC自定义框架jar包
    ["app/WEB-INF/lib/bonc-pure-1.0.2.jar"]="com.bonc:bonc-pure:1.0.2"
    ["app/WEB-INF/lib/bonc-pure-resources-1.0.2.jar"]="com.bonc:bonc-pure-resources:1.0.2"
    ["app/WEB-INF/lib/bonc-base-2.2.8.jar"]="com.bonc:bonc-base:2.2.8"
    ["app/WEB-INF/lib/bonc-base-resources-2.2.8.jar"]="com.bonc:bonc-base-resources:2.2.8"
    ["app/WEB-INF/lib/bonc-base-patch-0.1.jar"]="com.bonc:bonc-base-patch:0.1"
    ["app/WEB-INF/lib/bonc-x-dss-ext-4.1.18.jar"]="com.bonc:bonc-x-dss-ext:4.1.18"
    ["app/WEB-INF/lib/bonc-frame-dss-old.jar"]="com.bonc:bonc-frame-dss-old:1.0"
    ["app/WEB-INF/lib/bonc-xframe.jar"]="com.bonc:bonc-xframe:1.0"
    ["app/WEB-INF/lib/bonc-sso-client-2.2.0.jar"]="com.bonc:bonc-sso-client:2.2.0"
    
    # 联通定制jar包
    ["app/WEB-INF/lib/LNCU.jar"]="com.unicom:lncu:1.0"
    ["app/WEB-INF/lib/uac-sso-sdk-202305221.jar"]="com.unicom:uac-sso-sdk:20230522.1"
    
    # 其他定制jar包
    ["app/WEB-INF/lib/BoncEcharts1.0.jar"]="com.bonc:bonc-echarts:1.0"
    ["app/WEB-INF/lib/ECharts-2.2.7.jar"]="com.bonc:echarts:2.2.7"
    ["app/WEB-INF/lib/easy-1.1-20120424.jar"]="com.bonc:easy:1.1-20120424"
    ["app/WEB-INF/lib/loongfei-web-1.0.2.jar"]="com.loongfei:loongfei-web:1.0.2"
    
    # 可能无法从Maven中央仓库获取的jar包
    ["app/WEB-INF/lib/jmagick.jar"]="org.jmagick:jmagick:6.4.0"
    ["app/WEB-INF/lib/wc52.jar"]="com.bonc:wc52:1.0"
    ["app/WEB-INF/lib/o-jxcell.jar"]="com.bonc:o-jxcell:1.0"
    ["app/WEB-INF/lib/b-commons-digester.jar"]="com.bonc:b-commons-digester:1.0"
    ["app/WEB-INF/lib/b-jdom-1.1.jar"]="com.bonc:b-jdom:1.1"
)

# 安装函数
install_jar() {
    local jar_path="$1"
    local maven_coords="$2"
    
    # 解析Maven坐标
    IFS=':' read -ra COORDS <<< "$maven_coords"
    local group_id="${COORDS[0]}"
    local artifact_id="${COORDS[1]}"
    local version="${COORDS[2]}"
    
    echo "正在安装: $jar_path"
    echo "  GroupId: $group_id"
    echo "  ArtifactId: $artifact_id"
    echo "  Version: $version"
    
    if [ -f "$jar_path" ]; then
        mvn install:install-file \
            -Dfile="$jar_path" \
            -DgroupId="$group_id" \
            -DartifactId="$artifact_id" \
            -Dversion="$version" \
            -Dpackaging=jar \
            -DgeneratePom=true
        
        if [ $? -eq 0 ]; then
            echo "  ✓ 安装成功"
        else
            echo "  ✗ 安装失败"
            return 1
        fi
    else
        echo "  ✗ 文件不存在: $jar_path"
        return 1
    fi
    echo
}

# 检查原始lib目录是否存在
if [ ! -d "app/WEB-INF/lib" ]; then
    echo "警告: app/WEB-INF/lib 目录不存在"
    echo "请确保在项目根目录执行此脚本"
    exit 1
fi

echo "开始安装自定义JAR包..."
echo

# 统计变量
total_jars=0
success_count=0
fail_count=0

# 遍历并安装所有自定义jar包
for jar_path in "${!CUSTOM_JARS[@]}"; do
    maven_coords="${CUSTOM_JARS[$jar_path]}"
    total_jars=$((total_jars + 1))
    
    if install_jar "$jar_path" "$maven_coords"; then
        success_count=$((success_count + 1))
    else
        fail_count=$((fail_count + 1))
    fi
done

echo "========================================"
echo "安装结果汇总"
echo "========================================"
echo "总计JAR包: $total_jars"
echo "成功安装: $success_count"
echo "安装失败: $fail_count"

if [ $fail_count -eq 0 ]; then
    echo "✓ 所有自定义JAR包安装成功！"
    echo
    echo "现在可以运行以下命令验证项目："
    echo "1. mvn dependency:resolve - 解析所有依赖"
    echo "2. mvn clean compile - 编译项目"
    echo "3. mvn clean package - 打包项目"
else
    echo "✗ 有 $fail_count 个JAR包安装失败"
    echo
    echo "请检查失败的JAR包文件是否存在，然后重新运行脚本"
fi

echo
echo "注意事项："
echo "1. 这些JAR包已安装到本地Maven仓库 (~/.m2/repository)"
echo "2. 如果团队其他成员需要构建项目，他们也需要运行此脚本"
echo "3. 建议将这些JAR包上传到私有Maven仓库供团队共享"
echo "4. 生产环境部署时确保这些依赖可用"

echo "========================================"
