# 批量修复JSON和StringUtils导入问题的PowerShell脚本

Write-Host "开始批量修复导入问题..." -ForegroundColor Green

# 查找所有需要修复JSON导入的Java文件
Write-Host "查找需要修复JSON导入的文件..." -ForegroundColor Yellow
$jsonFiles = Get-ChildItem -Path "src\main\java" -Recurse -Filter "*.java" | Where-Object {
    $content = Get-Content $_.FullName -Raw
    $content -match "JSON\." -and $content -notmatch "import.*JSON"
}

Write-Host "找到 $($jsonFiles.Count) 个需要修复JSON导入的文件" -ForegroundColor Cyan

# 修复JSON导入
foreach ($file in $jsonFiles) {
    Write-Host "修复文件: $($file.Name)" -ForegroundColor White
    $content = Get-Content $file.FullName
    $newContent = @()
    $importAdded = $false
    
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        # 在package声明后添加import
        if ($line -match "^package " -and -not $importAdded) {
            $newContent += $line
            # 找到下一个import语句的位置
            for ($j = $i + 1; $j -lt $content.Length; $j++) {
                if ($content[$j] -match "^import ") {
                    break
                }
                $newContent += $content[$j]
                $i = $j
            }
            # 添加JSON导入
            $newContent += "import com.bonc.product.dss.util.JSON;"
            $importAdded = $true
        } else {
            $newContent += $line
        }
    }
    
    # 写回文件
    $newContent | Set-Content $file.FullName -Encoding UTF8
}

# 查找所有需要修复StringUtils导入的Java文件
Write-Host "查找需要修复StringUtils导入的文件..." -ForegroundColor Yellow
$stringUtilsFiles = Get-ChildItem -Path "src\main\java" -Recurse -Filter "*.java" | Where-Object {
    $content = Get-Content $_.FullName -Raw
    $content -match "StringUtils\." -and $content -notmatch "import.*StringUtils"
}

Write-Host "找到 $($stringUtilsFiles.Count) 个需要修复StringUtils导入的文件" -ForegroundColor Cyan

# 修复StringUtils导入
foreach ($file in $stringUtilsFiles) {
    Write-Host "修复文件: $($file.Name)" -ForegroundColor White
    $content = Get-Content $file.FullName
    $newContent = @()
    $importAdded = $false
    
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        # 在package声明后添加import
        if ($line -match "^package " -and -not $importAdded) {
            $newContent += $line
            # 找到下一个import语句的位置
            for ($j = $i + 1; $j -lt $content.Length; $j++) {
                if ($content[$j] -match "^import ") {
                    break
                }
                $newContent += $content[$j]
                $i = $j
            }
            # 添加StringUtils导入
            $newContent += "import org.apache.commons.lang3.StringUtils;"
            $importAdded = $true
        } else {
            $newContent += $line
        }
    }
    
    # 写回文件
    $newContent | Set-Content $file.FullName -Encoding UTF8
}

Write-Host "批量修复完成！" -ForegroundColor Green
Write-Host "修复了 $($jsonFiles.Count) 个JSON导入问题" -ForegroundColor Green
Write-Host "修复了 $($stringUtilsFiles.Count) 个StringUtils导入问题" -ForegroundColor Green
