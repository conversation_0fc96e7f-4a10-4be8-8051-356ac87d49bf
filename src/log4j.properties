### è®¾ç½® ###
###
# æ­¤å¥ä¸ºå°ç­çº§ä¸ºINFOçæ¥å¿ä¿¡æ¯è¾åºå°stdoutãDãEè¿ä¸ä¸ªä¸ªç®çå°ï¼stdoutåRçå®ä¹å¨ä¸é¢çä»£ç ï¼å¯ä»¥ä»»æèµ·åã
# ç­çº§å¯åä¸ºOFFãFATALãERRORãWARNãINFOãDEBUGãALLï¼å¦æéç½®OFFåä¸æ¾ç¤ºä»»ä½ä¿¡æ¯ï¼å¦æéç½®ä¸ºINFOè¿æ ·åªæ¾ç¤ºINFO, WARN, ERRORçlogä¿¡æ¯ï¼èDEBUGä¿¡æ¯ä¸ä¼è¢«æ¾ç¤º
#
log4j.rootLogger=info,stdout,I,D,E
#log4j.rootLogger=info,stdout
### è®¾ç½®è¾åºsqlççº§å«ï¼å¶ä¸­loggeråé¢çåå®¹å¨é¨ä¸ºjaråä¸­æåå«çåå ###
log4j.logger.org.apache=OFF
log4j.logger.com.ibatis=OFF
log4j.logger.java.sql.Connection=dubug
log4j.logger.java.sql.Statement=dubug
log4j.logger.java.sql.PreparedStatement=info
log4j.logger.java.sql.ResultSet=OFF
### è¾åºä¿¡æ¯å°æ§å¶å° ###
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.Log4jConsole.Threshold=info
#é»è®¤å¼æ¯true,æè°çææçæ¶æ¯é½ä¼è¢«ç«å³è¾åºã
log4j.appender.Log4jConsole.ImmediateFlush=true
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=[%-5p] %d{yyyy-MM-dd HH:mm:ss,SSS} method:%l%n%m%n
### è¾åº INFO çº§å«ä»¥ä¸çæ¥å¿ ###
log4j.appender.I=org.apache.log4j.DailyRollingFileAppender
log4j.appender.I.File=D://workspace/bonc/repos/bcshb/logs/log.log
#log4j.appender.I.File=/data/bschb/logs/log.log
#log4j.appender.I.File=../logs/log.log
log4j.appender.I.Append=true
log4j.appender.I.Threshold=debug
log4j.appender.I.layout=org.apache.log4j.PatternLayout
log4j.appender.I.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss}  [ %t:%r ] - [ %p ]  %m%n
### è¾åº DEBUG çº§å«ä»¥ä¸çæ¥å¿ ###
log4j.appender.D=org.apache.log4j.DailyRollingFileAppender
log4j.appender.D.File=D://workspace/bonc/repos/bcshb/logs/debug.log
#log4j.appender.D.File=/data/bschb/logs/debug.log
#log4j.appender.D.File=../logs/debug.log
log4j.appender.D.Append=true
log4j.appender.D.Threshold=debug
log4j.appender.D.layout=org.apache.log4j.PatternLayout
log4j.appender.D.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss}  [ %t:%r ] - [ %p ]  %m%n
### è¾åº ERROR çº§å«ä»¥ä¸çæ¥å¿ ###
log4j.appender.E=org.apache.log4j.DailyRollingFileAppender
log4j.appender.E.File=D://workspace/bonc/repos/bcshb/logs/error.log
#log4j.appender.E.File=/data/bschb/logs/error.log
#log4j.appender.E.File=../logs/error.log
log4j.appender.E.Append=true
log4j.appender.E.Threshold=error
log4j.appender.E.layout=org.apache.log4j.PatternLayout
log4j.appender.E.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss}  [ %t:%r ] - [ %p ]  %m%n