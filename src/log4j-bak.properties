### éç½®æ ¹ ###
log4j.rootLogger=info,console,fileAppender,dailyRollingFile,ROLLING_FILE,MAIL,DATABASE
### è®¾ç½®è¾åºsqlççº§å«ï¼å¶ä¸­loggeråé¢çåå®¹å¨é¨ä¸ºjaråä¸­æåå«çåå ###
#log4j.logger.org.apache=dubug
#log4j.logger.java.sql.Connection=dubug
log4j.logger.java.sql.Statement=dubug
log4j.logger.java.sql.PreparedStatement=dubug
#log4j.logger.java.sql.ResultSet=dubug
### éç½®è¾åºå°æ§å¶å° ###
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.Target=System.out
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{ABSOLUTE} %5p %c{ 1 }:%L - %m%n
### éç½®è¾åºå°æä»¶ ###
log4j.appender.fileAppender=org.apache.log4j.FileAppender
#log4j.appender.fileAppender.File=/data/bschb/logs/log.log
#log4j.appender.fileAppender.File=D:\\logs\\log.log
log4j.appender.fileAppender.Append=true
log4j.appender.fileAppender.Threshold=DEBUG
log4j.appender.fileAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.fileAppender.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss}  [ %t:%r ] - [ %p ]  %m%n
### éç½®è¾åºå°æä»¶ï¼å¹¶ä¸æ¯å¤©é½åå»ºä¸ä¸ªæä»¶ ###
log4j.appender.dailyRollingFile=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.dailyRollingFile.File=/data/bschb/logs/log.log
#log4j.appender.dailyRollingFile.File=D:\\logs\\log.log
log4j.appender.dailyRollingFile.Append=true
log4j.appender.dailyRollingFile.Threshold=DEBUG
log4j.appender.dailyRollingFile.layout=org.apache.log4j.PatternLayout
log4j.appender.dailyRollingFile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss}  [ %t:%r ] - [ %p ]  %m%n
### éç½®è¾åºå°æä»¶ï¼ä¸å¤§å°å°è¾¾æå®å°ºå¯¸çæ¶åäº§çä¸ä¸ªæ°çæä»¶ ###
log4j.appender.ROLLING_FILE=org.apache.log4j.RollingFileAppender
log4j.appender.ROLLING_FILE.Threshold=ERROR
log4j.appender.ROLLING_FILE.File=rolling.log
log4j.appender.ROLLING_FILE.Append=true
log4j.appender.ROLLING_FILE.MaxFileSize=10KB
log4j.appender.ROLLING_FILE.MaxBackupIndex=1
log4j.appender.ROLLING_FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.ROLLING_FILE.layout.ConversionPattern=[framework] %d - %c -%-4r [%t] %-5p %c %x - %m%n
### éç½®è¾åºå°é®ä»¶ ###
#log4j.appender.MAIL=org.apache.log4j.net.SMTPAppender
#log4j.appender.MAIL.Threshold=FATAL
#log4j.appender.MAIL.BufferSize=10
#log4j.appender.MAIL.From=<EMAIL>
#log4j.appender.MAIL.SMTPHost=mail.hollycrm.com
#log4j.appender.MAIL.Subject=Log4J Message
#log4j.appender.MAIL.To=<EMAIL>
#log4j.appender.MAIL.layout=org.apache.log4j.PatternLayout
#log4j.appender.MAIL.layout.ConversionPattern=[framework] %d - %c -%-4r [%t] %-5p %c %x - %m%n
### éç½®è¾åºå°æ°æ®åº ###
#log4j.appender.DATABASE=org.apache.log4j.jdbc.JDBCAppender
#log4j.appender.DATABASE.URL=********************************
#log4j.appender.DATABASE.driver=com.mysql.jdbc.Driver
#log4j.appender.DATABASE.user=root
#log4j.appender.DATABASE.password=
#log4j.appender.DATABASE.sql=INSERT INTO LOG4J (Message) VALUES ('[framework] %d - %c -%-4r [%t] %-5p %c %x - %m%n')
#log4j.appender.DATABASE.layout=org.apache.log4j.PatternLayout
#log4j.appender.DATABASE.layout.ConversionPattern=[framework] %d - %c -%-4r [%t] %-5p %c %x - %m%n
#log4j.appender.A1=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.A1.File=SampleMessages.log4j
#log4j.appender.A1.DatePattern=yyyyMMdd-HH'.log4j'
#log4j.appender.A1.layout=org.apache.log4j.xml.XMLLayout