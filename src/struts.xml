<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
    "-//Apache Software Foundation//DTD Struts Configuration 2.1.7//EN"
    "http://struts.apache.org/dtds/struts-2.1.7.dtd">

<struts>
	<!-- struts2中action后缀名 -->
	<constant name="struts.action.extension" value="action" />

	<!--配置对象工厂交给spring-->
	<constant name="struts.objectFactory" value="spring"/>

	<!--设置是否总是以自动装配策略创建对象-->
	<constant name="struts.objectFactory.spring.autoWire.alwaysRespect" value="true"/>

	<!-- 默认编码方式 -->
	<constant name="struts.i18n.encoding" value="utf-8" />

	<!-- 是否为开发模式，在开发模型中可以获得更多的跟踪信息 -->
	<constant name="struts.devMode" value="false" />

	<!-- 标签主题 -->
	<constant name="struts.ui.theme" value="pure" />

	<!-- 资源文件名称 -->
	<constant name="struts.custom.i18n.resources" value="globalMessages" />

	<!--静态资源文件配置 -->
	<constant name="struts.serve.static" value="true" />
	
	<!-- 在开发模式下启用 配置改变时自动加载 -->
	<constant name="struts.convention.classes.reload" value="true" />

	<!--  允许Ognl表达式访问静态方法-->
	<constant name="struts.ognl.allowStaticMethodAccess" value="true" />

	<!-- 上传文件的最大文件大小 -->
	<constant name="struts.multipart.maxSize" value="734003200" />

	<!-- action默认的父package，action将自动继承父package的所有属性，在action没有显式声明action的父package时生效 -->
	<constant name="struts.configuration.classpath.defaultParentPackage" value="pure-default" />

	<!-- 在寻找资源时是否忽略大小写 -->
	<constant name="struts.configuration.classpath.forceLowerCase" value="false" />

	<!-- codebehind中查找action的返回结果资源时的默认文件夹 -->
	<constant name="struts.codebehind.pathPrefix" value="/pages/" />

	<include file="struts-pure.xml" />
	<include file="struts-forum.xml" />
	<include file="struts-demo.xml" />
	<include file="struts-org.xml" />
</struts>