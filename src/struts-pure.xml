<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.1.7//EN"
        "http://struts.apache.org/dtds/struts-2.1.7.dtd">
<struts>

    <!-- 默认包 -->
    <package name="pure-default" namespace="/" extends="struts-default">

        <interceptors>
            <!-- 参数拦截器  add  by <EMAIL> at 20140729 for 机构账期-->
            <interceptor name="paramsInterceptor" class="com.bonc.xframe.interceptor.ParamsInterceptor"/>

            <!-- 对登录用户进行鉴权 -->
            <interceptor name="authInterceptor" class="com.bonc.pure.interceptor.AuthInterceptor"/>
            <!-- 识别用户登录的模块 made by zhougd 20100305 -->
            <interceptor name="moduleInterceptor" class="com.bonc.product.gbs.util.interceptor.ModuleInterceptor"/>

            <!-- 在用户没有修改过系统分配的密码情况下，强制用户 修改密码 -->
            <interceptor name="forceModPwdInterceptor" class="com.bonc.pure.interceptor.ForceModPwdInterceptor"/>

            <!-- xframe及struts1框架（老框架）对登录用户进行鉴权适配 -->
            <interceptor name="authAdapterInterceptor" class="com.bonc.xframe.interceptor.AuthAdapterInterceptor"/>

            <!-- 访问日志拦截器 -->
            <interceptor name="visitLogInterceptor" class="com.bonc.pure.interceptor.VisitLogInterceptor"/>
            <!-- 操作日志拦截器 -->
            <interceptor name="operLogInterceptor" class="com.bonc.pure.interceptor.OperLogInterceptor"/>

            <!-- 手机版拦截器  拦截验证sessionkey 记录日志
            <interceptor name="mobile-check-session" class="com.bonc.xframe.interceptor.CheckSessionInterceptor" />
            <interceptor name="mobile-log" class="com.bonc.xframe.interceptor.MobileLogInterceptor" />
            <interceptor-stack name="mobile-defaultStack">
                <interceptor-ref name="mobile-check-session" />
                <interceptor-ref name="mobile-log" />
                <interceptor-ref name="defaultStack" />
            </interceptor-stack>
            -->

            <interceptor name="zongBuInterceptor" class="com.bonc.webservice.interceptor.ZongBuInterceptor"/>
            <!-- 对登录用户进行鉴权 -->
            <interceptor-stack name="pure-defaultStack">
                <interceptor-ref name="zongBuInterceptor"/>
                <!-- 参数拦截器 -->
                <interceptor-ref name="paramsInterceptor"/>
                <interceptor-ref name="authInterceptor"/>
                <!-- 识别用户登录的模块 made by zhougd 20100305 -->
                <interceptor-ref name="moduleInterceptor"/>
                <!-- 强制修改密码拦截器 -->
                <interceptor-ref name="forceModPwdInterceptor"/>
                <!-- 如果确定不需要兼容之前的程序框架，可以考虑注销本拦截器 -->
                <interceptor-ref name="authAdapterInterceptor"/>
                <interceptor-ref name="visitLogInterceptor"/>
                <interceptor-ref name="operLogInterceptor"/>

                <interceptor-ref name="exception">
                    <param name="logEnabled">true</param>
                    <param name="logLevel">
                        warn
                    </param>
                </interceptor-ref>

                <interceptor-ref name="defaultStack"/>
            </interceptor-stack>

        </interceptors>
        <!-- 默认系统使用的拦截器 -->
        <default-interceptor-ref name="pure-defaultStack"/>

        <global-results>
            <!-- 登录页面 -->
            <result name="login-input" type="redirectAction">·
                <param name="actionName">Login</param>
                <param name="method">input</param>
                <param name="namespace">/</param>
            </result>
            <!-- 登录成功，如需登录后跳转到其他页面，则需修改本配置 -->
            <result name="login-success">/WEB-INF/view/pure/Login-redirect.jsp</result>
            <!-- 单点登录成功，跳转到其他页面 -->
            <result name="ssologin-success">/WEB-INF/view/pure/SSOLogin_redirect.jsp</result>
            <!-- 用户使用系统默认密码第一次登录时，需要先修改密码 -->
            <result name="login-first" type="redirectAction">
                <param name="actionName">FirstModifyPwd</param>
                <param name="namespace">/pure_extend</param>
            </result>
            <!-- 用户修改密码的时间超过三个月 -->
            <result name="login-updatepwd" type="redirectAction">
                <param name="actionName">FirstModifyPwd</param>
                <param name="namespace">/pure_extend</param>
            </result>
            <!-- 未登录 -->
            <result name="noLogin">/WEB-INF/view/pure/NoLogin.jsp</result>
        </global-results>

        <!-- 登录 -->
        <action name="Login" class="com.bonc.xframe.pure.security.extend.LoginAction">
            <result name="input">/WEB-INF/view/pure/Login-input.jsp</result>
        </action>
        <action name="MisLogin" class="com.bonc.xframe.pure.security.extend.MisLoginAction">
            <result name="input">/WEB-INF/view/pure/Login-input.jsp</result>
        </action>
        <!-- 2013年3月13日10:56:40 added by pengfei 4 sso  -->
        <action name="SSOLogin" class="com.bonc.pure.struts2.SSOLoginAction">
            <result name="sso">/WEB-INF/view/pure/SSOLogin-sso.jsp</result>
            <result name="input">/WEB-INF/view/pure/Login-input.jsp</result>
            <result name="kaoshi">/WEB-INF/view/pure/SSOLogin-kaoshi.jsp</result>
            <result name="jifen">/WEB-INF/view/pure/SSOLogin-jifen.jsp</result>
            <result name="jifenJump">/WEB-INF/view/pure/SSOLogin-jifenJump.jsp</result>
        </action>
        <!-- sso end -->

        <action name="OALogin" class="com.bonc.pure.struts2.LoginAction">
            <result name="input">/WEB-INF/view/pure/Login-input.jsp</result>
        </action>

        <!--<action name="Login" class="com.bonc.pure.struts2.LoginAction">
            <result name="input">/WEB-INF/view/pure/Login-input.jsp</result>
        </action>
        -->

        <!-- 退出登录 -->
        <action name="Logout" class="com.bonc.pure.struts2.LogoutAction"/>

        <!-- SelectListTag（HTML联动框）使用 -->
        <action name="SelectList" class="com.bonc.tags.html.components.SelectListAction"/>

        <!-- webChart展示使用 -->
        <action name="Chart" class="com.bonc.tags.chart.webcharts.ChartAction"/>
    </package>

    <!-- 框架扩展包 处理不能在/命名空间下配置的action -->
    <package name="pure-extend" namespace="/pure_extend" extends="pure-default">
        <!-- 第一次登录修改密码 -->
        <action name="FirstModifyPwd" class="com.bonc.pure.struts2.ModifyPwdAction">
            <result name="input">/WEB-INF/view/pure/ModifyPwd-input.jsp</result>
        </action>
        <!-- 第一次登录修改密码保存 -->
        <action name="ModifyPwd" class="com.bonc.pure.struts2.ModifyPwdAction">
            <result name="success" type="redirectAction">
                <param name="actionName">Logout</param>
                <param name="namespace">/</param>
            </result>
            <result name="input">/WEB-INF/view/pure/ModifyPwd-input.jsp</result>
        </action>
    </package>

    <package name="pure-security" namespace="/pure" extends="pure-default">
        <action name="*" class="com.bonc.pure.struts2.{1}Action">
            <result name="success">/WEB-INF/view/pure/{1}.jsp</result>
            <result name="create">/WEB-INF/view/pure/{1}-create.jsp</result>
            <result name="update">/WEB-INF/view/pure/{1}-update.jsp</result>
            <result name="view">/WEB-INF/view/pure/{1}-view.jsp</result>
            <result name="latest">/WEB-INF/view/pure/{1}-latest.jsp</result>
            <result name="list">/WEB-INF/view/pure/{1}-list.jsp</result>
            <result name="receive">/WEB-INF/view/pure/{1}-receive.jsp</result>
            <result name="send">/WEB-INF/view/pure/{1}-send.jsp</result>
            <result name="write">/WEB-INF/view/pure/{1}-write.jsp</result>
            <result name="choose">/WEB-INF/view/pure/{1}-choose.jsp</result>
            <result name="input">/WEB-INF/view/pure/{1}-input.jsp</result>
        </action>
    </package>
    <package name="pure-portlet" namespace="/pure/portlet" extends="pure-default">
        <action name="Post" class="com.bonc.pure.struts2.PostAction" method="latest">
            <result name="latest">/WEB-INF/view/pure/portlet/Post.jsp</result>
        </action>
    </package>

    <package name="pure-proxy" namespace="/pure/proxy" extends="pure-default">
        <action name="*" class="com.bonc.pure.struts2.proxy.{1}Action">
            <result name="success">/WEB-INF/view/pure/proxy/{1}.jsp</result>
        </action>
    </package>

    <!-- 手机版  -->
    <package name="xframe-mobile" namespace="/mobile" extends="pure-default">
        <!--
        <default-interceptor-ref name="mobile-defaultStack" />
             <action name="LoginMobile" class="com.bonc.product.mobile.LoginMobileAction">
                    <interceptor-ref name="mobile-log" />
                    <interceptor-ref name="defaultStack" />
             </action>

             <action name="VersionChecked" class="com.bonc.product.mobile.VersionCheckedAction" />
             <action name="SigninInfoAction" class="com.bonc.product.mobile.SigninInfoAction" />
        -->
    </package>

</struts>