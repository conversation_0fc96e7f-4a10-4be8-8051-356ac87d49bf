<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
    "-//Apache Software Foundation//DTD Struts Configuration 2.1.7//EN"
    "http://struts.apache.org/dtds/struts-2.1.7.dtd">
<struts>
	<package name="pure-org" namespace="/org" extends="pure-default">
		<action name="Demo" class="com.bonc.demo.hibernate.struts2.hibernate.DemoAction">
			<result name="success">/pages/hibernate/Demo.jsp</result>
			<result name="create">/pages/hibernate/Demo-create.jsp</result>
			<result name="update">/pages/hibernate/Demo-update.jsp</result>
		</action>
	</package>
</struts>