<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
    "-//Apache Software Foundation//DTD Struts Configuration 2.1.7//EN"
    "http://struts.apache.org/dtds/struts-2.1.7.dtd">
<struts>
	<package name="pure-demo" namespace="/demo" extends="pure-default">
		<action name="IbatisDemo" class="com.bonc.demo.ibatis.struts2.ibatis.IbatisDemoAction">
			<result name="success">/pages/ibatis/IbatisDemo.jsp</result>
			<result name="create">/pages/ibatis/IbatisDemo-create.jsp</result>
			<result name="update">/pages/ibatis/IbatisDemo-update.jsp</result>
		</action>		
		<action name="Demo" class="com.bonc.demo.hibernate.struts2.hibernate.DemoAction">
			<result name="success">/pages/hibernate/Demo.jsp</result>
			<result name="create">/pages/hibernate/Demo-create.jsp</result>
			<result name="update">/pages/hibernate/Demo-update.jsp</result>
		</action>
			<action name="Test" class="com.bonc.demo.ibatis.struts2.ibatis.TestAction">
			<result name="success">/pages/ibatis/Test.jsp</result>
			
		</action>
		
	
	    
	</package>
	
</struts>