html,body{
	font-size:12px;
	padding:0px;margin:0;
	overflow:hidden;
	width:100%;height:100%;
}
#buttonArea{
	background:url(img/bg1.gif);
	border-top:1px solid #F0F5FA;
	border-bottom:1px solid #99BBE8;
	padding:3px;
}
#controlBtns{
	float:right;
}
.btn{
	display:inline-block;
	color:#000;
	text-decoration:none;
	padding-right:3px;
	cursor:pointer;
}
.btn span{
	display:inline-block;
	height:17px;
	line-height:17px;
	padding:2px;
}
.btn img{border:0;vertical-align:text-bottom;}
.btn:hover{background:url(img/btnbgr.gif) top right;}
.btn:hover span{background:url(img/btnbg.gif);}
#listArea{
	overflow-x:hidden;
	overflow-y:auto;
}
#listTitle tr{background:url(img/bg2.gif);}
#listTitle td{padding:5px;border-top:1px solid #F0F5FA;border-left:1px solid #fff;border-right:1px solid #ccc;border-bottom:1px solid #D0D0D0;}
#listBody tr{cursor:pointer;}
#listBody .hover{background:#F0F0F0;}
#listBody .select{background:#DFE8F6;}
#listBody td{padding:5px;border-bottom:1px solid #EDEDED;}
#progressArea{
	background:#D4E1F2;
	border-top:1px solid #99BBE8;
	padding:3px;
}
#progressBar{
	position: relative;
	border:1px solid #6593CF;
	padding:1px;
}
#progress{
	height:16px;
	background:#8FB5E8 url(img/progressbg.gif);
}
#progressBar span{
	position: absolute;
	text-align: center;
	width:100%;line-height:16px;
	color:#396095;
}