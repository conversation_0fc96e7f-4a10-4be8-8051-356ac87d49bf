getContextPath = function() {
	var contentPath = '';
	var link = document.getElementsByTagName('link');
	for ( var q = 0; q < link.length; q++) {
		var h = link[q].href, i;
		if (h && (i = h.indexOf('struts/')) >= 0) {
			var j = h.indexOf('://');
			contentPath = j < 0 ? h.substring(0, i - 1) : h.substring(h
					.indexOf('/', j + 3), i - 1);
			return contentPath;
		}
	}
	;
	return contentPath;
}
importJS = function(url) {
	document.write('<script type="text/javascript" src="' + getContextPath()
			+ '/struts/pure/js/' + url + '"></script>');
}
importJS('01-ajaxlogin.js');
importJS('02-form-extend.js');
importJS('03-window.js');
importJS('04-menu.js');
importJS('05-frame.js');
importJS('06-frame-content.js');
importJS('07tab0.js');
importJS('10-Container.js');
importJS('11-portlet.js');
importJS('jquery.autocomplete.js');
//importJS('minmax.js');