Tab0 = function(options){
	this.setConfig(options);
	this._init();
}

Tab0.prototype = {
		clazz : 'Tab0', //类型
		basePath : '', //应用根路径
		themePath: '', //页面主题目录
		themes : 'tab-page', //当前主题。默认不使用主题
		width : 300, //宽度，integer
		height : 'auto',  //高度'auto'/integer
		editable : false, //Tab是否可编辑
		defaultCardInfo : {}, //(Object/function)新建的默认标签信息
		titleSize : 16, //标题宽度
		blankUrl : 's.gif',
		
		__counter : 1, //计数器
		/**
		 * 初始化Tab标签
		 */
		_init : function(){
			var thiz = this, $ = jQuery;
			
			thiz.element = $('<div class="tab0"></div>');
			//渲染card
			thiz._$head = $('<div class="tab0-head"></div>');
			thiz._$cardWrapper = $('<div class="item-wrapper"></div>').appendTo(thiz._$head);
			thiz._$cardContainer = $('<span class="item-container"></span>').appendTo(thiz._$cardWrapper);
			thiz._$head.append('<div style="clear:both;"></div>');
			
			//渲染内容
			thiz._$contentWrapper = $(['<table class="tab0-tb" cellpadding="0" cellspacing="0"><tbody>',
					'<tr>',
					'	<td class="tab0-cb-lt"><img src="' + thiz.basePath + thiz.themePath + thiz.blankUrl + '"/></td>', 
					'	<td class="tab0-cb-ct"></td>',
					'	<td class="tab0-cb-rt"><img src="' + thiz.basePath + thiz.themePath + thiz.blankUrl + '"/></td>', 
					'</tr>',
					'<tr>',
					'	<td class="tab0-cb-lc"></td>',
					'	<td valign="top"><div class="tab0-content"></div><div style="clear:both;"></div></td>', 
					'   <td class="tab0-cb-rc"></td>',
					'</tr>', 
					'<tr>',
					'	<td class="tab0-cb-lb"><img src="' + thiz.basePath + thiz.themePath + thiz.blankUrl + '"/></td>',
					'	<td class="tab0-cb-cb"></td>', 
					'	<td class="tab0-cb-rb"><img src="' + thiz.basePath + thiz.themePath + thiz.blankUrl + '"/></td>',
					'</tr></tbody></table>'].join('')).appendTo(thiz.element);
			thiz.content = thiz._$contentWrapper.find('.tab0-content');
			
			thiz.element.append(thiz._$head); 
			thiz.element.append(thiz._$contentWrapper);
			
			if(thiz.renderTo == null || thiz.renderTo == ''){
				throw 'There is no container for this tab.';
			}else if(typeof thiz.renderTo == 'string'){
				thiz.renderTo = $('#' + thiz.renderTo);
			}
			
			thiz.element.appendTo(thiz.renderTo);
			thiz.setEditable(thiz.editable);
			
			if(thiz.themes != 'default'){ //设置选项卡主题
				var _new = thiz.themes;
				thiz.themes = 'default';
				thiz.setTheme(_new);
			}
			
			//创建选项卡对象
			var _selectedCard = null;
			if(thiz.cardInfos){
				for(var i = 0; i < thiz.cardInfos.length; i++){
					var _card = thiz._createCard(thiz.cardInfos[i]);
					if(thiz.cardInfos.selected){
						_selectedCard = _card;
					}
				}
				
				if(thiz.cardInfos.length > 0){
					thiz.cards[thiz.cardInfos[thiz.cardInfos.length - 1].instanceId].element.addClass('item-last');
					if(!_selectedCard){
						_selectedCard = thiz.cards[thiz.cardInfos[0].instanceId];
					}
				}
				delete thiz.cardInfos;
			}
			//设置选项卡大小
			thiz._resize({width:thiz.width, height:thiz.height});
			//设置每个标签的内容区大小
			if(thiz.cards){
				for(var k in thiz.cards){
					thiz.cards[k].width = thiz.contentWidth;
					thiz.cards[k].height = thiz.contentHeight;
				}
			}
			if(!this.cardRemovable()){
				this._$cardWrapper.addClass('item-unremovable');
			}

			//选中选中的选项卡
			if(_selectedCard){
				_selectedCard.select();	
			}
		},
		/**
		 * 设置tab是否可编辑
		 * @param {boolean} editable (必选)是否可编辑的
		 * @return
		 */
		setEditable : function(editable){
			var $ = jQuery, thiz = this;
			if(editable && !thiz._editable){
				if(thiz.cards){
					for(var k in thiz.cards){
						thiz.cards[k].setEditable(true);
					}
				}
				thiz._editable = true;
			}else if(!editable && thiz._editable){
				if(thiz.cards){
					for(var k in thiz.cards){
						thiz.cards[k].setEditable(false);
					}
				}
				thiz._editable = false;
			}
		},
		/**
		 * 设置选项卡主题
		 * @param {String/'default'} newTheme (必选)选项卡的新的主题。作为元素class的前缀。
		 * 如果为default，则移除原主题，使用默认主题（空主题）。
		 * @return {void} 
		 * @public
		 */
		setTheme : function(newTheme){
			if(this.themes != newTheme){//设置新的主题
				var $ = jQuery;
				if(this.themes != 'default'){ //原本非默认主题，移除选项卡的原主题
					this.element.removeClass(this.themes + '-tab0');
					$(' > div.tab0-head', this.element).removeClass(this.themes + '-tab0-head');
					$(' > table.tab0-tb', this.element).removeClass(this.themes + '-tab0-tb');

					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-lt', this.element).removeClass(this.themes + '-tab0-cb-lt');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-ct', this.element).removeClass(this.themes + '-tab0-cb-ct');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-rt', this.element).removeClass(this.themes + '-tab0-cb-rt');

					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-lc', this.element).removeClass(this.themes + '-tab0-cb-lc');
					$(' > table.tab0-tb > tbody > tr > td > div.tab0-content', this.element).removeClass(this.themes + '-tab0-content');
					$(' > table.tab0-tb > tbody > tr > td > div.tab0-content > div.tab0-card-content', this.element).removeClass(this.themes + '-tab0-card-content');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-rc', this.element).removeClass(this.themes + '-tab0-cb-rc');
					
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-lb', this.element).removeClass(this.themes + '-tab0-cb-lb');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-cb', this.element).removeClass(this.themes + '-tab0-cb-cb');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-rb', this.element).removeClass(this.themes + '-tab0-cb-rb');
				}
				if(newTheme != 'default'){ //设置新主题
					this.element.addClass(newTheme + '-tab0');
					$(' > div.tab0-head', this.element).addClass(newTheme + '-tab0-head');
					$(' > table.tab0-tb', this.element).addClass(newTheme + '-tab0-tb');

					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-lt', this.element).addClass(newTheme + '-tab0-cb-lt');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-ct', this.element).addClass(newTheme + '-tab0-cb-ct');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-rt', this.element).addClass(newTheme + '-tab0-cb-rt');

					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-lc', this.element).addClass(newTheme + '-tab0-cb-lc');
					$(' > table.tab0-tb > tbody > tr > td > div.tab0-content', this.element).addClass(newTheme + '-tab0-content');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-rc', this.element).addClass(newTheme + '-tab0-cb-rc');
					
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-lb', this.element).addClass(newTheme + '-tab0-cb-lb');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-cb', this.element).addClass(newTheme + '-tab0-cb-cb');
					$(' > table.tab0-tb > tbody > tr > td.tab0-cb-rb', this.element).addClass(newTheme + '-tab0-cb-rb');
				}
				this.themes = newTheme;
			}
		},
		/**
		 * 设置选项卡尺寸
		 * @param {object} newSize (必选)选项卡新的尺寸
		 * @return {void}
		 * @public
		 */
		setSize : function(newSize){
			this._resize(newSize);
			if(this.cards){
				var _cs = {width:this.contentWidth, height: this.contentHeight}; //内容区新尺寸
				for(var k in this.cards){
					if(this.cards[k] == this.selectedCard){
						this.selectedCard.setSize(_cs);
					}else{
						this.cards[k].setSize(_cs, true);
					}
				}
			}
		},
		/**
		 * 连续改变大小前事件处理
		 * @return {void}
		 * @public
		 */
		beforeresize : function(){
			this.selectedCard.beforeresize();
		},
		/**
		 * 连续改变大小过程中事件处理
		 * @param {object} newSize (必选)改变大小过程中当前尺寸
		 * @return {void}
		 * @public
		 */
		onresize : function(newSize){
			this._resize(newSize);
			this.selectedCard.onresize({width:this.contentWidth, height: this.contentHeight});
		},
		/**
		 * 连续改变大小后事件处理
		 * @param {object} newSize (必须)改变大小后选项卡的尺寸
		 * @return {void}
		 * @public
		 */
		afterresize : function(newSize){
			this._resize(newSize);
			if(this.cards){
				var _cs = {width:this.contentWidth, height: this.contentHeight}; //内容区新尺寸
				for(var k in this.cards){
					if(this.cards[k] == this.selectedCard){
						this.selectedCard.afterresize(_cs);
					}else{
						this.cards[k].setSize(_cs, true);
					}
				}
			}
		},
		/**
		 * 新增标签
		 * @param {object} cardInfo (必选)新增的标签的属性对象
		 * @return {object} 新增的标签对象
		 */
		addCard : function(cardInfo){
			var thiz = this;
			var _card = thiz._createCard(cardInfo);
			thiz._fixItemLast();
			if(this.cardRemovable()){
				this._$cardWrapper.removeClass('item-unremovable');
			}
			_card.select();
			return _card;
		},
		/**
		 * 创建标签
		 * @param {object} cardInfo (必选)创建的标签的信息
		 * @return {Tab0.Card} 创建的标签对象
		 * private 
		 */
		_createCard : function(cardInfo){
			this.fireEvent('beforecreatecard', cardInfo);
			this.merge(cardInfo, {
				tab : this,
				editable : this.editable,
				width: this.contentWidth,
				height: this.contentHeight,
				params : this.merge(cardInfo.params, this.params),
				titleSize : this.titleSize,
				instanceId : cardInfo.instanceId || this._newInstanceId()
			})
			
			var _card = new Tab0.Card(cardInfo);
			this.cards = this.cards || {};
			this.cards[_card.instanceId] = _card;
			_card.element.appendTo(this._$cardContainer);

			var thiz = this;
			_card.addListener('beforedestroy', function(){
				delete thiz.cards[this.instanceId];
			});
			_card.addListener('beforeremove', function(){
				thiz._removing = true;
				var $ = jQuery, 
					_$allCards = $('div.tab0-head span.tab0-item', thiz.element),//所有标签
					_length = _$allCards.length;//标签数量
				if(_length > 1){
					var _index = _$allCards.index(this.element), //当前标签的位置
						_sii = null; //下一个选中的标签的实例ID
					if(_index < _length - 1){
						_sii = $(_$allCards[_index + 1]).attr('instanceId');
					}else{
						_sii = $(_$allCards[_index - 1]).attr('instanceId');
					}
					thiz.cards[_sii].select();
				}else{
					thiz.selectedCard = null;
				}
			});
			_card.addListener('afterdestroy', function(){
				if(thiz._removing){
					thiz._fixItemLast();
				}
			});
			this.fireEvent('aftercreatecard', _card);
			return _card;
		},
		/**
		 * 设置选项卡大小
		 * @param {object} newSize (必选)选项卡新的尺寸
		 * @return {void}
		 * @private
		 */
		_resize : function(newSize){
			this.merge(this, newSize);
			this.element.css('width', newSize.width + 'px');
			var pl = parseInt(this._$head.css('padding-left'), 10), //选项卡头部的左边距
			pr = parseInt(this._$head.css('padding-right'), 10), //选项卡头部的右边距
			hw = newSize.width - (isNaN(pl) ? 0 : pl) - (isNaN(pr) ? 0 : pr);//选项卡头部内容宽度
			this._$head.css('width', hw + 'px');
			this._$cardWrapper.css('width', hw + 'px');
			
			var $ = jQuery, _$bt = $('> tbody > tr > td.tab0-cb-lt img', this._$contentWrapper), //选项卡内容外壳的左上角图片
				_bw = _$bt.outerWidth(true);//选项卡内容外壳的左上角图片宽度
			
			this.content.css('width', (newSize.width - 2 * _bw) + 'px');
			this.contentWidth = this.content.width();
			
			this.contentHeight = newSize.height;
			if(newSize.height == 'auto'){
				this.content.css('height', 'auto');
			}else{
				var _$bb = $('> tbody > tr > td.tab0-cb-lb img', this._$contentWrapper); //选项卡内容外壳的左下角图片
				this.content.css('height', (newSize.height - this._$head.outerHeight(true) - _$bb.outerHeight(true) - _$bt.outerHeight(true)) + 'px');
				this.contentHeight = this.content.height();
			}
		},
		/**
		 * 调整最后的标签
		 * @return {void} 
		 * @private
		 */
		_fixItemLast : function(){
			var $ = jQuery;
			$('div.tab0-head span.item-last', this.element).removeClass('item-last');
			$('div.tab0-head span.tab0-item:last', this.element).addClass('item-last');
		},
		/**
		 * 判断选项卡标签是否可移除
		 * @return {boolean} 选项卡标签是否可移除
		 * @public
		 */
		cardRemovable : function(){
			var $ = jQuery;
			return $('.tab0-item', this._$cardWrapper).length > 1;
		},
		/**
		 * 销毁tab
		 * @return
		 */
		destroy : function(){
			if(this.cards){
				for(var k in this.cards){
					this.cards[k].destroy();
				}
				delete this.cards;
			}
			
			this.fireEvent('beforedestroy');
			this._$cardContainer.remove(); //清理标签容器
			delete this._$cardContainer;
			
			this._$cardWrapper.remove(); //清理选项卡标签外壳
			delete this._$cardWrapper;
			
			this.content.remove(); //清理选项卡内容区
			delete this.content;
			
			this._$contentWrapper.remove();
			delete this._$contentWrapper;
			
			this._$head.remove(); //清理tab头
			delete this._$head;
			
			this.element.remove(); //清理整个dom对象
			delete this.element;
		},
		/**
		 * 分配实例ID
		 * @return {string} 生成的页面唯一ID
		 * @private
		 */
		_newInstanceId : function(){
			return 'card' + new Date().getTime() + this.__counter++;
		},
	    /**
	     * 注册事件
	     * @param {String} eventName 事件名称,大小写任意,将自动转化为小写存储
	     * @param {Function} callBack 事件触发函数
	     */
	    addListener: function(eventName, callBack){
	        if (eventName) {
	            if (!this._eventCalls) {
	                this._eventCalls = {};
	                this.addListener('afterdestroy', function(){
	                	delete this._eventCalls;
	                });
	            }
	            var eventName = eventName.toLowerCase();
	            if (this._eventCalls[eventName]) {
	                this._eventCalls[eventName].push(callBack);
	            }
	            else {
	                this._eventCalls[eventName] = [callBack];
	            }
	        }
	    },
	    /**
	     * 移除事件监听。
	     * @param {String} eventName 事件名称
	     * @param {Function} callBack 事件监听回调函数
	     * @return 移除的事件监听回调函数
	     */
	    removeListener: function(eventName, callBack){
	        if (this._eventCalls && eventName) {
	            var calls = this._eventCalls[eventName.toLowerCase()];
	            if (calls) {
	                for (var i = 0; i < calls.length; i++) {
	                    if (calls[i] == callBack) {
	                        return calls.splice(i, 1)[0];
	                    }
	                }
	            }
	        }
	        return null;
	    },
	    /** 
	     * 触发事件 先调用系统本身的事件,如 beforerender，然后调用，调用由addListener添加的事件，当遇到事件的返回值为false[Boolean]时，停止继续调用。
	     * @param {String} eventName 事件名称，大小写任意，将自动转化为小写调用
	     * @param {Object..} 需要传递到触发事件的参数
	     * @private
	     */
	    fireEvent: function(){
	        var _success = true;
	        if (arguments[0]) {
	        	var eventName = arguments[0].toLowerCase();
	            if (this._eventCalls) {
	                var calls = this._eventCalls[eventName];
	                if (calls) {
	                    for (var i = 0; i < calls.length; i++) {
	                        _success = calls[i].apply(this, Array.prototype.slice.call(arguments, 1));
	                        if (_success === false) {
	                            return false;
	                        }
	                    }
	                }
	            }
	        }
	        return _success != false;
	    },
		/**
		 * 设置当前对象的配置信息。将属性赋给当前对象，事件(listeners)赋给当前对象的事件中心
		 * @param {object} config (可选)传入的配置信息
		 */
		setConfig : function(config){
			if(config){
				for(var k in config){
					if(k == 'listeners'){//将配置中的事件监听添加到事件控制中心
						if(config['listeners']){
							for(var i = 0; i < config['listeners'].length; i++){
								for(var k1 in config['listeners'][i]){
									this.addListener(k1, config['listeners'][i][k1]);
								}
							}
						}
					}else{ //将配置中的非事件监听属性添加到当前对象中
						this[k] = config[k];
					}
				}
			}
		},
		/**
		 * 合并对象
		 * @param {object} a (必选)被合并对象
		 * @param {object} b (必选)合并的对象
		 * @return {object} 合并后的新的对象
		 * @public
		 */
		merge : function(a, b){
	    	if(b){
	    		a = a || {};
	    		for(var k in b){
	    			a[k] = b[k];
	    		}
	    	}
	    	return a;
		}
}

Tab0.Card = function(cardInfo){
	//初始属性
	this.setConfig(cardInfo);
	
	this._init(cardInfo);
}

Tab0.Card.prototype = {
		title : '新建标签', //标签标题
		loadContent : function(container, params, newParams){}, //更新内容区(第一次加载或参数改变)回调。
				//container-(jQuery)内容区所在容器；params-(object)选项卡当前参数；newParams-(object)内容上次更新后，新的参数
		editable : false, //是否可编辑
		id : false, //选项卡标签ID
		className : false, //选项卡标签的类名
		hasParam : true, //可以接收参数的
		marginWidth : 0, //标题的外边距
		
		clazz : 'Tab0.Card', //类型
		_paramChanged : true, //参数已经修改
		instanceId : '', //标签的实例ID
		
		setConfig : Tab0.prototype.setConfig,
		addListener : Tab0.prototype.addListener,
		removeListener: Tab0.prototype.removeListener,
		fireEvent : Tab0.prototype.fireEvent,
		merge : Tab0.prototype.merge,
		
		_init : function(){
			var $ = jQuery, thiz = this;
			thiz.element = $('<span class="tab0-item"><span class="tab0-item-1"><span class="tab0-item-3"><span class="tab0-item-2"></span></span></span></span>').bind('click', function(){
				return thiz.select();
			}).bind('mouseover', function(){
				if(thiz.tab.selectedCard != thiz && !thiz.tab._sortableBegin){
					thiz.element.addClass('item-hover');
					thiz.mouseOver = true;
				}
			}).bind('mouseout', function(){
				if(thiz.mouseOver){
					thiz.element.removeClass('item-hover');
					thiz.mouseOver = false;
				}
			}).attr('instanceId', thiz.instanceId);
			thiz.$cardContent = thiz.element.find('span.tab0-item-3');
			thiz._$titleContainer = thiz.element.find('span.tab0-item-2');
			thiz._$title = $('<span class="tab0-title" title="' + thiz.title + '">' + thiz.substr(thiz.title, thiz.titleSize, '...', 2) + '</span>').appendTo(thiz._$titleContainer);
			
			if(thiz.id){
				thiz.element.attr("id", thiz.id);
			}
			if(thiz.className){
				thiz.element.addClass(thiz.className);
			}
			
			thiz.setEditable(thiz.editable);
		},
		/**
		 * 选中选项卡(选中selected css)
		 */
		select : function(){
			if(this.tab.selectedCard != this){
				var $ = jQuery, _headChange = false;
				this.fireEvent('beforeselect');
				
				if(this.tab.selectedCard){
					this.tab.selectedCard.element.removeClass('item-selected');
					this.tab.selectedCard.element.find('.tab0-item-1').removeClass('tab0-drag-place');
					this.tab.selectedCard._$contentWrapper.css('position', 'absolute');
					this.tab.selectedCard._$content.css({
						'height': '0px',
						'position': 'absolute',
						'z-index': 1
					});
					this.__contentHide = true;
				}
				
				try{
					this.updateContent();
				}catch(e){
					Bonc.debug(e, 'Tab0:select-更新内容报错。');
				}
				this.tab.selectedCard = this;
				this.element.addClass('item-selected');
				this.element.find('.tab0-item-1').addClass('tab0-drag-place');
				return this.fireEvent('afterselect');
			}
		},
		/**
		 * 更新内容（参数已经更改，重新加载内容，否则直接显示内容）
		 * @return
		 */
		updateContent : function(){
			var $ = jQuery;
			if(!this._$content){//第一次加载内容
				this._$content = $('<div class="tab0-card-content" style="z-index:2;"></div>').appendTo(this.tab.content);
				this._$contentWrapper = $('<div></div>').appendTo(this._$content);
				this.addListener('afterdestroy', function(){
					this._$contentWrapper.remove();
					delete this._$contentWrapper;
					this._$content.remove();
					delete this._$content;
				});
			}else if(this.__contentHide){ //内容区在临时区
				this._$contentWrapper.css('position', '');
				this._$content.css({
					'height': '',
					'line-height':'',
					'position': 'relative',
					'z-index': 2
				});
				this.__contentHide = false;
			}
			
			if(this._delayResize){ //延迟改变尺寸
				this.setSize({width: this.width, height:this.height});
				this._delayResize = false;
			}

			if(this._paramChanged){ //延迟更新参数
				this.loadContent.call(this, this._$contentWrapper, this.params, this.newParams);
				this.newParams = null;
				this._paramChanged = false;
			}
		},
		/**
		 * 设置选项卡大小
		 * @param {object} newSize (必选)选项卡内容的新的尺寸
		 * @param {boolean} delay (可选)选项卡内容大小改变是否延迟
		 * @return {void}
		 * @public
		 */
		setSize : function(newSize, delay){
			this.width = newSize.width;
			this.height = newSize.height;
			if(delay){
				this._delayResize = true;
				return;
			}
			this._resize(newSize);
			this.fireEvent('aftersetsize', newSize);
		},
		/**
		 * 连续改变选项卡内容大小事件前处理
		 * @return {void}
		 * @public
		 */
		beforeresize : function(){
			this.fireEvent('beforeresize');
		},
		/**
		 * 连续改变大小过程中事件处理
		 * @param {object} newSize (必选)内容区当前尺寸
		 * @return {void}
		 * @public
		 */
		onresize : function(newSize){
			this._resize(newSize);
			this.fireEvent('onresize', newSize);
		},
		/**
		 * 连续改变大小后事件处理
		 * @param {object} newSize (必选)内容区的最终尺寸
		 * @return {void}
		 * @public
		 */
		afterresize : function(newSize){
			this.width = newSize.width;
			this.height = newSize.height;
			this._resize(newSize);
			this.fireEvent('afterresize', newSize);
		},
		/**
		 * 设置内容区大小
		 * @param {object} newSize (必选)选项卡内容区的新尺寸
		 * @return {void}
		 * @private
		 */
		_resize : function(newSize){
			if(this._$contnt){
				this._$content.css('width', newSize.width + 'px');
				this._$contentWrapper.css('width', newSize.width + 'px');
				if(newSize.height == 'auto'){
					this._$content.css('height', 'auto');
					this._$contentWrapper.css('height', 'auto');
				}else{
					this._$content.css('height', newSize.height + 'px');
					this._$contentWrapper.css('height', newSize.height + 'px');
				}
			}
		},
		/**
		 * 设置选项卡状态
		 * @param editable 是否可编辑
		 * @return
		 */
		setEditable : function(editable){
			var $ = jQuery, thiz = this;
			if(editable && !thiz._editable){
				thiz.element.find('.tab0-item-1').addClass('item-editable');
				//可移除
				if(!thiz._$remove){
					thiz._$refresh = $('<span class="item-refresh" title="刷新">&nbsp;</span>').bind('click', function(){
						thiz.refresh();
						return false;
					}).bind('mouseover', function(){
						thiz._$refresh.addClass('refresh-hover');
					}).bind('mouseout', function(){
						thiz._$refresh.removeClass('refresh-hover');
					}).appendTo(thiz.$cardContent);
					
					thiz._$remove = $('<span class="item-remove" title="关闭">&nbsp;</span>').bind('click', function(){
						thiz._close();
						return false;
					}).bind('mouseover', function(){
						thiz._$remove.addClass('remove-hover');
					}).bind('mouseout', function(){
						thiz._$remove.removeClass('remove-hover');
					}).appendTo(thiz.$cardContent);
					thiz.addListener('beforedestroy', function(){
						this._$refresh.remove();
						delete this._$refresh;
						
						this._$remove.remove();
						delete this._$remove;
					});
				}else{
					thiz._$remove.css('display', 'inherit');
				}
				thiz.element.bind('dblclick', function(){
					if(thiz.tab.cardRemovable()){
						thiz._close();
					}
				});
				thiz._editable = true;
			}else if(!editable && thiz._editable){
				thiz.element.find('.tab0-item-1').removeClass('item-editable');
				thiz._$remove.hide();
				thiz._editable = false;
			}
		},
		/**
		 * 刷新内容，创建时覆盖
		 * @return {void}
		 * @public
		 */
		refresh : function(){
			
		},
		/**
		 * 移除当前选项卡
		 * @return
		 */
		remove : function(){
			this.fireEvent('beforeremove');
			if(this.tab._$cardWrapper.find('.tab0-item').length == 2){
				this.tab._$cardWrapper.addClass('item-unremovable');
			}
			this.destroy();
		},
		/**
		 * 销毁选项卡
		 */
		destroy : function(){
			this.fireEvent('beforedestroy');
			
			this._$titleContainer.remove();
			delete this._$titleContainer;
			
			this.$cardContent.remove();
			delete this.$cardContent;
			
			this.element.remove();
			delete this.element;
			
			delete this.tab;
			this.fireEvent('afterdestroy');
		},
	    /**
	     * 关闭选项卡操作
	     * @return
	     */
	    _close : function(){
	        //if (confirm('您真的要移除标签"' + this.title + '"吗？')) {
	        //	this.tab.fireEvent('datachange');
	        	this.remove();
	       // }
	    },
	    /**
	     * 截取字符串
	     * @param {String} str (必选)被截取的字符串
	     * @param {Integer} len (必选)截取长度
	     * @param {String} append (可选)如果字符串被截掉，附加在被截掉字符串后的字符串
	     * @param {Integer} appendLen (可选)如果被截掉的字符串需要附加字符串，此参数指定附加字符串对应占用的宽度
	     * @return {String} 处理后的字符串
	     * @public
	     */
		substr : function(str, len, append, appendLen){
			var g = /[^\x00-\xff]/g, //中文字符正则表达式
			s = str.replace(/[[]/g, 'a').replace(g, '[]'), //处理替代字符串
			l = s.length; //字符串总长度
			//总长度不超过len
			if(l <= len){
				return str;
			}
			//最后一个中文字符需要截掉
			if(s.charAt(len - 1) == '['){
				len--;
			}
			if(append){
				len -= appendLen;
			}else{
				append = '';
			}
			return str.substr(0, s.substr(0, len).replace(/[[]]/g, 'a').length) + append;
		}
}
