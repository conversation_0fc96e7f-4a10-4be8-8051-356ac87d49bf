Bonc.frame.Content = Bonc.extend(Bonc.BaseComponent, {
	clazz : 'Bonc.frame.Content',
	/**
	 * 判断是否使用选项卡展示
	 * @return {boolean} 是否使用选项卡展示
	 * @public
	 */
	useTab : function(){
		return this.cardNumb > 0;
	},
	/**
	 * 指定url更新内容区
	 * @param {string} url (必选)内容区使用url
	 * @param {string} pageName (必选)菜单名称
	 * @param {string} menuId (必选)菜单编号，用于选项卡方式展示菜单内容时，标识选项卡与菜单的对应关系
	 * @param {String} nav (必选)非选项卡方式展示内容时，菜单所在的位置导航
	 * @param {byte} opType (可选)菜单展开类型。0-内容区展开，1-打开新窗口。默认为0
	 * @return {boolean} 是否打开页面
	 */
	updateContent : function(url, pageName, menuId, nav, opType){
		if(!url){
			return;
		}
		if(opType == 1){
			this.openWin(url);
			return true;
		}
		if(this.useTab()){
			var cardInstanceId = 'tab_card_' + menuId;
			if(this.tab.cards && this.tab.cards[cardInstanceId]){
				this.tab.cards[cardInstanceId].select();
			}else{
				var $ = jQuery, $els = $(' > .tab0-head .tab0-item', this.tab.element);
				//选项卡标签数量已经为最大数量，关闭第一个选项卡
				if($els.length == this.cardNumb){
					if(this.tabOperationMode == '1'){
						alert("打开页面数已超过最大限制，请先关闭部分页面后再操作！");
						return false;
					}
					this.tab.cards[$els.first().attr('instanceid')].remove();
				}
				this.tab.addCard({
					title : pageName,
					instanceId : cardInstanceId,
					url: url
				});
			}
		}else{
			if(nav){
				this.$nav.html(' 您所在的位置：' + nav).show();
			}else{
				this.$nav.hide();
			}
			this.$iframe.attr('src', url);
		}
	},
    /**
     * 弹出窗口
     * @param {string} url (必选)弹出窗口的访问路径
     * @param {JSON} options (必选)弹出窗口的备选参数
     * @return {void}
     * @public
     */
    openWin : function(url, options){
    	var _property = '',
			_default = {
				winName : '_blank', //弹出窗口名称
				height : 600,
				width : 1000,
				top : 0,
				left : 0, 
				toolbar : 'no',
				menubar : 'no',
				scrollbars : 'yes',
				resizable : 'yes',
				location : 'no',
				status : 'no',
				align : 'center'
			};
		_default = jQuery.extend(_default, options);
		
		//弹出窗口居中显示属性
		if(_default['align']=='center'){
			_default['top'] = (screen.height-_default['height'])/2;
			_default['left'] = (screen.width-_default['width'])/2;
		}
		
		//设置弹出窗口属性
		for(var k in _default){
			if(k == "height" || k == 'width' || k == 'top' || k == 'left' 
				|| k == 'toolbar' || k == 'menubar' || k == 'scrollbars' 
					|| k == 'resizable' || k == 'location' || k == 'status'){
				_property = _property == '' ? (k + '=' + _default[k]) : (_property + ',' + k + '=' + _default[k]);
			}
		}
		var win = window.open(url, _default['winName'], _property);
		if(win == null){
			alert('页面已经阻止弹出的新窗口！您需要在页面最上方的阻止弹出框提示中取消阻止弹出窗口。');
		}
    },
	/**
	 * 渲染
	 * @return {void}
	 * @protected
	 */
	render : function(){
		if(this.useTab()){
			this.tab = new Tab0({
				editable: true, //是否可编辑
				renderTo: jQuery(this.renderTo),
				width: 800,
				height: 300,
				titleSize : this.titleSize,
				blankUrl : Bonc.Constants.BLANK_IMAGE_URL,
				listeners : [{
					'beforecreatecard': function(cardInfo){
						jQuery.extend(cardInfo, {
							loadContent : function(container, params, newParams){
								this.$iframe = jQuery('<iframe frameborder="0" scrolling="auto" src="' + this.url + '" name="link1" marginheight="0" marginwidth="0" style="padding:0;margin:0;width:' + this.width + 'px;height:' + this.height + 'px;" ></iframe>').appendTo(container);
							},refresh : function(){
								this.$iframe.attr("src", this.url);
							},listeners : [{'aftersetsize' : function(newSize){
									this.$iframe.css({
										width : newSize.width + 'px',
										height : newSize.height + 'px'
									});
								},'beforeresize' : function(){
								}, 'onresize' : function(newSize){
								},'afterresize' : function(newSize){
								},'beforedestroy' : function(){
									if(this.$iframe){
										this.$iframe.remove();
										delete this.$iframe;
									}
								},'beforeremove' : function(){
								},'afterselect' : function(){
									if(this.__firstSelect !== false){
										this.__firstSelect = false;
									}else{
										var $ = jQuery;
										$('<div/>').insertAfter(this.$iframe).remove();
									}
								}
							}]
						});
					}
				}]
			});
		}else{
			var $ = jQuery;
			this.$nav = $('<div id="nav-location"></div>').appendTo(this.renderTo);
			this.$iframe = $('<iframe frameborder="0" scrolling="auto" src="about:blank" name="link1" marginheight="0" marginwidth="0" style="padding:0;margin:0;" ></iframe>').appendTo(this.renderTo);
		}
	},
	/**
	 * 销毁对象
	 * @return {void}
	 * @public
	 */
	destroy : function(){
		if(this.useTab()){
			this.tab.destroy();
		}else{
			this.$iframe.remove();
			delete this.$iframe;
		}
	},
	/**
	 * 设置内容区大小
	 * @param {Object} newSize (必选)内容区的新尺寸
	 * @return {void}
	 * @public
	 */
	resize : function(newSize){
		if(newSize.width != this.width || newSize.height != this.height){
			if(this.useTab()){
				this.tab.setSize(newSize);
			}else{
				this.$iframe.css({
					'width' : newSize.width + 'px',
					'height' : (newSize.height - 25) + 'px'
				});
			}
			this.width = newSize.width;
			this.height = newSize.height;
		}
	}
});