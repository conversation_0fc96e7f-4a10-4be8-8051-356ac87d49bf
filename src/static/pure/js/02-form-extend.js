Bonc.FormExt = {
	/**
	 * 联动select 
	 * @param {JSONObject}
	 *            接受参数如下
	 * @param {String}
	 *            [ajaxUrl]  数据请求连接，相对于应用的绝对路径，不包含应用名，或者是相对路径,默认为/SelectList
	 * @param {String}
	 *            [provider] 后台联动的数据提供实现
	 * @param {String}
	 *            [arg]  联动扩展字段,默认为null
	 * @param {Boolean}
	 *            [showNull] 是否展现空选项,默认为true
	 * @param {Boolean}
	 *            [showNullOnlyIfMulti] 展现空选项是否仅仅在候选项是多选的情况下,默认为true
	 * @param {String}
	 *            [headerKey] 空选项的值,默认为-1
	 * @param {String}
	 *            [headerValue] 空选项的描述,默认为"全部"
	 * @param {JSONArray}
	 *            [selects] 联动select配置，按照联动顺序
	 * @param {Array<String>}
	 * 			  [selectIds] 联动select元素ID，按照联动顺序 selects 和selectIds 必须配置一个
	 * @extends Bonc.BaseComponent
	 * @example new Bonc.FormExt.CascadeSelect({
	 * 		provider:'userExtProvider',
  	 * 		showNull:true,
	 * 		showNullOnlyIfMulti:true,
	 * 		headerKey:'-1',
	 * 		headerValue:'全部',
	 * 		arg:'areaId',
	 * 		selects:[
	 * 			{listKey:'area_id',listValue:'area_desc',id:'User_userExtMap_areaId'},
	 * 			{listKey:'city_id',listValue:'city_desc',id:'User_userExtMap_cityId'}
	 * 		]
	 *	});
	 */
	CascadeSelect : Bonc.extend(Bonc.BaseComponent, {
		clazz:'Bonc.FormExt.CascadeSelect',
		provider:null,
		arg:null,
		showNull:true,
		showNullOnlyIfMulti:true,
		ajaxUrl:'/SelectList',
		selectIds:null,
		selects:null,
		defaultListKey:'key',
		defaultListValue:'vdesc',
		headerKey:'-1',
		headerValue:'全部',
		render:function(){
			var selects=this.selects,selectIds=this.selectIds;
			if(selects&&!selectIds){
				selectIds=[];
				for(var i=0;i<selects.length;i++){
					selectIds.push(selects[i].id);
				}
				this.selectIds=selectIds;
			}
			if(selectIds&&selectIds.length>1){
				var _ids='#'+selectIds[0];
				for(var i=1;i<selectIds.length-1;i++){
					_ids+=',#'+selectIds[i];//只联动length-1个数据
				}
				var thiz=this;
				jQuery(_ids).change(function(){
					var fireNext = false, _$ = jQuery(this), ids = selectIds, lvl = jQuery.inArray(_$.attr("id"), ids) + 1;
				    jQuery.getJSON(Bonc.toFullPath(thiz.ajaxUrl), {
				        level: lvl,
				        provider: thiz.provider,
				        parentValue: _$.val(),
				        arg: thiz.arg
				    }, function(data){
				        var nextId = ids[lvl], _$next = jQuery('#' + nextId);
				        document.getElementById(nextId).options.length = 0;
				        if (thiz.showNull && !(thiz.showNullOnlyIfMulti && data.length == 1)) {
				            var varItem = new Option(thiz.headerValue, thiz.headerKey);
				            _$next[0].options.add(varItem);
				        }
				        else {
				            fireNext = true;
				        }
				        var vkey=thiz.selects?thiz.selects[lvl].listKey:'',vdesc=thiz.selects?thiz.selects[lvl].listValue:'';
				        vkey=vkey||thiz.defaultListKey,vdesc=vdesc||thiz.defaultListValue
				        jQuery.each(data, function(i, item){
				            _$next.append('<option value="' + item[vkey] + '">' + item[vdesc] + '</option>');
				        });
				        if (fireNext && lvl + 1 < ids.length) {
				            jQuery('#' + ids[lvl + 1]).change();
				        }
				        else {
				            for (var i = lvl + 1; i < ids.length; i++) {
				                jQuery('#' + ids[i]).html('<option value="'+ thiz.headerKey+'">'+thiz.headerValue+'</option>');
				            }
				        }
				    });
				});
			}
        }
	}),
	/**
	 * 树状选择 
	 * @param {JSONObject}
	 *            接受参数如下
	 * @param {String}
	 *            [ajaxUrl]  数据请求连接，相对于应用的绝对路径，不包含应用名，或者是相对路径,默认为/SelectList
	 * @param {String}
	 *            [provider] 后台联动的数据提供实现
	 * @param {String}
	 *            [arg]  联动扩展字段,默认为null
	 * @param {String}
	 *            [listKey] 节点的选择值
	 * @param {String}
	 *            [listValue] 节点的展现描述
	 * @extends Bonc.BaseComponent
	 * @example new Bonc.FormExt.TreeSelect({
			provider:'userExtProvider',
			arg:'areaId',
			listKey:'id',
			listValue:'text',
			rootId:'root',
			rootText:'树',
			title:'树',
			width:210,
			descInputId:'inputDesc',
			inputId:'inputId'
		});
	 */
	TreeSelect:Bonc.extend(Bonc.BaseComponent, {
        clazz: 'Bonc.FormExt.TreeSelect',
        inputId:null,
        title:'',
        descInputId:null,
        provider:null,
        ajaxUrl:'/SelectList',
        rootId :'',
        rootText:'',
        listKey:'vkey',
        listValue:'vdesc',
        width:210,
        expandAll:false,
        expanded:false,
        jsonConfig:false,
        root:null,
        showTbar:false,
        render:function(){
        	this.treeContainer=jQuery('<div id="'+Bonc.newId()+'" style="position:absolute;">').appendTo('body');
        	var thiz=this,treePanel=new Ext.tree.TreePanel({
        		title:thiz.title||'',
				width : thiz.width||210,
				root : thiz.jsonConfig==true?thiz.root:new Ext.tree.AsyncTreeNode({
					id : thiz.rootId,
					text : thiz.rootText,
					expanded:thiz.expanded
				}),
				applyTo : thiz.treeContainer[0],
				autoScroll : false,
				containerScroll : true,
				enableDrop : true,
				hidden :true,
				loader : thiz.jsonConfig==true?new Ext.tree.TreeLoader():new Ext.tree.TreeLoader({
					dataUrl : thiz.ajaxUrl,
					createNode : function(attr) {
						attr.id = attr[thiz.listKey];
						attr.text = attr[thiz.listValue];
						attr.leaf=parseInt(attr.LEAF) <= 0 ?true:false;
						return parseInt(attr.LEAF) <= 0 ? new Ext.tree.TreeNode(attr): new Ext.tree.AsyncTreeNode(attr);
					}
				}),
				tools : [ {
					id : "close",
					handler : function(event, toolEl, panel) {
						panel.hide();
					}
				} ],
       			 tbar:thiz.showTbar==true?[{
		            text: '保存',
		            iconCls: 'icon-save',
		            handler: function(){
		                var checkedIds=[],checkedTexts=[];	                
		                Ext.each(treePanel.getChecked(), function(node){
		                	checkedIds.push(node.id);
		                	checkedTexts.push(node.text);
		                });
		                $j('#'+thiz.descInputId).val(checkedTexts.join(','));
						$j('#'+thiz.inputId).val(checkedIds.join(','));
						treePanel.hide();
		            }
		        }]:null,
				listeners:{
					beforeload:function(node) {
						var url=Bonc.toFullPath(thiz.ajaxUrl);
						url+=url.indexOf('?')>0?'&':'?';
						node.getOwnerTree().loader.dataUrl = url+'parentValue='+ node.id+'&provider='+thiz.provider+'&arg='+thiz.arg;
					},
					click:thiz.showTbar==true?function(){}:function(node){
						$j('#'+thiz.descInputId).val(node.text);
						$j('#'+thiz.inputId).val(node.id);
						node.getOwnerTree().hide();
					}
				}
			});
			jQuery('#'+this.descInputId).bind("focus",function() {
				var position = jQuery(this).position();
				thiz.treeContainer.css({
					left : position.left,
					top : position.top + 30
				});
				treePanel.show();
				if(thiz.expandAll){
					treePanel.expandAll();
				}
			});
        }
	})
};