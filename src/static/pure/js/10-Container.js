Bonc.Portal = {};
Bonc.Portal.Container = Bonc.extend(Bonc.BaseComponent, {
    clazz: 'Bonc.Portal.Container',
    width: '100%', //默认宽度
    height: 300, //默认高度
    render: function(renderTo){
        var config = this.config;
        if (!config || config.length == 0) {
            config = [{
                width: '50%',
                width: '50%'
            }];
        }
        html = ['<table cellspacing="0" class="portal-columns"><tbody><tr>'];
        for (var i = 0; i < config.length; i++) {
            html.push('<td style="position:static;width:');
            html.push(config[i].width);
            html.push('"></td>');
        }
        html.push('</tr></tbody></table>');
        var portlets = this.portlets || [], portlet = null, td = jQuery('#' + this.renderTo).html(html.join('')).disableSelection().find('td');
        config = this.config;
        if (config && config.length > 0) {
            for (var i = 0; i < config.length; i++) {
                var portletconf = config[i].portlets;
                for (var j = 0; j < portletconf.length; j++) {
                    portlet = portletconf[j];
                    for (var m = 0; m < portlets.length; m++) {
                        if (portlets[m].id == portlet.id) {
                            this.addPortlet(jQuery.extend(portlets[m], portlet), td.eq(i));
                            break;
                        }
                    }
                }
            }
        }
        else {
            for (var i = 0; i < portlets.length && i < 4; i++) {
                portlet = portlets[i];
                this.addPortlet(portlet, td.eq(0));
            }
        }
        
        this._$td = td;
        this.afterrender();
    },
    afterrender: function(){
        this._$td.sortable({
            connectWith: 'td',
            handle:'div.portlet-handler',
            cursor: 'move',
            containment: 'document',
            scroll: false,
            opacity: 0.6,
            delay: 100,
            update: this.moveend
        });
    },
    /**
     * 新增portlet
     * @param {Bonc.Portal.Porlet} portlet
     */
    addPortlet: function(portlet, target){
        if (portlet) {
            new Bonc.Portal.Portlet(Bonc.apply({
                target: target || this._$td.last()
            }, portlet));
        }
    },
    setPortlets: function(ids){
    	var portletObj = this._$td.children('div').hide();
        if (ids && ids.length > 0) {
            var ids = jQuery.isArray(ids) ? ids : [ids], portlets = this.portlets;
            for (var i = 0; i < ids.length; i++) {
                if (portletObj.filter('[id="' + ids[i] + '"]').show().length == 0) {
                    for (var m = 0; m < portlets.length; m++) {
                        if (portlets[m].id == ids[i]) {
                            this.addPortlet(portlets[m]);
                            break;
                        }
                    }
                }
            }
            portletObj.filter(':hidden').remove();
        }
        else {
            portletObj.remove();
        }
    },
    /**
     * 设置分栏参数
     * @param {Array}|{Number} columns
     */
    setColumns: function(columns){
        if (columns) {
            var columns = columns, total = 0;
            if (!isNaN(columns)) {
                var v = 100 / columns;
                total = columns;
                columns = new Array(columns);
                for (var i = 0; i < total; i++) {
                    columns[i] = v + '%';
                }
            }else {
                total = columns.length;
            }
            var $td = this._$td, _total = $td.length, i = 0;
            for (; i < total && i < _total; i++) {
                $td.eq(i).css('width', columns[i]);
            }
            
            if (_total > total) {
                i = i - 1;
                var $lastTd = $td.get(i), $moreTd = $td.filter(':gt(' + i + ')');
                for (i = 0; i < $moreTd.length; i++) {
                    $moreTd.eq(i).children().appendTo($lastTd);
                }
                $moreTd.remove();
                this._$td = $td.parent().children();
            }
            else if (_total < total) {
                var html = []
                for (; i < total; i++) {
                    html.push('<td style="position:static;width:');
                    html.push(columns[i]);
                    html.push('"></td>');
                }
                this._$td = $td.parent().append(html.join('')).children();
            }
            this.afterrender();
        }
    },
    /**
     * 将当前状态序列化，
     * @return {JSON} 序列化结果
     */
    serialize: function(){
        var $td = this._$td, json = [];
        for (var i = 0; i < $td.length; i++) {
            var $portletsObj = $td.eq(i), column = {
                width: $portletsObj[0].style.width
            }, portlets = [];
            $portletsObj.children().each(function(){
                portlets.push({
                    id: this.id
                });
            });
            column.portlets = portlets;
            json.push(column);
        }
        return json;
    },
    destory: function(){
        delete this._$td;
    }
});
