Bonc.Portal.Portlet = Bonc.extend(Bonc.BaseComponent, {
    clazz: 'Bonc.Portal.Portlet',
    title: '', //默认标题
    width: '100%', //默认宽度
    height: 100, //默认高度
    top: 100,//默认位置
    url: null,//远程URL
    content: null,//内容
    iframe: false,//是否iframe嵌入
    dragable: true,//是否允许拖动
    resizable: true,//是否允许改变大小
    render: function(renderTo){
        this._$dom = jQuery(['<div id="' + this.id + '" class="portlet" style="width:' + this.width + 'px;">', 
	                             '<div class="portlet-handler" ', this.dragable === true ? ' style="cursor: move;"' : '', '>', 
	                            		 '<div class="portlet-title">', this.title, '</div>', 
	                            		 '<ul class="portlet-handler-btn"><li class="portlet-btn-minmax portlet-btn-minimize"><a>&nbsp;</a></li><li class="portlet-btn-close" title="关闭"><a>&nbsp;</a></li></ul>', 
	                            '</div>', 
	                            '<div class="portlet-content" style="height:' + this.height + 'px;">', this.content === null ? '' : this.content, '</div>', 
                            '</div>'].join('')).appendTo(this.target || 'body');
        this._rendercontent();
        this.afterrender();
    },
    _rendercontent: function(){
        if (this.url) {
            if (this.iframe == "iframe") {
                this._$dom.find('div.portlet-content').html('<iframe src="' + Bonc.toFullPath(this.url,false) + '" height="100%" width="100%" frameborder="0" marginheight="0" marginwidth="0"></iframe>');
            }
            else {
                this._$dom.find('div.portlet-content').load(Bonc.toFullPath(this.url,false));
            }
        }
    },
    afterrender: function(){
        var thiz = this, _$dom = this._$dom;
        _$dom.children('div:first').bgIframe({
            opacity: true
        });
        _$dom.delegate('.portlet-btn-close', 'click', function(e){
            thiz.destory();
        }).delegate('.portlet-btn-minmax', 'click', function(e){
            jQuery(this).toggleClass("portlet-btn-minimize").parents('.portlet:first').find('.portlet-content').toggle();
        });
    },
    destory: function(){
        this._$dom.remove();
        delete this.target;
        delete this._$dom;
        this.fireEvent('destory', this);
    }
});
