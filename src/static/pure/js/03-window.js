Bonc.Window=Bonc.extend(Bonc.BaseComponent, {
	clazz : 'Bonc.Window',
	title : '', //默认标题
	width : 600, //默认宽度
	height : 300, //默认高度
	top:100,//默认位置
	
	modal:true,//是否模态窗口
	url:null,//远程URL
	content:null,//内容
	iframe:false,//是否iframe嵌入
	
	dragable:true,//是否允许拖动
	resizable:true,//是否允许改变大小
	
	buttons:null,
	render : function() {
		this._$dom=jQuery([this.modal===true?'<div class="window-mask-layer"></div>':'',
			'<div id="' + this.id + '" class="sys-window" style="width:' + this.width + 'px;','top:' + this.top + 'px;','left:',(jQuery('body').width()-this.width)/2,'px">', 
				'<div class="sys-window-handler" ',this.dragable===true?' style="cursor: move;"':'','>', 
					'<div class="sys-window-title">', this.title, '</div>', 
					'<ul class="sys-window-handler-btn"><li class="sys-window-btn-close" title="关闭"><a>&nbsp;</a></li></ul>', 
				'</div>', 
				'<div class="sys-window-container" style="height:' + this.height + 'px;">',this.content===null?'':this.content,'</div>',
				'<div class="sys-window-buttons-wrap">',this.getButtonHTML(),'</div>', 
			'</div>'].join('')).appendTo('body');
		this._rendercontent();
		this.afterrender();
	},
	_rendercontent:function(){
		if(this.url){
			if(this.iframe===true){
				
			}else{
				this._$dom.find('div.sys-window-container').load(this.url);
			}
		}
	},
	getButtonHTML:function(){
		var buttons=this.buttons;
		if(buttons){
			//todo
		}
		return '&nbsp;'
	},
	afterrender : function() {
		var thiz = this,_$dom=this._$dom;
        _$dom.children('div:first').bgIframe({
            opacity: true
        });
		//绑定拖拽事件
		if(this.dragable===true){
            _$dom.drag(function(ev, dd){
                var $this = jQuery(this), $parent = $this.parent();
                //设置拖拽范围，fixed 容器有滚动条的情况
                dd.limit = {
                    bottom: $parent.height() - $this.outerHeight(),
                    right: $parent.width() - $this.outerWidth()
                };
            }, {
                handle: ".sys-window-handler",
                not: 'a',
                click: true
            }).drag(function(ev, dd){
                //拖拽中
                jQuery(this).css({
                    top: Math.min(dd.limit.bottom, Math.max(0, dd.offsetY)),
                    left: Math.min(dd.limit.right, Math.max(0, dd.offsetX))
                });
            });
		}
		_$dom.delegate('.sys-window-btn-close', 'click', function(e) {
			//绑定按钮点击事件
			thiz.destory();
		});
	},
	destory : function() {
		this._$dom.remove();
		delete this._$dom;
	}
});