/*页面提示信息*/
div.message-box {
	margin: 0 auto;
	margin-top: 60px;
	padding: 10px;
	width: 400px;
	border: 1px solid #bbb;
	-webkit-box-shadow: 5px 5px 5px #ddd;
	-moz-box-shadow: 5px 5px 5px #ddd;
	box-shadow: 5px 5px 5px #ddd;
	-moz-border-radius: 8px;
	-khtml-border-radius: 8px;
	-webkit-border-radius: 8px;
	border-radius: 8px;
	text-shadow: 2px 2px 2px #ccc;
}

/*页面提示大图标*/
.icon-big-warn,.icon-big-error,.icon-big-info {
	padding-left: 60px !important;
	background: transparent url(images/icon-warning.gif) no-repeat scroll
		10px center;
}
/* warn */
.icon-big-warn {
	background-image: url(images/icon-warning.gif);
}

/* error */
.icon-big-error {
	background-image: url(images/icon-error.gif)
}

.icon-big-info {
	background-image: url(images/icon-info.gif)
}

/*操作错误提示信息*/
.actionMessage,.errorMessage,.warnMessage {
	margin: 4px;
}

.errorMessage span,.actionMessage span,.warnMessage span {
	padding-left: 18px;
	background: url(images/ok.gif) no-repeat scroll left center transparent;
	color: #71C167;
}

.warnMessage span {
	background-image: url(images/warning.gif);
	color: #e0c210;
}

.errorMessage span {
	color: #F1A599;
	background-image: url(images/exclamation.gif)
}
/**排序*/
.sort {
	background: url(images/icon_sort.gif);
	height: 15px;
	width: 15px;
}

.sortAsc {
	background-image: url(images/icon_sort_up.gif)
}

.sortDesc {
	background-image: url(images/icon_sort_down.gif)
}

/*操作区域*/
.operation-container {
	height: 30px;
	text-align: right;
	line-height: 30px;
	margin-right: 18px;
}

.operation-container a {
	margin: 0 4px;
}

/**操作图标*/
.icon-add,.icon-save,.icon-info,.icon-status-offline,.icon-status-online,.icon-vcard,.icon-vcard-edit,.icon-vcard-add,.icon-vcard-delete,.icon-vcard-role
	{
	font-size: 12px;
	padding: 3px 0 3px 20px;
	background: url(images/vcard.jpg) no-repeat scroll left center
		transparent;
}

.icon-status-online {
	background-image: url(images/status_online.png);
	_background-image: url(images/status_online.jpg);
}

.icon-status-offline {
	background-image: url(images/status_offline.png);
	_background-image: url(images/status_offline.jpg);
}

/**共用*/
.icon-add {
	background-image: url(images/add.png);
	_background-image: url(images/add.jpg);
}

.icon-save {
	background-image: url(images/save.gif);
}
/*表单提示*/
.icon-info {
	background-image: url(images/information.png);
	_background-image: url(images/information.jpg);
}

/**用户管理*/
.icon-vcard-edit {
	background-image: url(images/vcard_edit.png);
	_background-image: url(images/vcard_edit.jpg);
}

.icon-vcard-add {
	background-image: url(images/vcard_add.png);
	_background-image: url(images/vcard_add.jpg);
}

.icon-vcard-delete {
	background-image: url(images/vcard_delete.png);
	_background-image: url(images/vcard_delete.jpg);
}

.icon-vcard-role {
	background-image: url(images/group_key.png);
	_background-image: url(images/group_key.jpg);
}

img.datetimepicker {
    background-image: url(images/calendar.gif);
}
/*资源树节点*/
.node-group {
	background-image: url(images/group.png) !important;
	_background-image: url(images/group.jpg) !important;
}

/*资源树节点*/
.resources-id {
	background-image: url(images/resources/brick.png) !important;
	_background-image: url(images/resources/brick.gif) !important;
}

.resources-id-1 {
	background-image: url(images/resources/page_white.png) !important;
	_background-image: url(images/resources/page_white.gif) !important;
}

.resources-id-2 {
	background-image: url(images/resources/tab.png) !important;
	_background-image: url(images/resources/tab.gif) !important;
}

.resources-id-3 {
	background-image: url(images/resources/link.png) !important;
	_background-image: url(images/resources/link.gif) !important;
}

.resources-id-4 {
	background-image: url(images/resources/anchor.png) !important;
	_background-image: url(images/resources/anchor.gif) !important;
}

.resources-id-5 {
	background-image: url(images/resources/server.png) !important;
	_background-image: url(images/resources/server.gif) !important;
}

.resources-id-99 {
	background-image: url(images/resources/page_white_text.png) !important;
	_background-image: url(images/resources/page_white_text.gif) !important;
}

.resources-root {
	background-image: url(images/resources/application_home.png) !important;
	_background-image: url(images/resources/application_home.gif) !important;
}
.notViewed {
	background-image: url("images/notViewed.png") !important;
}
.msg-edit {
	background: url("images/message-edit.png") no-repeat scroll left center transparent !important;
	padding-left:20px;
}
.msg-receive {
	background: url("images/message-accept.png") no-repeat scroll left center transparent !important;
	padding-left:20px;
}
.msg-send {
	background: url("images/message-send.png") no-repeat scroll left center transparent !important;
	padding-left:20px;
}