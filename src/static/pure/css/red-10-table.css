/*
* data grid 用在数据集的表格显示中 
*/
table.grid {
	border-collapse: collapse;
	width: 100%;
}

table.grid thead,table.grid thead th {
	border-bottom: 2px solid #D61821;
	font-weight: bold;
	padding: 3px 0;
	font-size: 12px;
	text-align: center;
}

table.grid tbody td,table.grid tbody tr {
	border-bottom: 1px solid #ccc;
}

table.grid td {
	padding: 3px;
	font-size: 12px;
}

table.grid tbody tr .operation-data-row a {
	visibility:hidden;
	filter:alpha(opacity=0);
	-moz-opacity: 0;
	opacity: 0;
}

table.grid tbody tr:hover .operation-data-row a {
	visibility:visible;
	filter:alpha(opacity=80);
	-moz-opacity: 0.8;
	opacity: 0.8;
}

table.grid .grid-row-number {
	color: #828282;
}

table.grid .odd {
	background-color: #FFFFCC;
}

table.grid .string,table.grid .left {
	text-align: left;
}

table.grid .number,table.grid .right {
	text-align: right;
}

table.grid .date,table.grid .center {
	text-align: center;
}

table.grid td a {
	marggin: 0 3px;
}

td.postTd{
	background: url("images/menu/red.gif") no-repeat left center transparent;
}