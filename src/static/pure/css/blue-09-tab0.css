/*************选项卡***************/
div.tab-page-tab0-head {
	height: 27px!important;
	padding: 0 5px !important;
}

div.tab-page-tab0-head div.item-wrapper {
	height: 25px;
	overflow: hidden;
}

div.tab-page-tab0-head span.tab0-item {
	height: 27px!important;
}

div.tab-page-tab0-head span.tab0-item-2 {
	color: #666666; 
	line-height: 25px;
}

div.tab-page-tab0-head span.item-editable span.tab0-item-2 {
    padding: 0 20px 0 5px;
}

div.tab-page-tab0-head span.tab0-item {
    background: url("images/tab0/card_unsel_l.gif") no-repeat left top;
    padding-left: 4px;
}

div.tab-page-tab0-head span.tab0-item-1 {
    background: url("images/tab0/card_unsel_r.gif") no-repeat right top;
    padding-right: 4px;
    height: 25px;
    line-height: 25px;
}

div.tab-page-tab0-head span.tab0-item-3 {
    background: url("images/tab0/card_unsel_c.gif") repeat-x right top;
    height: 26px;
    line-height: 26px;
    display: inline-block;
    position: relative;
}

div.tab-page-tab0-head span.tab0-item-2 {
    padding: 0 12px 0 2px;
    background-image: none;
    line-height: 25px;
    height: 25px;
}

div.tab-page-tab0-head span.item-hover {
    background-image: url("images/tab0/blue_card_sel_l.gif");
}

div.tab-page-tab0-head span.tab0-item span.tab0-title {
	line-height: 25px;
	min-width: 50px;
	_width : 50px;
	padding: 0;
}

div.tab-page-tab0-head span.item-hover span.tab0-item-1 {
    background-image: url("images/tab0/blue_card_sel_r.gif");
}

div.tab-page-tab0-head span.item-hover span.tab0-item-3 {
    background-image: url("images/tab0/blue_card_sel_c.gif");
}

div.tab-page-tab0-head span.item-selected {
    background-image: url("images/tab0/blue_card_sel_l.gif");
    cursor: default;
    background-color: transparent;
}

div.tab-page-tab0-head span.item-selected span.tab0-item-1 {
    background-image: url("images/tab0/blue_card_sel_r.gif");
}

div.tab-page-tab0-head span.item-selected span.tab0-item-3 {
    background-image: url("images/tab0/blue_card_sel_c.gif");
}

div.tab-page-tab0-head span.item-selected span.tab0-title {
    color: #000000;
}

td.tab-page-tab0-cb-lt img {
    height: 1px;
    width: 0;
    background-color: #BCD2E6;
    background-image: none;
}

td.tab-page-tab0-cb-ct {
	background-color: #BCD2E6;
	background-image: none;
}

td.tab-page-tab0-cb-rt img {
    height: 1px;
    width: 0;
    background-color: #BCD2E6;
    background-image: none;
}

td.tab-page-tab0-cb-lc {
    background: none;
}

td.tab-page-tab0-cb-rc {
    background: none;
}

td.tab-page-tab0-cb-lb img {
    background: none;
    height: 0;
    width: 0;
}

td.tab-page-tab0-cb-cb {
    background: none;
}

td.tab-page-tab0-cb-rb img {
    background: none;
    height: 0;
    width: 0;
}

div.tab-page-tab0-head span.item-remove {
    background: url("images/tab0/close_disactive.gif") no-repeat;
    display: none;
    height: 14px;
    right: 2px;
    top: 5px;
    width: 14px;
    line-height: 14px;
}

div.tab-page-tab0-head span.item-refresh {
    background: url("images/tab0/refresh.gif") no-repeat;
    display: none;
    height: 14px;
    right: 20px;
    top: 5px;
    width: 14px;
    line-height: 14px;
    position: absolute;
}

div.tab-page-tab0-head span.item-hover span.item-remove, div.tab-page-tab0-head span.item-selected span.tab0-item-1 span.item-remove {
	display: inline-block;
}

div.tab-page-tab0-head span.item-selected span.tab0-item-1 span.item-refresh {
	display: inline-block;
}

div.tab-page-tab0-head div.item-unremovable span.item-remove {
	display: none!important;
}

div.tab-page-tab0-head div.item-unremovable span.item-refresh {
    right: 2px!important;
}

div.tab-page-tab0-head span.tab0-item span.refresh-hover {
	background-image: url("images/tab0/blue_refresh_active.gif");
	cursor: pointer;
}

div.tab-page-tab0-head span.item-selected span.tab0-item-1 span.tab0-item-2 {
	padding: 0 38px 0 5px;;
}

div.tab-page-tab0-head div.item-unremovable span.tab0-item-2 {
	padding: 0 20px 0 5px!important;
}

div.tab-page-tab0-head span.tab0-item span.remove-hover {
    background-image: url("images/tab0/blue_close_active.gif");
    cursor: pointer;
}

span.advanced-span {
	white-space: nowrap;
	display: inline-block;
}

span.advanced-span span {
	display: inline-block;
	vertical-align: top;
}

span.advanced-span span.span-text-wrapper {
	overflow: hidden;
}