/**登录后body样式**/
.pure-frame-body {
	margin: 0 auto;
	font-family: "宋体";
	font-size: 8.75pt;
	background-color: #fff;
	padding: 0 3px;
	min-width: 960px;
}

/*auto*/
#pure-frame-auto .pure-frame-body {
	width: auto;
}

/*1280*/
#pure-frame-1280  .pure-frame-body {
	width: 1280px;
}
/*1024*/
#pure-frame-1024 .pure-frame-body {
	width: 980px;/*wzg modify 为了屏蔽滚动条*/
}
/**
 *首页顶部
 **/
#pure-frame-top {
	height: 45px;
	position: relative;
}

/*首页顶部logo*/
#pure-frame-top-logo {
	width: 450px;
	height: 45px;
	position: absolute;
	top: 0;
	left: 0;
}

/*首页顶部标题*/
#pure-frame-top-title {
	width: 0;
}

/**系统菜单*/
#pure-sys-tool {
	list-style-type: none;
	bottom: 2px;
	height: 24px;
	position: absolute;
	right: 10px;
	/*width: 120px;*/
}

#pure-sys-tool li {
	color: #0F3C5C;
	float: left;
	overflow: hidden;
	padding: 0px 6px;
	text-decoration: none;
	border-right: 1px solid #994747;
}

#pure-sys-tool li.last {
	border: none;
}

#pure-sys-tool a,#pure-sys-tool span {
	display: inline-block;
	height: 16px;
	line-height: 16px;
}

#pure-sys-tool a {
	cursor: pointer;
}

/*首页公告*/
#sys-tool-post {
	background: url(images/tool/post-icon.gif) no-repeat scroll left center transparent;
	padding-left: 18px;
}
/*首页顶部 菜单栏*/
#nav-menu-bar {
    /*height: 25px;*/
    z-index: 99;
    overflow: visible;
    position: relative;
}

/*首页顶部 菜单栏 时间*/
#nav-menu-bar-datetime {
    height: 25px;
    line-height: 25px;
    padding-left: 13px;
    float: left;
    color: #fff;
    font-weight: normal;
    width: 200px;
    vertical-align: top;
}

/*首页顶部 菜单栏 菜单*/
#nav-menu-bar-menu {
    float: left;
    font-size: 13px;
    height: 25px;
    overflow-x: hidden;
    white-space: nowrap;
    width: 801px;
}

/*首页顶部 菜单栏  菜单内容*/
#nav-menu-bar-menu-content {
    height: 25px;
    font-size: 13px;
    float: left;
    width: 770px;
    white-space: nowrap;
    overflow-x: hidden;
}

/*首页顶部 菜单栏  左右滚动*/
#nav-menu-bar-menu-scroll {
    float: right;
    width: 30px;
    line-height: 25px;
    height: 25px;
}

/*首页顶部 菜单栏  左右滚动图标，使用Border切片出小三角形*/
#nav-menu-bar-menu-scroll span {
    display: inline-block;
    border: 0 solid #2a7de2;
    border-width: 7px;
    border-color: #2a7de2 #fff;
    background-color: #fff;
    overflow: hidden;
    cursor: pointer;
    height: 0;
    width: 0;
    margin: 5px 0px;
}

#nav-menu-bar-menu-scroll span.left {
    margin-right: 3px;
    border-left-width: 0;
}

#nav-menu-bar-menu-scroll span.right {
    border-right-width: 0;
}

/*有滚动条*/
.withscroll div.scroll {
    visibility: visible;
    display: inline-block;
}

.withscroll div.content {
}

/*无滚动条*/
.noscroll div.scroll {
    visibility: hidden;
    display: none;
}

.noscroll div.content {
}

/*
 *首页顶部 当前位置导航
 */
#nav-location {
    color: gray;
    height: 25px;
    line-height: 25px;
    padding-left: 20px;
    margin-left: 7px;
}

/*框架*/
#frame_011 {
	overflow: hidden;
	position: absolute;
	width: 900px;
	height: 200px;
}

.horizon-menu {
	position: relative;
}

div.horizon-menu-content {
	float: left;
	position: relative;
}

/*一级水平菜单*/
div.horizon-lev1 {
	height: 25px;
	cursor: default;
	float: left;
	vertical-align: top;
	overflow: visible;
}

div.horizon-lev1 span.menu-item {
	vertical-align: top;
	cursor: pointer;
}

div.horizon-lev1 span.menu-item-2 {
	line-height: 24px;
	color: #FFFFFF;
	padding: 0 10px;
	font-weight: bold;
	display: inline-block;
	height: 24px;
	overflow: hidden;
	margin: 1px 1px 0 1px;
}

div.horizon-lev1 span.menu-item-selected span.menu-item-2 {
	background-color: #F1F1F1;
	color: #000000;
	cursor: default;
}

div.horizon-lev1 span.menu-item-hover span.menu-item-2 {
	background-color: #F1F1F1;
	color: #000000;
}

/*左边树*/
.left-menu-area {
	background-color: #F6F6F6;
	width: 1px;
}

div.left-menu {
	overflow: auto;
	position: relative;
	width: 170px;
	display: none;
	padding: 10px 5px;
}

.op-area {
	display: none;
	background-color: #F6F6F6;
	background-image: url("images/menu/op_bg.gif");
	background-repeat: repeat-y;
	background-position: right;
}

.show-sub-menu .left-menu {
	display: block;
}

.show-sub-menu .op-area {
	display: block;
}

div.treemenu-uncollapse .op-area {
	display: none!important;
}

.show-sub-menu td.op-area {
	display: table-cell;
}

#left_menu_01 .jstree li {
	line-height: 22px;
}

/*左边树的折叠按钮*/
span.op-left-menu {
	display: inline-block;
	width: 6px;
	height: 48px;
	background: url("images/menu/left2.gif") no-repeat center;
}

.op-area span.op-left-menu-hover {
	background-image: url("images/menu/left1.gif");
}

.left-menu-collapse span.op-left-menu {
	background-image: url("images/menu/right2.gif");
}

div.left-menu-collapse .left-menu {
	display: none;
}

div.left-menu-collapse span.op-left-menu-hover {
	background-image: url("images/menu/right1.gif");
}

/*滚动条*/
div.scroll-menu-content {
	white-space: nowrap;
	overflow: hidden;
	float: left;
	height: 25px;
}

div.menu-scroll {
    float: left;
    width: 30px;
    line-height: 25px;
    height: 25px;
    margin-left: 8px;
    visibility: hidden;
    display: none;
}

/*首页顶部 菜单栏  左右滚动图标，使用Border切片出小三角形*/
div.menu-scroll span {
    display: inline-block;
    border: 0 solid #2a7de2;
    border-width: 7px;
    border-color: #2a7de2 #fff;
    background-color: #fff;
    overflow: hidden;
    cursor: pointer;
    height: 0;
    width: 0;
    margin: 5px 0px;
}

div.menu-scroll span.left {
    margin-right: 3px;
    border-left-width: 0;
}

div.menu-scroll span.right {
    border-right-width: 0;
}

/*有滚动条*/
.withscroll div.menu-scroll {
    visibility: visible;
    display: inline-block;
}

/*无滚动条*/
.noscroll div.menu-scroll {
    visibility: hidden;
    display: none;
}

div.horizon-lev1 div.menu-scroll span {
	border-color: #8C0001 #FFFFFF;
}

div.horizon-lev2 div.menu-scroll span {
	border-color: #F1F1F1 #8C0001;
}