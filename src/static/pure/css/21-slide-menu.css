/*****************顶级菜单*****************/
.slide-menu ul {
	z-index: 7100;
	list-style-type: none;
}

.slide-menu ul li {
	display: inline;
}
.slide-menu ul li a {
	display: inline-block;
	color: #fff;
	font-weight: bold;
	margin: 1px 1px 0 1px;
	line-height: 24px;
	height: 24px;
	padding: 0 10px;
	text-decoration: none;
	padding: 0 10px;
}

* html .slide-menu ul li a {
	/*IE6 hack to get sub menu links to behave correctly*/
	display: inline-block;
}

.slide-menu ul li a:link,.slide-menu ul li a:visited {
	color: #fff;
}

.slide-menu ul li a:hover,.slide-menu ul li a.selected {
	background-color: #FCFCFC;
	color: #000 !important;
}

.slide-menu ul li a.lvl0-expanded {
	-moz-border-radius: 4px 4px 0 0;
	-khtml-border-radius: 4px 4px 0 0;
	-webkit-border-radius: 4px 4px 0 0;
	border-radius: 4px 4px 0 0;
	border-bottom: 1px solid #fff;
}

/*****************1级子菜单*****************/
.slide-menu ul li ul {
	position: absolute;
	left: 0;
	top: 0;
	background-color: #FCFCFC;
	-webkit-box-shadow: 1px 1px 0px rgba(196, 172, 172, 0.3);
	-moz-box-shadow: 1px 1px 0px rgba(196, 172, 172, 0.3);
	box-shadow: 1px 1px 0px rgba(196, 172, 172, 0.3);
	-moz-border-radius: 0 0 4px 4px;
	-khtml-border-radius: 0 0 4px 4px;
	-webkit-border-radius: 0 0 4px 4px;
	border-radius: 0 0 4px 4px;
	filter:alpha(opacity=95);
	-moz-opacity: 0.95;
	opacity: 0.95
}

.slide-menu ul li ul li {
	position: relative;
	display: list-item;
}

.slide-menu ul li ul li.first {
	border-top: none;
}

.slide-menu ul li ul a.last {
	-moz-border-radius-bottomleft: 4px;
	-moz-border-radius-bottomright: 4px;
	-khtml-border-bottom-left-radius: 4px;
	-khtml-bottom-right-border-radius: 4px;
	-webkit-border-bottom-left-radius: 4px;
	-webkit-bottom-right-border-radius: 4px;
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
}

.slide-menu ul li ul li a {
	font-weight: normal;
	width: 100px;
	padding: 1px 20px;
	margin: 0;
	color: #000;
	white-space: nowrap;
}

.slide-menu ul li ul li.leaf a {
	width: auto;
}

/*具有子菜单的子菜单，在右边展示一个小箭头*/
.slide-menu ul li ul li a.collapsed {
	background: url(images/menu/blue.gif) no-repeat right center;
}

/*具有子菜单的子菜单，鼠标划过时切换图片*/
.slide-menu ul li ul li a.collapsed:hover {
	background-image: url(images/menu/white.gif);
}

.slide-menu ul li ul li a:link,.slide-menu ul li ul li a:visited
	{
	color: #000;
}

.slide-menu ul li ul li a:hover {
	background-color: #8B0000;
	color: #fff !important;
}

/* Holly Hack for IE \*/
* html .slide-menu {
	height: 1%;
}

/*****************2级及2级以上的子菜单*****************/
.slide-menu ul li ul li ul {
	border: 1px solid #8B0000;
}

.slide-menu ul li ul li ul li.first {
	border-top: 0;
}