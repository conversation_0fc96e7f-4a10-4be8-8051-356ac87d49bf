span.input-tip {
	color: grey;
	margin: 0 3px;
	filter:alpha(opacity=50);
	-moz-opacity: 0.5;
	opacity: 0.5
}
/*
 @title:鼠标手势样式
 @author:lius<PERSON>in
 @description:鼠标手势样式
 @time:2010-03-26
 @version:1.0
 */
.mouse-hand {
	cursor: pointer;
}

/*
 @title:右浮动样式
 @author:lius<PERSON>in
 @description:
 @time:2010-03-26
 @version:1.0
 */
.float-right {
	float: right;
}

/*
 @title:左浮动样式
 @author:lius<PERSON>in
 @description:
 @time:2010-03-26
 @version:1.0
 */
.float-left {
	float: left;
}

/*
 @title:清除浮动
 @author:lius<PERSON>in
 @description:清除浮动
 @time:2010-03-26
 @version:1.0
 */
.float-clear {
	clear: both;
	border: none;
	height: 0;
	font-size: 0;
	display: block;
}

/*文本位置*/
.text-left {
	text-align: left;
}

.text-right {
	text-align: right;
}

.text-center {
	text-align: center;
}

.appletBox {
	border: none;
}

.appletBox .appletTitleBar {
	background-image: none;
	border-top:none;
}

table.form td {
    border: none;
}