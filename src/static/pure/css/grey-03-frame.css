/**
 *首页顶部
 **/
#pure-frame-top {
	background: url(images/grey/logo-bg.gif) repeat-x scroll 0 0 transparent;
    height: 65px;
}

/*首页顶部logo*/
#pure-frame-top-logo {
	background: url(images/grey/logo.jpg) no-repeat !important;
	height: 65px;
}

/*首页顶部 菜单栏*/
#nav-menu-bar {
	background: url("images/grey/menu-bj.gif") repeat-x scroll 0 0 transparent;
    font-size: 13px;
    height: 39px;
}

/*首页顶部 菜单栏 时间*/
#nav-menu-bar-datetime {
    height: 39px;
    line-height: 39px;
    color: #444444;
}

/*一级水平菜单*/
div.horizon-lev1 {
	height: 39px;
	background: url("images/grey/menu-bj.gif") repeat-x scroll 0 0 transparent;
}

div.horizon-lev1 span.menu-item {
	height: 39px;
	vertical-align: top;
}

div.horizon-lev1 span.menu-item-2 {
	line-height: 39px;
	color: #444444;
	padding: 0 10px;
	font-weight: bold;
	display: inline-block;
	height: 39px;
	overflow: hidden;
	margin:1px 1px 0 1px;
}

div.horizon-lev1 span.menu-item-selected span.menu-item-2, div.horizon-lev1 span.menu-item-hover span.menu-item-2 {
	background: url("images/grey/navi-bg.png") repeat-x scroll 0 0 transparent;
	color: #444444;
}

/*二级水平菜单*/
div.horizon-lev2 {
	height: 25px;
	background-color: #F1F1F1;
	cursor: default;
	border-bottom: #8c0001 solid 1px; 
}

div.scroll-menu-content{
	height:39px;
}

div.horizon-lev2 span.menu-item {
	vertical-align: top;
}

div.horizon-lev2 span.menu-item-2 {
	line-height: 25px;
	color: #656565;
	padding: 0 10px;
	vertical-align: top;
	display: inline-block;
}

div.horizon-lev2 span.menu-item-selected span.menu-item-2 {
	color: #000000;
	font-weight: bold;
	background: url("images/red/menu_lev2_selected_arrow.gif") no-repeat bottom center;
}