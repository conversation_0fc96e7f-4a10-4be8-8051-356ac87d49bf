/*选项卡tab0*/
div.tab0 {
	position: relative;
}

div.tab0-head {
	position: relative;
	z-index: 11;
	top: 3px;
	cursor: default;
}

table.tab0-tb {
	z-index: 1;
}

div.tab0-card-content {
	overflow: hidden;
	position: relative;
}

div.tab0-head div.item-wrapper {
	display: inline-block;
	margin: 0;
	padding: 0;
	white-space: nowrap;
	float: left;
	overflow: hidden;
}

div.tab0-head span.item-container {
	display: inline-block;
	white-space: nowrap;
	position: relative;
}

div.tab0-head span.tab0-item {
	margin: 0 2px 0 0;
	padding: 0;
	display: inline-block;
	height: 31px;
	line-height: 100%;
	vertical-align: top;
}

span.tab0-item span.tab0-item-1 {
	display: inline-block;
	height: 31px;
	line-height: 100%;
}

span.tab0-item span.tab0-item-2 {
	display: inline-block;
	height: 31px;
	line-height: 31px;
	padding: 0 0 0 12px;
	color: #3366CC;
	position: relative;
}

div.tab0-head span.item-last {
	margin: 0;
}

/*标签标题*/
div.tab0-head span.tab0-title {
	padding-right: 8px;
	display: inline-block;
	vertical-align : top;
}

/*选项卡选中*/
div.tab0-head span.item-selected {
	cursor: default;
}

div.tab0-head span.item-selected span.tab0-item-2 {
	color: #000000;
}

div.tab0-head span.item-editable span.tab0-item-2 {
	padding: 0 18px 0 12px;
}

/*标签移出按钮*/
div.tab0-head span.item-remove {
	vertical-align: top;
}

div.tab0-head span.item-remove {
	display: inline-block;
	position: absolute;
	right: 4px;
	top: 0;
}

/*选项卡内容区*/
div.tab0-content {
	margin: auto;
	padding: 0;
	clear: both;
	position: relative;
	float: left;
	overflow: hidden;
}