/*首页顶部logo*/
#pure-frame-top-logo {
	background: url(images/blue/logo.gif) no-repeat;
}

/*****************首页顶部 菜单栏*****************/
#nav-menu-bar,#nav-menu-bar .navMenu {
	background-color: #2a7de2;
}

#nav-table-border {
	border-top: 2px solid #1C86EE;
}

.navMenu .scroll span {
	border-color: #2a7de2 #fff;
	background-color: #fff;
}

/*****************1级子菜单*****************/
.slide-menu ul li ul {
	border: 1px solid #2a7de2;
	border-top: 0;
}

.slide-menu ul li ul li {
	border-top: 1px solid #d7e9fe;
}

/*具有子菜单的子菜单，在右边展示一个小箭头*/
.slide-menu ul li ul li a.collapsed {
	background: #fff url(images/blue/blue.gif) no-repeat right center;
}
/*具有子菜单的子菜单，鼠标划过时切换图片*/
.slide-menu ul li ul li a.collapsed:hover {
	background: #2a7de2 url(images/blue/white.gif) no-repeat right center;
}

.slide-menu ul li ul li a:hover {
	background-color: #2a7de2;
	color: #fff;
}

/*****************2级及2级以上的子菜单*****************/
.slide-menu ul li ul li ul {
	border: 1px solid #2a7de2;
}

#nav-location {
	background: url(images/blue/nav-location.gif) no-repeat left center;
}

.appletBox .appletTitle {
	background-image: url(images/blue/redbar.gif)
}

.appletBox .appletTitleBar {
	border-color: #1C86EE;
}

/*************二级水平菜单***************/
div.horizon-lev2 {
	height: 25px;
	background-color: #F1F1F1;
	cursor: default;
}

div.horizon-lev2 span.menu-item {
	vertical-align: top;
}

div.horizon-lev2 span.menu-item-2 {
	line-height: 24px;
	color: #656565;
	padding: 0 10px;
	vertical-align: top;
	display: inline-block;
	margin-top: 1px;
}

div.horizon-lev2 span.menu-item-selected span.menu-item-2 {
	color: #000000;
	font-weight: bold;
	background-color: #FFFFFF;
}

/*************三级水平菜单***************/
div.horizon-lev3 {
	height: 25px;
	background-color: #FFFFFF;
	cursor: default;
}

div.horizon-lev3 span.menu-item {
	vertical-align: top;
}

div.horizon-lev3 span.menu-item-2 {
	line-height: 23px;
	color: #656565;
	padding: 0 10px;
	vertical-align: top;
	display: inline-block;
	margin-top: 2px;
}

div.horizon-lev3 span.menu-item-selected span.menu-item-2 {
	color: #000000;
	font-weight: bold;
}

/*************最后级水平菜单***************/
#frame_011 div.horizon-last-lev {
	border-bottom: #2A7DE2 solid 1px; 
}

#frame_011 div.horizon-last-lev span.menu-item-selected span.menu-item-2 {
	background: url("images/blue/menu_lev2_selected_arrow.gif") no-repeat bottom center;
}