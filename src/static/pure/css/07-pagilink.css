div.paglink-container {
	text-align: right;
	margin: 3px 10px;
}

div.paglink-container a,div.paglink-container span {
	background: none no-repeat scroll left center transparent;
	padding-left: 16px;
}

div.paglink-container span.text {
	padding-left: 0px;
}

div.paglink-container span.split {
	padding: 0px;
	color: #e2e2e2;
	margin: 0 3px;
}

div.paglink-container input {
	border: none;
	border-bottom: 1px solid #bbb;
}
/*首页*/
div.paglink-container .first {
	background-image: url(images/pagi/page-first.gif)
}

div.paglink-container .first-disabled {
	background-image: url(images/pagi/page-first-disabled.gif)
}
/*尾页*/
div.paglink-container .last {
	background-image: url(images/pagi/page-last.gif)
}

div.paglink-container .last-disabled {
	background-image: url(images/pagi/page-last-disabled.gif)
}
/*前一页*/
div.paglink-container .prev {
	background-image: url(images/pagi/page-prev.gif)
}

div.paglink-container .prev-disabled {
	background-image: url(images/pagi/page-prev-disabled.gif)
}
/*后一页*/
div.paglink-container .next {
	background-image: url(images/pagi/page-next.gif)
}

div.paglink-container .next-disabled {
	background-image: url(images/pagi/page-next-disabled.gif)
}

div.paglink-container .refresh {
	background-image: url(images/pagi/refresh.gif)
}

div.paglink-container .refresh-disabled {
	background-image: url(images/pagi/refresh-disabled.gif)
}