/*背景遮罩*/
.window-mask-layer {
    background-color: #fff;
    height: 100%;
    left: 0;
    opacity: 0.6;
    filter: alpha(opacity = 60);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 100;
}

/*window*/
.sys-window {
    position: fixed;
    border: 1px solid #616161;
    -webkit-box-shadow: 0 0 5px #acacac;
    -moz-box-shadow: 0 0 5px #acacac;
    box-shadow: 0 0 5px #acacac;
    -moz-border-radius: 8px;
    -khtml-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
    min-width: 100px;
    height: auto;
    background-color: #fff;
    z-index: 101;
}

/*拖拽句柄*/
.sys-window-handler {
    width: 100%;
    background-color: #e4e4e4;
    -moz-border-radius: 8px 8px 0 0;
    -khtml-border-radius: 8px 8px 0 0;
    -webkit-border-radius: 8px 8px 0 0;
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid #aaa;
    height: 30px;
    line-height: 30px;
    color: #000;
}

.sys-window-title {
    position: absolute;
    font-weight: bolder;
    left: 16px;
    right: 0;
}

.sys-window-container {
    overflow: auto;
    padding: 18px 30px;
}

.sys-window-handler-btn {
    position: absolute;
    right: 10px;
    top: 0;
    cursor: pointer;
}

.sys-window-handler-btn li {
    display: inline;
    margin: 0 3px;
}

.sys-window-handler-btn li a {
    display: inline-block;
    width: 62px;
}

.sys-window-handler-btn li.sys-window-btn-close a {
    background: url(images/window-close.png) no-repeat scroll center -1px transparent;
}

.sys-window-handler-btn li.sys-window-btn-close a:hover {
    background: url(images/window-close-hover.png) no-repeat scroll center -8px transparent;
    text-decoration: none;
}

.sys-window .sys-window-buttons-wrap {
    background-color: #F6F6F6;
    border-bottom: 1px solid #CCCCCC;
    border-top: 1px solid #EBEBEB;
    -moz-border-radius: 0 0 8px 8px;
    -khtml-border-radius: 0 0 8px 8px;
    -webkit-border-radius: 0 0 8px 8px;
    text-align: right;
    padding: 10px;
    padding-right: 20px;
    -webkit-box-shadow: 0 -2px 2px rgba(204, 204, 204, 0.3) inset;
    -moz-box-shadow: 0 -2px 2px rgba(204, 204, 204, 0.3) inset;
    box-shadow: 0 -2px 2px rgba(204, 204, 204, 0.3) inset;
}

.sys-window .sys-window-buttons-wrap input {
    margin: 0 10px;
    background: url(images/buttons.png) no-repeat scroll 0 -50px transparent;
    border: medium none;
    cursor: pointer;
    height: 25px;
    text-align: center;
    width: 80px;
}