/*登录页面背景图*/
#pure-login-column #login {
	background: transparent url(images/red/loginbackground.gif) no-repeat;
	width: 654px;
	height: 386px;
	position: absolute;
	top: 50%;
	left: 50%;
	margin-left: -327px;
	margin-top: -233px;
}

/*登录页面表单*/
#pure-login-column #login form {
	position: absolute;
	top: 50%;
	left: 50%;
	margin-top: -50px;
	margin-left: -70px;
	width: 400px;
}

/*登录页面表单label*/
#pure-login-column #login form label {
	width: 50px;
	display: inline-block;
	color: #777;
	padding: 5px 0;
}

/*登录页面表单input*/
#pure-login-column #login form input {
	height: 16px;
	border: 1px solid #A3A3A3;
	background: none repeat scroll 0 0 #FFD;
	padding: 3px;
}

#pure-login-column #login  form .row {
	width: 240px;
	padding: 3px;
}

#pure-login-column #login  form .btn-row {
	position: absolute;
	right: 60px;
	top: 30px;
	width: 80px;
}

#pure-login-column #login form .btn-submit {
	background: transparent url(images/login-button.gif) no-repeat;
	width: 79px;
	height: 29px;
	cursor: pointer;
	border: 0;
}

#pure-login-column #login form .btn-cancel {
	display: none;
}

/*登录页面错误提示*/
#pure-login-column #login .errors {
	height: 20px;
	color: red;
}