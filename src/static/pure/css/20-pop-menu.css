/*****************顶级菜单*****************/
.pop-menu ul {
	z-index: 7100;
	list-style-type: none;
}

.pop-menu ul li {
	display: inline;
}

.pop-menu ul li a {
	display: inline-block;
	color: #fff;
	font-weight: bold;
	margin: 0px;
	line-height: 24px;
	height: 24px;
	padding: 0 10px;
	text-decoration: none;
	border: 1px solid #8B0000;
	border-bottom: 0px;
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
}

* html .pop-menu ul li a {
	/*IE6 hack to get sub menu links to behave correctly*/
	display: inline-block;
}

.pop-menu ul li a:link,.pop-menu ul li a:visited {
	color: #fff;
}

.pop-menu ul li:hover a   ,.pop-menu ul li a.selected {
	background-color: #fff !important;
	color: #000 !important;
}

/*****************子菜单*****************/
.pop-menu ul li div {
	display: none;
	position: absolute;
	visibility: hidde;
	background-color: #FFFFFF;
	-webkit-box-shadow: 1px 1px 0px rgba(196, 172, 172, 0.3);
	-moz-box-shadow: 1px 1px 0px rgba(196, 172, 172, 0.3);
	box-shadow: 1px 1px 0px rgba(196, 172, 172, 0.3);
	-moz-border-radius: 0 4px 4px 4px;
	-khtml-border-radius: 0 4px 4px 4px;
	-webkit-border-radius: 0 4px 4px 4px;
	filter: alpha(opacity =  95);
	_filter: none;
	-moz-opacity: 0.95;
	opacity: 0.95;
	border-radius: 0 0 4px 4px;
	margin-top: -1px;
	padding: 10px 0px 10px 0px;
}

.pop-menu ul li:hover div table td {
	vertical-align: top;
	border-left: 1px solid #D61821;
}

.pop-menu ul li:hover div table td.first{
	border:none;
}

.pop-menu ul li:hover div {
	display: block;
	visibility: visible;
	border: 1px solid #8B0000;
	border-top: 0px;
}

.pop-menu  ul li div ul li a {
	border: 0px;
}

.pop-menu  ul li:hover div ul li a {
	color: #000000;
	font-size: 12px;
	font-weight: bord;
	border: 0px;
}

.pop-menu  ul li:hover div ul li a:hover {
	color: #8B0000 !important;
	text-decoration: underline;
}

.pop-menu  ul li:hover div ul li ul li a {
	--color: #808080 !important;
	font-size: 12px;
	--font-weight: normal;
	margin-left: 12px;
}

.pop-menu  ul li:hover div ul li ul li a:hover {
	text-decoration: underline;
}

.pop-menu div ul li {
	display: list-item;
}

.pop-menu ul.lvl1 ul {
	padding-left:10px;
}

/* Holly Hack for IE \*/
* html .pop-menu {
	height: 1%;
}