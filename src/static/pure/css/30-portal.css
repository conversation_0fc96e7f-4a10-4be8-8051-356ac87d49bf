/*导航*/
#portal-nav {
    height: 28px;
    line-height: 28px;
    position: relative;
    border-bottom: 1px dashed #FF8F8E;
}

/*更改提示信息*/
#portal-modify-msg {
    display: none;
    margin-left: 10px;
}

#portal-nav ul {
    position: absolute;
    top: 0;
    right: 20px;
}

#portal-nav li {
    display: inline;
}

/*portlet定义*/
#portal-portlets-panel {
    top: 28px;
    background-color: #f5f5f5;
    display: none;
}

#portal-portlets-panel {
    padding: 0 20px;
    border: 1px solid #ccc;
    border-top: none;
}

#portal-portlets {
    margin: 15px;
}

#portal-portlets label {
    margin-left: 10px;
}

#portal-portlets input {
    margin-left: 10px;
}

.portal-columns {
    width: 100%;
    padding-bottom: 20px;
}

.portal-columns td {
    vertical-align: top;
}

.portlet {
    height: auto;
    background-color: #fff;
    margin: 2px;
}

.portlet-handler {
    background: url(images/portlet-title.gif) repeat-x;
    border: 1px solid #cecece;
    height: 25px;
    line-height: 25px;
    position: relative;
}

.portlet-title {
    font-weight: bolder;
    margin-left: 16px;
    right: 0;
}

.portlet-content {
    overflow: auto;
    padding: 18px 30px;
    border: 1px solid #DCDCDC;
    border-top: none;
    height: 100px;
    padding: 10px;
}

.portlet-handler-btn {
    position: absolute;
    right: 10px;
    top: 0;
    cursor: pointer;
}

.portlet-handler-btn li {
    display: inline;
    margin: 0 3px;
}

.portlet-handler-btn li a {
    display: inline-block;
    width: 16px;
}

.portlet-handler-btn li.portlet-btn-close a {
    background: url(images/portlet-close.gif) no-repeat scroll center center transparent;
}

.portlet-handler-btn li.portlet-btn-minmax a {
    background: url(images/portlet-maxmin.gif) no-repeat scroll center center transparent;
}

.portlet-handler-btn li.portlet-btn-close a:hover {
    background-image: url(images/portlet-close-hover.png);
    text-decoration: none;
}

.portlet-handler-btn li.portlet-btn-minmax a:hover {
    background-image: url(images/portlet-maxmin-hover.png);
    text-decoration: none;
}

.ui-sortable-placeholder {
    visibility: visible !important;
    background-color: #FA8431;
    border: 1px solid #FF6600;
    border-radius: none;
    height: 6px !important;
    margin-bottom: 10px;
    overflow: hidden;
}

.ui-sortable-placeholder * {
    visibility: hidden;
}

.portlet-post {
    list-style: square outside none;
    margin-left: 10px;
}

.portlet-post li {
    border-bottom: 1px dashed #E5E5E5;
    margin: 5px;
    padding-bottom: 3px;
    position: relative;
}

.portlet-post li a {
    font-weight: bolder;
}

.portlet-post li span {
    color: #A7A7A7;
    position: absolute;
    top: 0;
    right: 0;
}
