/*************左边树菜单***************/
#left_menu_01 .jstree-default.jstree-focused { background:#F6F6F6; }

#left_menu_01 div.jstree-default .jstree-no-dots .jstree-open > ins, #left_menu_01 .jstree-default .jstree-no-dots .jstree-leaf > ins{
	background: url("images/blue/menu_open.gif") no-repeat center;
}

#left_menu_01 div.jstree-default .jstree-no-dots .jstree-closed > ins{
	background: url("images/blue/menu_close.gif") no-repeat center;
}

#left_menu_01 div.jstree-default .jstree-no-dots .jstree-open ins, #left_menu_01 .jstree-default .jstree-no-dots .jstree-leaf ins{
	_background: url("images/blue/menu_open.gif") no-repeat center;
}

#left_menu_01 div.jstree-default .jstree-no-dots .jstree-closed ins{
	_background: url("images/blue/menu_close.gif") no-repeat center;
}

#left_menu_01 .jstree-default .jstree-no-dots li {
    _background: none repeat scroll 0 0 transparent;
}