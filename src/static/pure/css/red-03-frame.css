/*首页顶部logo*/
#pure-frame-top-logo {
	background: url(images/red/logo-portal.gif) no-repeat;
}

/*****************首页顶部 菜单栏*****************/
#nav-menu-bar,#nav-menu-bar .navMenu {
	background-color: darkred;
}

#nav-table-border {
	border-top: 2px solid #D61821;
}

.navMenu .scroll span {
	border-color: darkred #fff;
	background-color: #fff;
}

/*****************1级子菜单*****************/
.slide-menu ul li ul {
	border: 1px solid darkred;
	border-top: 0;
}

.slide-menu ul li ul li {
	border-top: 1px solid #CECFC6;
}

/*具有子菜单的子菜单，在右边展示一个小箭头*/
.slide-menu ul li ul li a.collapsed {
	background-image: url(images/menu/red.gif);
}
/*具有子菜单的子菜单，鼠标划过时切换图片*/
.slide-menu ul li ul li a.collapsed:hover {
	background: darkred url(images/menu/white.gif) no-repeat right center;
}

.slide-menu ul li ul li a:hover {
	background-color: darkred;
	color: #fff;
}

#nav-menu-bar-menu-scroll span {
	 border-color: darkred #FFFFFF;
}
/*****************2级及2级以上的子菜单*****************/
.slide-menu ul li ul li ul {
	border: 1px solid darkred;
}

#nav-location {
	background: url(images/red/nav-location.gif) no-repeat left center;
}

/*************二级水平菜单***************/
div.horizon-lev2 {
	height: 25px;
	background-color: #F1F1F1;
	cursor: default;
}

div.horizon-lev2 span.menu-item {
	vertical-align: top;
}

div.horizon-lev2 span.menu-item-2 {
	line-height: 24px;
	color: #656565;
	padding: 0 10px;
	vertical-align: top;
	display: inline-block;
	margin-top: 1px;
}

div.horizon-lev2 span.menu-item-selected span.menu-item-2 {
	color: #000000;
	font-weight: bold;
	background-color: #FFFFFF;
}

/*************三级水平菜单***************/
div.horizon-lev3 {
	height: 25px;
	background-color: #FFFFFF;
	cursor: default;
}

div.horizon-lev3 span.menu-item {
	vertical-align: top;
}

div.horizon-lev3 span.menu-item-2 {
	line-height: 23px;
	color: #656565;
	padding: 0 10px;
	vertical-align: top;
	display: inline-block;
	margin-top: 2px;
}

div.horizon-lev3 span.menu-item-selected span.menu-item-2 {
	color: #000000;
	font-weight: bold;
}

/*************最后级水平菜单***************/
#frame_011 div.horizon-last-lev {
	border-bottom: #8c0001 solid 1px; 
}

#frame_011 div.horizon-last-lev span.menu-item-selected span.menu-item-2 {
	background: url("images/red/menu_lev2_selected_arrow.gif") no-repeat bottom center;
}
