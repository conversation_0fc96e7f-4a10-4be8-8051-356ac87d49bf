#============================================================================
# Configure Main Scheduler Properties  
#============================================================================
#org.quartz.scheduler.instanceName = DefaultQuartzScheduler
#org.quartz.scheduler.instanceId = AUTO
#org.quartz.scheduler.rmi.export = false
#org.quartz.scheduler.rmi.proxy = false
#org.quartz.scheduler.wrapJobExecutionInUserTransaction = false

#============================================================================
# Configure ThreadPool  
#============================================================================
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 10
org.quartz.threadPool.threadPriority = 5
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread = true
org.quartz.jobStore.isClustered= true

org.quartz.jobStore.clusterCheckinInterval= 20000

org.quartz.jobStore.misfireThreshold= 60000

#============================================================================
# Configure JobStore  
#============================================================================
#org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
#org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.oracle.OracleDelegate
#org.quartz.jobStore.useProperties = false
#org.quartz.jobStore.tablePrefix = QRTZ_
#org.quartz.jobStore.dataSource = myDS

#org.quartz.jobStore.isClustered = true
#org.quartz.jobStore.clusterCheckinInterval = 15000

#============================================================================
# Configure DataSource
#============================================================================


org.quartz.scheduler.instanceName = DefaultQuartzScheduler

org.quartz.scheduler.instanceId= AUTO

org.quartz.threadPool.threadCount = 3

org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX

#org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.oracle.OracleDelegate
org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate

org.quartz.jobStore.tablePrefix = QZ_

org.quartz.jobStore.dataSource = myDS

#org.quartz.scheduler.jmx.export = true

#jdbc--

org.quartz.dataSource.myDS.driver = oracle.jdbc.driver.OracleDriver

#org.quartz.dataSource.myDS.URL =jdbc:oracle:thin:@//************:1521/orcl
org.quartz.dataSource.myDS.URL =****************************** =(ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))(ADDRESS =(PROTOCOL = TCP)(HOST = ************)(PORT = 1521))(LOAD_BALANCE = yes)(CONNECT_DATA =(SERVER = DEDICATED)(SERVICE_NAME = yxfwdb)))

org.quartz.dataSource.myDS.user = market

org.quartz.dataSource.myDS.password = market

org.quartz.dataSource.myDS.maxConnections = 5

org.quartz.jobStore.misfireThreshold = 60000