<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >

<sqlMap  namespace="pure.domain.demo">

<!-- 实体映射-->
<resultMap id="demo" class="com.bonc.demo.ibatis.domain.PureIbatisDemo">
	<result property="demoCode" column="DEMO_CODE"/>
	<result property="demoName" column="DEMO_NAME"/>
	<result property="demoProOne" column="DEMO_PRO_ONE"/>
	<result property="demoProTwo" column="DEMO_PRO_TWO"/>
	<result property="createDate" column="CREATE_DATE"/>
	<result property="updateDate" column="UPDATE_DATE"/>
	<result property="ord" column="ORD"/>	
</resultMap>

<!--查询缓存-->
<cacheModel id="ibatisDemoCache" type="LRU" readOnly="true" serialize="false">
	<flushInterval hours="24"/>
	<flushOnExecute statement="pure.domain.demo.saveDemo"/>
	<flushOnExecute statement="pure.domain.demo.updateDemo"/>
	<flushOnExecute statement="pure.domain.demo.deleteDemo"/>
	<property name="size" value="1000" />
</cacheModel>

<!-- 获取实例数据-->
<sql id="getDemoData">
 	 a.DEMO_CODE DEMO_CODE,
     a.DEMO_NAME DEMO_NAME,
     a.DEMO_PRO_ONE DEMO_PRO_ONE,
     a.DEMO_PRO_TWO DEMO_PRO_TWO,
     a.CREATE_DATE CREATE_DATE,
     a.UPDATE_DATE UPDATE_DATE,
     a.ORD ORD
 from DSS_USER.PURE_DEMO a	
</sql>	

<!--根据ID查询一个实体对象-->
<select id="getDemo" resultMap="demo" >
	select <include refid="pure.domain.demo.getDemoData"/>
	where a.demo_code=#demoCode# 
</select>

<!--查询实体对象列表  -->
<select id="getDemoList" resultMap="demo" ><!-- cacheModel="ibatisDemoCache" -->
	select <include refid="pure.domain.demo.getDemoData"/> 
	
	<dynamic prepend="WHERE">
			<isPropertyAvailable property="demoName">
				<isNotEmpty prepend="AND" property="demoName">
					a.demo_name like #demoName#
				</isNotEmpty>
			</isPropertyAvailable>	
			<isPropertyAvailable property="beginDate">
				<isNotEmpty prepend="AND" property="beginDate">
					<isNotEmpty  property="endDate">
						a.create_date between #beginDate# and #endDate#
					</isNotEmpty>
					
				</isNotEmpty>
			</isPropertyAvailable>		
   </dynamic>
  <dynamic   prepend="order by">
  <isNotEmpty  property="sortCol">
					$sortCol$
  </isNotEmpty>
  <isNotEmpty  property="sortOrder">
					 $sortOrder$
   </isNotEmpty>
          
 </dynamic>
</select>

<!--新增一条记录 -->
<insert id="saveDemo">
  <selectKey resultClass="long" keyProperty="demoCode" >
    SELECT  DSS_USER.HIBERNATE_SEQUENCE.NEXTVAL AS demoCode FROM DUAL
  </selectKey>
		insert into DSS_USER.PURE_DEMO(DEMO_CODE,DEMO_NAME,DEMO_PRO_ONE,
		DEMO_PRO_TWO,CREATE_DATE,ORD)values(#demoCode#,#demoName#,#demoProOne#,#demoProTwo#,sysdate,#ord#)
</insert>


<update id="kaijiang">
	MERGE INTO kaijiang t1
      USING (SELECT COUNT(1) AS kpicount
               FROM kaijiang t
              WHERE t.EXPECT = '$expect$'  and areano= '$areaNo$') t2
      ON (t2.kpicount > 0)
      
      WHEN MATCHED THEN
        UPDATE 
           SET 
               t1.OPENTIMESTAMP = '$opentimestamp$'
               WHERE t1.EXPECT = '$expect$' 
        
      
      WHEN NOT MATCHED THEN
        INSERT
          ( EXPECT,
            OPENCODE,
            OPENTIMESTAMP,
            AREANO
            )
        VALUES
          ('$expect$',
           '$opencode$',
           '$opentimestamp$',
           '$areaNo$'
           )
           
        
	</update>
	

<!--删除记录-->
<delete id="deleteDemo">
	delete from DSS_USER.PURE_DEMO where DEMO_CODE = #demoCodes#
</delete>

<delete id="deletecaipiao">
	delete from CAIPIAO 
</delete>

<insert id="savecaipiao">
		insert into CAIPIAO(id,VALUE,area_no) values(#all#,#mo#,#confArea#)
</insert>

<!--更新一条记录 -->
<update id="updateDemo">
		 update DSS_USER.PURE_DEMO set 
		  DEMO_NAME=#demoName#
        <dynamic>
            <isNotNull property="demoProOne">
            ,DEMO_PRO_ONE=#demoProOne# 
            </isNotNull>
             <isNotNull property="demoProTwo">
            ,DEMO_PRO_TWO=#demoProTwo# 
            </isNotNull>
        </dynamic>
        ,update_date=sysdate,ord=#ord#
		where DEMO_CODE =#demoCode#	
</update>


 <select id="cailist" resultClass="java.util.HashMap">
select t.*, t1.area_name,t2.expect
  from caipiao t, kaijiang_conf t1,(select max (t.expect) expect,areaNo  from  kaijiang  t  group by areaNo) t2
 where t.area_no = t1.area_no  and t1.area_no=t2.areaNo
   and t1.ishad = '1'
 order by to_number(id) desc

</select>

 <select id="kailist" resultClass="java.util.HashMap">
		select * from kaijiang where areano= '$confArea$'     order by to_number(EXPECT) desc

</select>

 <select id="confList" resultClass="java.util.HashMap">
		select * from KAIJIANG_CONF  where ishad='1'

</select>
</sqlMap>
