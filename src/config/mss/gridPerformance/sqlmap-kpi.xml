<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<sqlMap namespace="mss.gridPerformance.kpi">
	<select id="queryKpiTree" resultClass="java.util.HashMap">
select t.par_kpi_code "pId", t.kpi_code "id", t.kpi_name "name"
  		from G_GA.G_GA_KPI_MANAGE_V3 t
 		where t.kpi_status = '1'
 		start with t.par_kpi_code is null
		connect by prior t.kpi_code = t.par_kpi_code
      
      <!-- union 
		 		select distinct  'combKpi' "pId",KPI_ID "id",KPI_NAME "name"  
		 		from g_gi.G_GI_PRODUCE_COMB_KPI
	  union 
		 		select distinct 'root' "pId",'combKpi' "id",'组合指标' "name"  
		 		from g_gi.G_GI_PRODUCE_COMB_KPI  -->
	</select>
	<select id="queryKpiTree_bak20150915am0950" resultClass="java.util.HashMap">
		select t.par_kpi_code "pId", t.kpi_code "id", t.kpi_name "name"
  		from G_GA.G_GA_KPI_MANAGE_V3 t
 		where t.kpi_status = '1'
 		start with t.par_kpi_code is null
		connect by prior t.kpi_code = t.par_kpi_code
      union all
		 		select distinct  'combkpi' "pId",KPI_ID "id",KPI_NAME "name"  
		 		from g_gi.G_GI_PRODUCE_COMB_KPI
	  union all
		 		select distinct  'root' "pId",KPI_ID "combkpi",'组合指标' "name"  
		 		from g_gi.G_GI_PRODUCE_COMB_KPI
	</select>
	<select id="queryKpiTree_bak" resultClass="java.util.HashMap">
		select t.par_kpi_code "pId", t.kpi_code "id", t.kpi_name "name"
  		from G_GA.G_GA_KPI_MANAGE_V3 t
 		where t.kpi_status = '1'
 		start with t.par_kpi_code is null
		connect by prior t.kpi_code = t.par_kpi_code
	</select>
	
	<delete id="deleteKpi">
		delete from G_GA.G_GA_KPI_MANAGE_V3 t 
		where t.kpi_code in(
			select t.kpi_code
			from G_GA.G_GA_KPI_MANAGE_V3 t 
			start with t.par_kpi_code ='$kpiCode$' 
			connect by prior t.kpi_code=t.par_kpi_code
		)
		or t.kpi_code ='$kpiCode$'
	</delete>
	<insert id="insertKpi">
		insert into G_GA.G_GA_KPI_MANAGE_V3 t  values(
			'$stoType$',
			'$kpiType$',
			'$kpiCode$',
			'$kpiName$',
			'$parentKpiCode$',
			'$kpiUnit$',
			'$kpiExpress$',
			'',
			'',
			'$kpiCycle$',
			'',
			'1',
			sysdate,
			'$loginId$',
			'',
			'',
			'',
			'$kpiSql$'
		)
	</insert>
	<update id="updateKpi">
		update G_GA.G_GA_KPI_MANAGE_V3 t
		set 
		t.sto_type='$stoType$',
		t.kpi_type='$kpiType$',
		t.kpi_name='$kpiName$',
		t.kpi_unit='$kpiUnit$',
		t.kpi_express='$kpiExpress$',
		t.KPI_SQL='$kpiSql$',
		t.kpi_cycle='$kpiCycle$'
		where t.kpi_code='$kpiCode$'
	</update>
	<select id="queryKpiSeq" resultClass="string">
		select 'KPI_'||trim(to_char(seq_kpi.nextval,'00000')) from dual
	</select>
	<select id="queryKpiDetail" resultClass="java.util.HashMap">
	select 
	t.kpi_code "kpiCode",
	t.kpi_name "kpiName",
	t.par_kpi_code "parentKpiCode",
	m.kpi_name "parentKpiName",
	t.sto_type "stoType",
	t.kpi_type "kpiType",
	t.kpi_unit "kpiUnit",
	t.kpi_cycle "kpiCycle",
	t.kpi_express "kpiExpress",
	t.KPI_SQL "kpiSql"
	from G_GA.G_GA_KPI_MANAGE_V3 t , G_GA.G_GA_KPI_MANAGE_V3 m
	where  t.par_kpi_code =m.kpi_code(+) and t.kpi_code='$kpiCode$'
	
	</select>
	<select id="queryStoType" resultClass="java.util.HashMap">
		select t.STO_TYPE "vkey",t.STO_TYPE_DESC "vdesc"
		from DMCODE.G_DMCODE_KPI_STO_TYPE t
	</select>
	<select id="queryKpiType" resultClass="java.util.HashMap">
		select t.KPI_TYPE "vkey",t.KPI_TYPE_DESC "vdesc"
		from DMCODE.G_DMCODE_KPIM_TYPE t
	</select>
	<select id="queryKpi" resultClass="java.util.HashMap">
		select * from G_GA.G_GA_KPI_MANAGE_V3 t where t.kpi_name like '%' || '$kpiName$' || '%'
	</select>
</sqlMap>