<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.workZbLogin.Login">
	<select id="getTime" resultClass="java.util.HashMap">
        select *  from PURE_GS.LOGIN_FAIL_LOG where login_id='$loginId$'
    </select>
   <insert id="failLog">
       INSERT INTO PURE_GS.LOGIN_FAIL_LOG(
       id,
       login_id,
       login_time
       ) VALUES (
       sys_guid(),
       #loginId#,
       #LoginTime#
       )
   </insert>
    <select id="getTimeSize" resultClass="java.lang.Long">
        select login_time from PURE_GS.LOGIN_FAIL_LOG where login_id='$loginId$'
    </select>
    <delete id="deleteFailLog">
        DELETE FROM PURE_GS.LOGIN_FAIL_LOG WHERE login_id='$loginId$'
    </delete>
</sqlMap>