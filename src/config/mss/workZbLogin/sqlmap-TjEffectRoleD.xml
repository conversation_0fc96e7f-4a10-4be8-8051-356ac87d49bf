<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.workZbLogin.TjEffectRoleD">

	<!-- 查询列表 -->
	<select id="queryList" resultClass="java.util.HashMap">
		select t.* from G_GA.G_GA_TJ_EFFECT_ROLE_D t where t.month_id = substr('$dayId$',0,6) and t.day_id = substr('$dayId$',7,8)
		order by t.AREA_ID
	</select>
	<!-- 获取列表 -->
	<select id="getListById" resultClass="java.util.HashMap">
		SELECT O.AREA_ID,O.AREA_NAME,O.ORG_PARENT_ID,O.ORG_PARENT_NAME,O.grade,
		O.MONTH_ID,
		O.DAY_ID,
		<PERSON><PERSON>_<PERSON>,
		O.SK<PERSON>L_NUM,
		O.SKJL_YWZERO_RATE,
		O.SKJL_YWARRIVE_RATE,
		O.SKJL_KDZERO_RATE,
		O.SKJL_KDARRIVE_RATE,
		O.SKJL_FTTRZERO_RATE,
		O.SKJL_FTTRARRIVE_RATE,
		O.QDJL_NUM,
		O.QDJL_YWZERO_RATE,
		O.QDJL_YWARRIVE_RATE,
		O.QDJL_KDZERO_RATE,
		O.QDJL_KDARRIVE_RATE,
		O.QDJL_FTTRZERO_RATE,
		O.QDJL_FTTRARRIVE_RATE,
		O.SQJL_NUM               ,
		O.SQJL_YWZERO_RATE       ,
		O.SQJL_YWARRIVE_RATE     ,
		O.SQJL_KDZERO_RATE       ,
		O.SQJL_KDARRIVE_RATE     ,
		O.SQJL_FTTRZERO_RATE     ,
		O.SQJL_FTTRARRIVE_RATE   ,
		O.XYKHJL_NUM,
		O.XYKHJL_YWZERO_RATE,
		O.XYKHJL_YWARRIVE_RATE,
		O.XYKHJL_KDZERO_RATE,
		O.XYKHJL_KDARRIVE_RATE,
		O.XYKHJL_FTTRZERO_RATE,
		O.XYKHJL_FTTRARRIVE_RATE,
		O.YYY_NUM,
		O.YYY_YWZERO_RATE,
		O.YYY_YWARRIVE_RATE,
		O.YYY_KDZERO_RATE,
		O.YYY_KDARRIVE_RATE,
		O.YYY_FTTRZERO_RATE,
		O.YYY_FTTRARRIVE_RATE,
		O.ZWJL_NUM,
		O.ZWJL_YWZERO_RATE,
		O.ZWJL_YWARRIVE_RATE,
		O.ZWJL_KDZERO_RATE,
		O.ZWJL_KDARRIVE_RATE,
		O.ZWJL_FTTRZERO_RATE,
		O.ZWJL_FTTRARRIVE_RATE,
		(case when exists
		(select 1  from G_GA.G_GA_TJ_EFFECT_ROLE_D b where o.AREA_ID = b.ORG_PARENT_ID
		and b.month_id=substr('$dayId$',0,6) and b.day_id=substr('$dayId$',7,8)
		) then 'false' else 'true' end) "isLeaf"
		from G_GA.G_GA_TJ_EFFECT_ROLE_D o
		where o.month_id=substr('$dayId$',0,6) and o.day_id=substr('$dayId$',7,8) and o.AREA_ID = '$orgId$'
		union all
		SELECT AREA_ID,AREA_NAME,ORG_PARENT_ID,ORG_PARENT_NAME,grade,
		MONTH_ID,
		DAY_ID,
		ALL_NUM,
		SKJL_NUM,
		SKJL_YWZERO_RATE,
		SKJL_YWARRIVE_RATE,
		SKJL_KDZERO_RATE,
		SKJL_KDARRIVE_RATE,
		SKJL_FTTRZERO_RATE,
		SKJL_FTTRARRIVE_RATE,
		QDJL_NUM,
		QDJL_YWZERO_RATE,
		QDJL_YWARRIVE_RATE,
		QDJL_KDZERO_RATE,
		QDJL_KDARRIVE_RATE,
		QDJL_FTTRZERO_RATE,
		QDJL_FTTRARRIVE_RATE,
		SQJL_NUM               ,
		SQJL_YWZERO_RATE       ,
		SQJL_YWARRIVE_RATE     ,
		SQJL_KDZERO_RATE       ,
		SQJL_KDARRIVE_RATE     ,
		SQJL_FTTRZERO_RATE     ,
		SQJL_FTTRARRIVE_RATE,
		XYKHJL_NUM,
		XYKHJL_YWZERO_RATE,
		XYKHJL_YWARRIVE_RATE,
		XYKHJL_KDZERO_RATE,
		XYKHJL_KDARRIVE_RATE,
		XYKHJL_FTTRZERO_RATE,
		XYKHJL_FTTRARRIVE_RATE,
		YYY_NUM,
		YYY_YWZERO_RATE,
		YYY_YWARRIVE_RATE,
		YYY_KDZERO_RATE,
		YYY_KDARRIVE_RATE,
		YYY_FTTRZERO_RATE,
		YYY_FTTRARRIVE_RATE,
		ZWJL_NUM,
		ZWJL_YWZERO_RATE,
		ZWJL_YWARRIVE_RATE,
		ZWJL_KDZERO_RATE,
		ZWJL_KDARRIVE_RATE,
		ZWJL_FTTRZERO_RATE,
		ZWJL_FTTRARRIVE_RATE,
		(case when exists (select 1 from G_GA.G_GA_TJ_EFFECT_ROLE_D b where t.AREA_ID = b.ORG_PARENT_ID
		and b.month_id=substr('$dayId$',0,6) and b.day_id=substr('$dayId$',7,8)) then 'false' else 'true' end) "isLeaf"
		from G_GA.G_GA_TJ_EFFECT_ROLE_D t
		where t.month_id=substr('$dayId$',0,6) and t.day_id=substr('$dayId$',7,8) and t.org_parent_id = '$orgId$'
		order by AREA_ID
	</select>
	<!-- 展开 -->
	<select id="getListByParentId" resultClass="java.util.HashMap">
		SELECT AREA_ID,AREA_NAME,ORG_PARENT_ID,ORG_PARENT_NAME,grade,
		MONTH_ID,
		DAY_ID,
		ALL_NUM,
		SKJL_NUM,
		SKJL_YWZERO_RATE,
		SKJL_YWARRIVE_RATE,
		SKJL_KDZERO_RATE,
		SKJL_KDARRIVE_RATE,
		SKJL_FTTRZERO_RATE,
		SKJL_FTTRARRIVE_RATE,
		QDJL_NUM,
		QDJL_YWZERO_RATE,
		QDJL_YWARRIVE_RATE,
		QDJL_KDZERO_RATE,
		QDJL_KDARRIVE_RATE,
		QDJL_FTTRZERO_RATE,
		QDJL_FTTRARRIVE_RATE,
		SQJL_NUM               ,
		SQJL_YWZERO_RATE       ,
		SQJL_YWARRIVE_RATE     ,
		SQJL_KDZERO_RATE       ,
		SQJL_KDARRIVE_RATE     ,
		SQJL_FTTRZERO_RATE     ,
		SQJL_FTTRARRIVE_RATE,
		XYKHJL_NUM,
		XYKHJL_YWZERO_RATE,
		XYKHJL_YWARRIVE_RATE,
		XYKHJL_KDZERO_RATE,
		XYKHJL_KDARRIVE_RATE,
		XYKHJL_FTTRZERO_RATE,
		XYKHJL_FTTRARRIVE_RATE,
		YYY_NUM,
		YYY_YWZERO_RATE,
		YYY_YWARRIVE_RATE,
		YYY_KDZERO_RATE,
		YYY_KDARRIVE_RATE,
		YYY_FTTRZERO_RATE,
		YYY_FTTRARRIVE_RATE,
		ZWJL_NUM,
		ZWJL_YWZERO_RATE,
		ZWJL_YWARRIVE_RATE,
		ZWJL_KDZERO_RATE,
		ZWJL_KDARRIVE_RATE,
		ZWJL_FTTRZERO_RATE,
		ZWJL_FTTRARRIVE_RATE,
		(case when exists (select 1 from G_GA.G_GA_TJ_EFFECT_ROLE_D b where t.AREA_ID = b.ORG_PARENT_ID
		and b.month_id=substr('$dayId$',0,6) and b.day_id=substr('$dayId$',7,8)) then 'false' else 'true' end)  "isLeaf",
		(case when exists (select 1 from G_GA.G_GA_TJ_EFFECT_ROLE_D b where t.AREA_ID = b.ORG_PARENT_ID
		and b.month_id=substr('$dayId$',0,6) and b.day_id=substr('$dayId$',7,8)) then 'false' else 'true' end)  "expanded"
		from G_GA.G_GA_TJ_EFFECT_ROLE_D t
		where t.month_id=substr('$dayId$',0,6) and t.day_id=substr('$dayId$',7,8)
		and t.org_parent_id='$orgId$'
		order by t.AREA_ID
	</select>


    
</sqlMap>