<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.workZbLogin.RybhM">

    <!-- 获取列表 -->
	<select id="getListById" resultClass="java.util.HashMap">
        SELECT O.ORG_ID,O.ORG_NAME,O.ORG_PARENT_ID,O.ORG_PARENT_NAME,O.ORG_GRADE,

        sum(LJRS1)                                                                                LJRS1,
        sum(LD_LJRS1)                                                                             LD_LJRS1,
        abs(round(decode(sum(LD_LJRS1), 0, 0, (sum(LJRS1) - sum(LD_LJRS1)) / sum(LD_LJRS1))))     LJRS1_RATE,
        sum(LJRS2)                                                                                LJRS2,
        sum(LD_LJRS2)                                                                             LD_LJRS2,
        abs(round(decode(sum(LD_LJRS2), 0, 0, (sum(LJRS2) - sum(LD_LJRS2)) / sum(LD_LJRS2))))     LJRS2_RATE,
        sum(LJRS3)                                                                                LJRS3,
        sum(LD_LJRS3)                                                                             LD_LJRS3,
        abs(round(decode(sum(LD_LJRS3), 0, 0, (sum(LJRS3) - sum(LD_LJRS3)) / sum(LD_LJRS3))))     LJRS3_RATE,
        sum(LJRS4)                                                                                LJRS4,
        sum(LD_LJRS4)                                                                             LD_LJRS4,
        abs(round(decode(sum(LD_LJRS4), 0, 0, (sum(LJRS4) - sum(LD_LJRS4)) / sum(LD_LJRS4))))     LJRS4_RATE,
        sum(LJRS5)                                                                                LJRS5,
        sum(LD_LJRS5)                                                                             LD_LJRS5,
        abs(round(decode(sum(LD_LJRS5), 0, 0, (sum(LJRS5) - sum(LD_LJRS5)) / sum(LD_LJRS5))))     LJRS5_RATE,
        sum(LJRS6)                                                                                LJRS6,
        sum(LD_LJRS6)                                                                             LD_LJRS6,
        abs(round(decode(sum(LD_LJRS6), 0, 0, (sum(LJRS6) - sum(LD_LJRS6)) / sum(LD_LJRS6))))     LJRS6_RATE,
        sum(LJRS7)                                                                                LJRS7,
        sum(LD_LJRS7)                                                                             LD_LJRS7,
        abs(round(decode(sum(LD_LJRS7), 0, 0, (sum(LJRS7) - sum(LD_LJRS7)) / sum(LD_LJRS7))))     LJRS7_RATE,
        sum(LJRS8)                                                                                LJRS8,
        sum(LD_LJRS8)                                                                             LD_LJRS8,
        abs(round(decode(sum(LD_LJRS8), 0, 0, (sum(LJRS8) - sum(LD_LJRS8)) / sum(LD_LJRS8))))     LJRS8_RATE,
        sum(LJRS9)                                                                                LJRS9,
        sum(LD_LJRS9)                                                                             LD_LJRS9,
        abs(round(decode(sum(LD_LJRS9), 0, 0, (sum(LJRS9) - sum(LD_LJRS9)) / sum(LD_LJRS9))))     LJRS9_RATE,
        sum(LJRS10)                                                                               LJRS10,
        sum(LD_LJRS10)                                                                            LD_LJRS10,
        abs(round(decode(sum(LD_LJRS10), 0, 0, (sum(LJRS10) - sum(LD_LJRS10)) / sum(LD_LJRS10)))) LJRS10_RATE,
        sum(LJRS11)                                                                               LJRS11,
        sum(LD_LJRS11)                                                                            LD_LJRS11,
        abs(round(decode(sum(LD_LJRS11), 0, 0, (sum(LJRS11) - sum(LD_LJRS11)) / sum(LD_LJRS11)))) LJRS11_RATE,

     (case when exists
        (select 1  from g_ga.G_GA_RYBH_M b where o.org_id = b.ORG_PARENT_ID
         and b.month_id = '$monthId$') then 'false' else 'true' end) "isLeaf"
    from G_GA.G_GA_RYBH_M o
    where o.month_id = '$monthId$' and o.org_id = '$orgId$'
        <!-- 用工类型 -->
        <isNotEmpty property="yglx">
            <isNotEqual property="yglx" compareValue="0">
                and o.STAFF_TYPE = '$yglx$'
            </isNotEqual>
        </isNotEmpty>

    group by o.org_id,o.org_name,o.org_parent_id,o.org_parent_name,o.org_grade
    union all
        SELECT ORG_ID,ORG_NAME,ORG_PARENT_ID,ORG_PARENT_NAME,ORG_GRADE,

        sum(LJRS1)                                                                                LJRS1,
        sum(LD_LJRS1)                                                                             LD_LJRS1,
        abs(round(decode(sum(LD_LJRS1), 0, 0, (sum(LJRS1) - sum(LD_LJRS1)) / sum(LD_LJRS1))))     LJRS1_RATE,
        sum(LJRS2)                                                                                LJRS2,
        sum(LD_LJRS2)                                                                             LD_LJRS2,
        abs(round(decode(sum(LD_LJRS2), 0, 0, (sum(LJRS2) - sum(LD_LJRS2)) / sum(LD_LJRS2))))     LJRS2_RATE,
        sum(LJRS3)                                                                                LJRS3,
        sum(LD_LJRS3)                                                                             LD_LJRS3,
        abs(round(decode(sum(LD_LJRS3), 0, 0, (sum(LJRS3) - sum(LD_LJRS3)) / sum(LD_LJRS3))))     LJRS3_RATE,
        sum(LJRS4)                                                                                LJRS4,
        sum(LD_LJRS4)                                                                             LD_LJRS4,
        abs(round(decode(sum(LD_LJRS4), 0, 0, (sum(LJRS4) - sum(LD_LJRS4)) / sum(LD_LJRS4))))     LJRS4_RATE,
        sum(LJRS5)                                                                                LJRS5,
        sum(LD_LJRS5)                                                                             LD_LJRS5,
        abs(round(decode(sum(LD_LJRS5), 0, 0, (sum(LJRS5) - sum(LD_LJRS5)) / sum(LD_LJRS5))))     LJRS5_RATE,
        sum(LJRS6)                                                                                LJRS6,
        sum(LD_LJRS6)                                                                             LD_LJRS6,
        abs(round(decode(sum(LD_LJRS6), 0, 0, (sum(LJRS6) - sum(LD_LJRS6)) / sum(LD_LJRS6))))     LJRS6_RATE,
        sum(LJRS7)                                                                                LJRS7,
        sum(LD_LJRS7)                                                                             LD_LJRS7,
        abs(round(decode(sum(LD_LJRS7), 0, 0, (sum(LJRS7) - sum(LD_LJRS7)) / sum(LD_LJRS7))))     LJRS7_RATE,
        sum(LJRS8)                                                                                LJRS8,
        sum(LD_LJRS8)                                                                             LD_LJRS8,
        abs(round(decode(sum(LD_LJRS8), 0, 0, (sum(LJRS8) - sum(LD_LJRS8)) / sum(LD_LJRS8))))     LJRS8_RATE,
        sum(LJRS9)                                                                                LJRS9,
        sum(LD_LJRS9)                                                                             LD_LJRS9,
        abs(round(decode(sum(LD_LJRS9), 0, 0, (sum(LJRS9) - sum(LD_LJRS9)) / sum(LD_LJRS9))))     LJRS9_RATE,
        sum(LJRS10)                                                                               LJRS10,
        sum(LD_LJRS10)                                                                            LD_LJRS10,
        abs(round(decode(sum(LD_LJRS10), 0, 0, (sum(LJRS10) - sum(LD_LJRS10)) / sum(LD_LJRS10)))) LJRS10_RATE,
        sum(LJRS11)                                                                               LJRS11,
        sum(LD_LJRS11)                                                                            LD_LJRS11,
        abs(round(decode(sum(LD_LJRS11), 0, 0, (sum(LJRS11) - sum(LD_LJRS11)) / sum(LD_LJRS11)))) LJRS11_RATE,

     (case when exists (select 1 from g_ga.G_GA_RYBH_M b where t.org_id = b.ORG_PARENT_ID
        and b.month_id = '$monthId$') then 'false' else 'true' end) "isLeaf"
    from G_GA.G_GA_RYBH_M t
    where t.month_id = '$monthId$' and t.org_parent_id = '$orgId$'
        <!-- 用工类型 -->
        <isNotEmpty property="yglx">
            <isNotEqual property="yglx" compareValue="0">
                and t.STAFF_TYPE = '$yglx$'
            </isNotEqual>
        </isNotEmpty>
    group by t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade
        order by org_id
	</select>
    <!-- 展开 -->
    <select id="getListByParentId" resultClass="java.util.HashMap">
        SELECT ORG_ID,ORG_NAME,ORG_PARENT_ID,ORG_PARENT_NAME,ORG_GRADE,
        sum(LJRS1)                                                                                LJRS1,
        sum(LD_LJRS1)                                                                             LD_LJRS1,
        abs(round(decode(sum(LD_LJRS1), 0, 0, (sum(LJRS1) - sum(LD_LJRS1)) / sum(LD_LJRS1))))     LJRS1_RATE,
        sum(LJRS2)                                                                                LJRS2,
        sum(LD_LJRS2)                                                                             LD_LJRS2,
        abs(round(decode(sum(LD_LJRS2), 0, 0, (sum(LJRS2) - sum(LD_LJRS2)) / sum(LD_LJRS2))))     LJRS2_RATE,
        sum(LJRS3)                                                                                LJRS3,
        sum(LD_LJRS3)                                                                             LD_LJRS3,
        abs(round(decode(sum(LD_LJRS3), 0, 0, (sum(LJRS3) - sum(LD_LJRS3)) / sum(LD_LJRS3))))     LJRS3_RATE,
        sum(LJRS4)                                                                                LJRS4,
        sum(LD_LJRS4)                                                                             LD_LJRS4,
        abs(round(decode(sum(LD_LJRS4), 0, 0, (sum(LJRS4) - sum(LD_LJRS4)) / sum(LD_LJRS4))))     LJRS4_RATE,
        sum(LJRS5)                                                                                LJRS5,
        sum(LD_LJRS5)                                                                             LD_LJRS5,
        abs(round(decode(sum(LD_LJRS5), 0, 0, (sum(LJRS5) - sum(LD_LJRS5)) / sum(LD_LJRS5))))     LJRS5_RATE,
        sum(LJRS6)                                                                                LJRS6,
        sum(LD_LJRS6)                                                                             LD_LJRS6,
        abs(round(decode(sum(LD_LJRS6), 0, 0, (sum(LJRS6) - sum(LD_LJRS6)) / sum(LD_LJRS6))))     LJRS6_RATE,
        sum(LJRS7)                                                                                LJRS7,
        sum(LD_LJRS7)                                                                             LD_LJRS7,
        abs(round(decode(sum(LD_LJRS7), 0, 0, (sum(LJRS7) - sum(LD_LJRS7)) / sum(LD_LJRS7))))     LJRS7_RATE,
        sum(LJRS8)                                                                                LJRS8,
        sum(LD_LJRS8)                                                                             LD_LJRS8,
        abs(round(decode(sum(LD_LJRS8), 0, 0, (sum(LJRS8) - sum(LD_LJRS8)) / sum(LD_LJRS8))))     LJRS8_RATE,
        sum(LJRS9)                                                                                LJRS9,
        sum(LD_LJRS9)                                                                             LD_LJRS9,
        abs(round(decode(sum(LD_LJRS9), 0, 0, (sum(LJRS9) - sum(LD_LJRS9)) / sum(LD_LJRS9))))     LJRS9_RATE,
        sum(LJRS10)                                                                               LJRS10,
        sum(LD_LJRS10)                                                                            LD_LJRS10,
        abs(round(decode(sum(LD_LJRS10), 0, 0, (sum(LJRS10) - sum(LD_LJRS10)) / sum(LD_LJRS10)))) LJRS10_RATE,
        sum(LJRS11)                                                                               LJRS11,
        sum(LD_LJRS11)                                                                            LD_LJRS11,
        abs(round(decode(sum(LD_LJRS11), 0, 0, (sum(LJRS11) - sum(LD_LJRS11)) / sum(LD_LJRS11)))) LJRS11_RATE,
    (case when exists (select 1 from g_ga.G_GA_RYBH_M b where t.org_id = b.ORG_PARENT_ID
        and b.month_id = '$monthId$') then 'false' else 'true' end)  "isLeaf",
    (case when exists (select 1 from g_ga.G_GA_RYBH_M b where t.org_id = b.ORG_PARENT_ID
        and b.month_id = '$monthId$') then 'false' else 'true' end)  "expanded"
    from G_GA.G_GA_RYBH_M t
    where t.month_id = '$monthId$'
    and t.org_parent_id='$orgId$'
        <!-- 用工类型 -->
        <isNotEmpty property="yglx">
            <isNotEqual property="yglx" compareValue="0">
                and t.STAFF_TYPE = '$yglx$'
            </isNotEqual>
        </isNotEmpty>
    group by t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade
    order by t.org_id
	 </select>
    
    
</sqlMap>