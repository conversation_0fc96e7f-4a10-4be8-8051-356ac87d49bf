<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.busiReport.MarketTieJunRatioMonth">

    <select id="selectArea" resultClass="java.util.HashMap">
		select t.area_no,t.area_desc from PURE_GS.REGION t where 1=1
		  <isNotEmpty prepend="and" property="areaNo">
			<isNotEqual property="areaNo" compareValue="">
			<isNotEqual property="areaNo" compareValue="087">
		      T.AREA_NO = '$areaNo$'
		   	</isNotEqual>
		   	</isNotEqual>
		 </isNotEmpty>
		order by t.ord
	</select>
	
	
	
	 <!-- 查询组织结构 -->
    <select id="getOrg" resultClass="java.util.HashMap">
        select a.* from
        ( select o.name TEXT, o.id ID, o.parent_id, '0' IS_ORG, o.grade, o.ord
        from PURE_GS.ORG_ORGANIZATION_MOD_V16_M o
        where o.month_id = '$monthId$'
        <isNotEmpty prepend="and" property="sellArea">
            o.id = '$sellArea$'
        </isNotEmpty>
        and o.grade &lt; 5
        union all
        select o.name TEXT,
        o.id ID,
        o.parent_id,
        '0' IS_ORG,
        o.grade,
        o.ord
        from (select * from PURE_GS.ORG_ORGANIZATION_MOD_V16_M t
        where t.month_id='$monthId$'
        and t.grade &lt; 5 ) o
        CONNECT BY PRIOR o.id = o.parent_id
        START WITH
        <isNotEmpty  property="sellArea">
            o.parent_id = '$sellArea$'
        </isNotEmpty>
        ) a
        order by a.grade, a.ord
    </select>
    <!-- 查询全部角色 -->
    <select id="selectRoleList" resultClass="java.util.HashMap">
        select 'all' as ROLE_ID,'全部' as ROLE_DESC from dual
        union all
        select PRP_ROLE as ROLE_ID,PRP_ROLE_DESC as ROLE_DESC from G_GA.G_GA_PRP_ROLE
    </select>

	
	<select id="getListById" resultClass="java.util.HashMap">

 	 select org_id,
       org_name,
       org_parent_id,
       org_parent_name,
       org_grade,
       sum(TJ_NUM) as TJ_NUM,
       sum(LG_NUM)  as LG_NUM,
       round(sum(TJ_SCX_RATE), 2) as TJ_SCX_RATE,
       round(sum(TJ_ALL_RATE), 2) as TJ_ALL_RATE,
       round(sum(WG_TJ_RATE), 2) as WG_TJ_RATE,
      
       (case
         when (select count(1)
                 from G_GA.G_GA_PUBLIC_TJRATIO_INFO_M m
                where m.month_id =substr('$monthId$',0,6) 
                  and m.org_parent_id = o.org_id) = 0 then
          'true'
         else
          'false'
       end) "isLeaf"
  from G_GA.G_GA_PUBLIC_TJRATIO_INFO_M o
 where o.month_id = substr('$monthId$',0,6) 
   and o.org_id = '$orgIds$'   
   
    
        
   
  group by org_id,org_name,org_parent_id,org_parent_name,org_grade,org_id  
   
union all    
select 
       org_id,
       org_name,
       org_parent_id,
       org_parent_name,
       org_grade,
       sum(TJ_NUM) as TJ_NUM,
       sum(LG_NUM)  as LG_NUM,
       round(sum(TJ_SCX_RATE), 2) as TJ_SCX_RATE,
       round(sum(TJ_ALL_RATE), 2) as TJ_ALL_RATE,
       round(sum(WG_TJ_RATE), 2) as WG_TJ_RATE,
       (case
         when (select count(1)
                 from G_GA.G_GA_PUBLIC_TJRATIO_INFO_M m
                where m.month_id = substr('$monthId$',0,6)  
                  and m.org_parent_id = t.org_id) = 0 then  
          'true'
         else
          'false'
       end) "isLeaf"
  from (select * from G_GA.G_GA_PUBLIC_TJRATIO_INFO_M t1 order by t1.org_id) t
 where t.month_id =substr('$monthId$',0,6) 
   and t.org_parent_id = '$orgIds$'     
   
   
    
   
  group by t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade,t.org_id 
   
     order by org_grade,org_id   
 
	</select>
    <select id="getListByParentId" resultClass="java.util.HashMap">
       select  org_id,
       org_name,
       org_parent_id,
       org_parent_name,
       org_grade,
       sum(TJ_NUM) as TJ_NUM,
       sum(LG_NUM)  as LG_NUM,
       round(sum(TJ_SCX_RATE), 2) as TJ_SCX_RATE,
       round(sum(TJ_ALL_RATE), 2) as TJ_ALL_RATE,
       round(sum(WG_TJ_RATE), 2) as WG_TJ_RATE,
	       
          (case when (select count(1)
                     from G_GA.G_GA_PUBLIC_TJRATIO_INFO_M m
                    where m.month_id = substr('$monthId$', 0, 6)
                     
                      and m.org_parent_id =t.org_id )=0 then 'true' else 'false' end) "isLeaf",
          (case when (select count(1)
                     from G_GA.G_GA_PUBLIC_TJRATIO_INFO_M m
                    where m.month_id = substr('$monthId$', 0, 6)
                      
                      and m.org_parent_id =t.org_id )=0 then 'true' else 'false' end) "expanded"
                      
    from G_GA.G_GA_PUBLIC_TJRATIO_INFO_M t
    where t.month_id = substr('$monthId$',0,6)
   
    and t.org_parent_id='$orgIds$'       
    
    
    group by t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade,t.org_id   
    order by t.org_id   
   


	 </select>
    
    
</sqlMap>