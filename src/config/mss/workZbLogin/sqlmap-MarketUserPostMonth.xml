<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.busiReport.MarketUserPostMonth">

    <select id="selectArea" resultClass="java.util.HashMap">
		select t.area_no,t.area_desc from PURE_GS.REGION t where 1=1
		  <isNotEmpty prepend="and" property="areaNo">
			<isNotEqual property="areaNo" compareValue="">
			<isNotEqual property="areaNo" compareValue="087">
		      T.AREA_NO = '$areaNo$'
		   	</isNotEqual>
		   	</isNotEqual>
		 </isNotEmpty>
		order by t.ord
	</select>
	
	
	
	 <!-- 查询组织结构 -->
    <select id="getOrg" resultClass="java.util.HashMap">
        select a.* from
        ( select o.name TEXT, o.id ID, o.parent_id, '0' IS_ORG, o.grade, o.ord
        from PURE_GS.ORG_ORGANIZATION_MOD_V16_M o
        where o.month_id = '$monthId$'
        <isNotEmpty prepend="and" property="sellArea">
            o.id = '$sellArea$'
        </isNotEmpty>
        and o.grade &lt; 5
        union all
        select o.name TEXT,
        o.id ID,
        o.parent_id,
        '0' IS_ORG,
        o.grade,
        o.ord
        from (select * from PURE_GS.ORG_ORGANIZATION_MOD_V16_M t
        where t.month_id='$monthId$'
        and t.grade &lt; 5 ) o
        CONNECT BY PRIOR o.id = o.parent_id
        START WITH
        <isNotEmpty  property="sellArea">
            o.parent_id = '$sellArea$'
        </isNotEmpty>
        ) a
        order by a.grade, a.ord
    </select>
    <!-- 查询全部角色 -->
    <select id="selectRoleList" resultClass="java.util.HashMap">
        select 'all' as ROLE_ID,'全部' as ROLE_DESC from dual
        union all
        select PRP_ROLE as ROLE_ID,PRP_ROLE_DESC as ROLE_DESC from G_GA.G_GA_PRP_ROLE
    </select>

	
	<select id="getListById" resultClass="java.util.HashMap">

 	 select org_id,
       org_name,
       org_parent_id,
       org_parent_name,
       org_grade,
      sum(CPGL_NUM) as CPGL_NUM,
       sum(KHYY_NUM) as KHYY_NUM,
       sum(QDYY_NUM) as QDYY_NUM,
       sum(SKJL_NUM) as SKJL_NUM,
       sum(SHQDJL_NUM) as SHQDJL_NUM,
       sum(SQJL_NUM) as SQJL_NUM,
       sum(SCYX_NUM) as SCYX_NUM,
       sum(WGFZR_NUM) as WGFZR_NUM,
       sum(XSYY_NUM) as XSYY_NUM,
       sum(XYZYYX_NUM) as XYZYYX_NUM,
       sum(YYTDZ_NUM) as YYTDZ_NUM,
       sum(YYTYYY_NUM) as YYTYYY_NUM,
       sum(ZHYY_NUM) as ZHYY_NUM,
       sum(ZTYY_NUM) as ZTYY_NUM,
       sum(ZDGL_NUM) as ZDGL_NUM,
       sum(ZHTC_NUM) as ZHTC_NUM,
       sum(HTZC_NUM) as HTZC_NUM,
       sum(QDYX_NUM) as QDYX_NUM,
       sum(ZJGCS_NUM) as ZJGCS_NUM,
       sum(ALL_NUM) as ALL_NUM,
      
       
      
       (case
         when (select count(1)
                 from G_GA.G_GA_PUBLIC_POST_INFO_M m
                where m.month_id =substr('$monthId$',0,6) 
                  and m.org_parent_id = o.org_id) = 0 then
          'true'
         else
          'false'
       end) "isLeaf"
  from G_GA.G_GA_PUBLIC_POST_INFO_M o
 where o.month_id = substr('$monthId$',0,6) 
   and o.org_id = '$orgIds$'   
   
    
        
   
  group by org_id,org_name,org_parent_id,org_parent_name,org_grade,org_id  
   
union all    
select 
       org_id,
       org_name,
       org_parent_id,
       org_parent_name,
       org_grade,
       sum(CPGL_NUM) as CPGL_NUM,
       sum(KHYY_NUM) as KHYY_NUM,
       sum(QDYY_NUM) as QDYY_NUM,
       sum(SKJL_NUM) as SKJL_NUM,
       sum(SHQDJL_NUM) as SHQDJL_NUM,
       sum(SQJL_NUM) as SQJL_NUM,
       sum(SCYX_NUM) as SCYX_NUM,
       sum(WGFZR_NUM) as WGFZR_NUM,
       sum(XSYY_NUM) as XSYY_NUM,
       sum(XYZYYX_NUM) as XYZYYX_NUM,
       sum(YYTDZ_NUM) as YYTDZ_NUM,
       sum(YYTYYY_NUM) as YYTYYY_NUM,
       sum(ZHYY_NUM) as ZHYY_NUM,
       sum(ZTYY_NUM) as ZTYY_NUM,
       sum(ZDGL_NUM) as ZDGL_NUM,
       sum(ZHTC_NUM) as ZHTC_NUM,
       sum(HTZC_NUM) as HTZC_NUM,
       sum(QDYX_NUM) as QDYX_NUM,
       sum(ZJGCS_NUM) as ZJGCS_NUM,
       sum(ALL_NUM) as ALL_NUM,
      
       (case
         when (select count(1)
                 from G_GA.G_GA_PUBLIC_POST_INFO_M m
                where m.month_id = substr('$monthId$',0,6)  
                  and m.org_parent_id = t.org_id) = 0 then  
          'true'
         else
          'false'
       end) "isLeaf"
  from (select * from G_GA.G_GA_PUBLIC_POST_INFO_M t1 order by t1.org_id) t
 where t.month_id =substr('$monthId$',0,6) 
   and t.org_parent_id = '$orgIds$'     
   
   
    
   
  group by t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade,t.org_id 
   
     order by org_grade,org_id   
 
	</select>
    <select id="getListByParentId" resultClass="java.util.HashMap">
       select  org_id,
       org_name,
       org_parent_id,
       org_parent_name,
       org_grade,
       sum(CPGL_NUM) as CPGL_NUM,
       sum(KHYY_NUM) as KHYY_NUM,
       sum(QDYY_NUM) as QDYY_NUM,
       sum(SKJL_NUM) as SKJL_NUM,
       sum(SHQDJL_NUM) as SHQDJL_NUM,
       sum(SQJL_NUM) as SQJL_NUM,
       sum(SCYX_NUM) as SCYX_NUM,
       sum(WGFZR_NUM) as WGFZR_NUM,
       sum(XSYY_NUM) as XSYY_NUM,
       sum(XYZYYX_NUM) as XYZYYX_NUM,
       sum(YYTDZ_NUM) as YYTDZ_NUM,
       sum(YYTYYY_NUM) as YYTYYY_NUM,
       sum(ZHYY_NUM) as ZHYY_NUM,
       sum(ZTYY_NUM) as ZTYY_NUM,
       sum(ZDGL_NUM) as ZDGL_NUM,
       sum(ZHTC_NUM) as ZHTC_NUM,
       sum(HTZC_NUM) as HTZC_NUM,
       sum(QDYX_NUM) as QDYX_NUM,
       sum(ZJGCS_NUM) as ZJGCS_NUM,
       sum(ALL_NUM) as ALL_NUM,
      
	       
          (case when (select count(1)
                     from G_GA.G_GA_PUBLIC_POST_INFO_M m
                    where m.month_id = substr('$monthId$', 0, 6)
                     
                      and m.org_parent_id =t.org_id )=0 then 'true' else 'false' end) "isLeaf",
          (case when (select count(1)
                     from G_GA.G_GA_PUBLIC_POST_INFO_M m
                    where m.month_id = substr('$monthId$', 0, 6)
                      
                      and m.org_parent_id =t.org_id )=0 then 'true' else 'false' end) "expanded"
                      
    from G_GA.G_GA_PUBLIC_POST_INFO_M t
    where t.month_id = substr('$monthId$',0,6)
   
    and t.org_parent_id='$orgIds$'       
    
    
    group by t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade,t.org_id   
    order by t.org_id   
   


	 </select>
    
    
</sqlMap>