<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.workZbLogin.TjEffectAllD">

	<!-- 查询列表 -->
	<select id="queryList" resultClass="java.util.HashMap">
		select t.* from G_GA.G_GA_TJ_EFFECT_ALL_D t where t.month_id = substr('$dayId$',0,6) and t.day_id = substr('$dayId$',7,8)
		order by t.AREA_ID
	</select>
	<!-- 获取列表 -->
	<select id="getListById" resultClass="java.util.HashMap">
		SELECT O.AREA_ID,O.AREA_NAME,O.ORG_PARENT_ID,O.ORG_PARENT_NAME,O.grade,
		O.MONTH_ID，
		O.DAY_ID，
		O.ALL_NUM，
		O.YW_ZERO_NUM，
		O.YW_ZERO_RATE，
		O.YW_LOW_NUM，
		O.YW_LOW_RATE，
		O.YW_ARRIVE_NUM，
		O.YW_ARRIVE_RATE，
		O.KD_ZERO_NUM，
		O.KD_ZERO_RATE，
		O.KD_LOW_NUM，
		O.KD_LOW_RATE，
		O.KD_ARRIVE_NUM，
		O.KD_ARRIVE_RATE，
		O.FTTR_ZERO_NUM，
		O.FTTR_ZERO_RATE，
		O.FTTR_LOW_NUM，
		O.FTTR_LOW_RATE，
		O.FTTR_ARRIVE_NUM，
		O.FTTR_ARRIVE_RATE，
		(case when exists
		(select 1  from G_GA.G_GA_TJ_EFFECT_ALL_D b where o.AREA_ID = b.ORG_PARENT_ID
		and b.month_id=substr('$dayId$',0,6) and b.day_id=substr('$dayId$',7,8)
		) then 'false' else 'true' end) "isLeaf"
		from G_GA.G_GA_TJ_EFFECT_ALL_D o
		where o.month_id=substr('$dayId$',0,6) and o.day_id=substr('$dayId$',7,8) and o.AREA_ID = '$orgId$'
		union all
		SELECT AREA_ID,AREA_NAME,ORG_PARENT_ID,ORG_PARENT_NAME,grade,
		MONTH_ID，
		DAY_ID，
		ALL_NUM，
		YW_ZERO_NUM，
		YW_ZERO_RATE，
		YW_LOW_NUM，
		YW_LOW_RATE，
		YW_ARRIVE_NUM，
		YW_ARRIVE_RATE，
		KD_ZERO_NUM，
		KD_ZERO_RATE，
		KD_LOW_NUM，
		KD_LOW_RATE，
		KD_ARRIVE_NUM，
		KD_ARRIVE_RATE，
		FTTR_ZERO_NUM，
		FTTR_ZERO_RATE，
		FTTR_LOW_NUM，
		FTTR_LOW_RATE，
		FTTR_ARRIVE_NUM，
		FTTR_ARRIVE_RATE，
		(case when exists (select 1 from G_GA.G_GA_TJ_EFFECT_ALL_D b where t.AREA_ID = b.ORG_PARENT_ID
		and b.month_id=substr('$dayId$',0,6) and b.day_id=substr('$dayId$',7,8)) then 'false' else 'true' end) "isLeaf"
		from G_GA.G_GA_TJ_EFFECT_ALL_D t
		where t.month_id=substr('$dayId$',0,6) and t.day_id=substr('$dayId$',7,8) and t.org_parent_id = '$orgId$'
		order by AREA_ID
	</select>
	<!-- 展开 -->
	<select id="getListByParentId" resultClass="java.util.HashMap">
		SELECT AREA_ID,AREA_NAME,ORG_PARENT_ID,ORG_PARENT_NAME,grade,
		MONTH_ID，
		DAY_ID，
		ALL_NUM，
		YW_ZERO_NUM，
		YW_ZERO_RATE，
		YW_LOW_NUM，
		YW_LOW_RATE，
		YW_ARRIVE_NUM，
		YW_ARRIVE_RATE，
		KD_ZERO_NUM，
		KD_ZERO_RATE，
		KD_LOW_NUM，
		KD_LOW_RATE，
		KD_ARRIVE_NUM，
		KD_ARRIVE_RATE，
		FTTR_ZERO_NUM，
		FTTR_ZERO_RATE，
		FTTR_LOW_NUM，
		FTTR_LOW_RATE，
		FTTR_ARRIVE_NUM，
		FTTR_ARRIVE_RATE，
		(case when exists (select 1 from G_GA.G_GA_TJ_EFFECT_ALL_D b where t.AREA_ID = b.ORG_PARENT_ID
		and b.month_id=substr('$dayId$',0,6) and b.day_id=substr('$dayId$',7,8)) then 'false' else 'true' end)  "isLeaf",
		(case when exists (select 1 from G_GA.G_GA_TJ_EFFECT_ALL_D b where t.AREA_ID = b.ORG_PARENT_ID
		and b.month_id=substr('$dayId$',0,6) and b.day_id=substr('$dayId$',7,8)) then 'false' else 'true' end)  "expanded"
		from G_GA.G_GA_TJ_EFFECT_ALL_D t
		where t.month_id=substr('$dayId$',0,6) and t.day_id=substr('$dayId$',7,8)
		and t.org_parent_id='$orgId$'
		order by t.AREA_ID
	</select>
</sqlMap>