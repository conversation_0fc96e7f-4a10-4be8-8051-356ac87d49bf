<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.workZbLogin.TiejunChnManagerWarn">

	<!-- 查询组织结构（到网格级） -->
	<select id="getOrgTree" resultClass="java.util.HashMap">
		select a.* from
		( select o.name as TEXT, o.id as ID, o.parent_id as PID, o.GRADE, o.ORD
		from PURE_GS.ORG_ORGANIZATION_MOD_V16_D o
			where o.month_id = substr('$dayId$',0,6) and o.day_id = substr('$dayId$',7,8)
			<isEqual property="saleArea" compareValue="8700">
				and o.id in ('8700','8690')
			</isEqual>
			<isNotEqual property="saleArea" compareValue="8700">
				and o.id = '$saleArea$'
			</isNotEqual>
			and o.grade &lt; 5
		union all
		select o.name as TEXT, o.id as ID, o.parent_id as PID, o.GRADE, o.ORD
		from (select * from PURE_GS.ORG_ORGANIZATION_MOD_V16_D t
			where t.month_id = substr('$dayId$',0,6) and t.day_id = substr('$dayId$',7,8)
			and t.grade &lt; 5 ) o
			START WITH
			<isEqual property="saleArea" compareValue="8700">
				o.parent_id in ('8700','8690')
			</isEqual>
			<isNotEqual property="saleArea" compareValue="8700">
				o.parent_id = '$saleArea$'
			</isNotEqual>
			CONNECT BY PRIOR o.id = o.parent_id
		) a
		order by a.GRADE, a.ORD
	</select>

	<!-- 查询角色树 (网格经理及全部下级) -->
	<select id="getRoleTree" resultClass="java.util.HashMap">
		select t.role_id as id,
		t.parent_id as pid,
		case when t.role_id = '$roleId$' then '全部' else t.role_name end text
		from pure_gs.pure_role t
		where t.flag = '1'
		start with t.role_id = '$roleId$'
		connect by prior t.role_id = t.parent_id
	</select>
	<!-- 查询个人列表 -->
	<select id="queryPerlList" resultClass="java.util.HashMap">
		select *  from G_GA.G_GA_QDBBJLYJ_D t    
		where t.month_id = substr('$dayId$',0,6) and t.day_id = substr('$dayId$',7,8)
		<isEqual property="grade" compareValue="2">
			<isNotEmpty property="orgId">
				and t.AREA_ID = '$orgId$'
			</isNotEmpty>
		</isEqual>
		<isEqual property="grade" compareValue="3">
			<isNotEmpty property="orgId">
				and t.MARKET_ID = '$orgId$'
			</isNotEmpty>
		</isEqual>
		<isEqual property="grade" compareValue="4">
			<isNotEmpty property="orgId">
				and t.GRID_ID = '$orgId$'
			</isNotEmpty>
		</isEqual>
		<isNotEqual property="roleId" compareValue="wgjl">
			and  t.CLASS_NAME in(select ROLE_NAME from pure_gs.pure_role where  ROLE_ID= '$roleId$') 
		</isNotEqual>
		
		order by t.AREA_ID,t.MARKET_ID,t.GRID_ID
	</select>

	<!-- 根据组织结构Id获取组织结构信息 -->
	<select id="getOrgById" resultClass="java.util.HashMap">
		select o.name TEXT, o.id ID, o.parent_id, '0' IS_ORG, o.grade GRADE, o.ord, o.areacode AREA_ID
		from pure_gs.ORG_ORGANIZATION_MOD_V16 o
		where o.id = '$orgId$' and rownum = 1
	</select>
    
</sqlMap>