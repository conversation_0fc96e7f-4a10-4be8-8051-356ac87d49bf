<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.costCenter.NewDevUserDeposit">

    <select id="selectArea" resultClass="java.util.HashMap">
		select t.area_no,t.area_desc from CODE_AREA t where 1=1 
		  <isNotEmpty prepend="and" property="areaNo">
			<isNotEqual property="areaNo" compareValue="">
			<isNotEqual property="areaNo" compareValue="087">
		      T.AREA_NO = '$areaNo$'
		   	</isNotEqual>
		   	</isNotEqual>
		 </isNotEmpty>
		order by t.ord
	</select>
	<select id="selectServiceType" resultClass="java.util.HashMap">
	 	select 'root' as "id", '全部' as "text", '' as "parentId", 1 "leaf"
	    	from dual
		union all
		select *
	 		from (select t.ID as "id",
	               t.NAME as "text",
	               t.PARENT_ID as "parentId",
	               (select decode(count(*), 0, 1, 0)
	                  from g_dmcode.g_dmcode_cost_retain_type b
	                 where b.PARENT_ID = t.ID) "leaf"
	        from g_dmcode.g_dmcode_cost_retain_type t
	        WHERE T.ID NOT IN(11,12)
	        START WITH T.PARENT_ID = 'root'
	        CONNECT BY PRIOR T.ID = T.PARENT_ID
	        ORDER SIBLINGS BY T.PARENT_ID)
	</select>
	<select id="getListById" resultClass="java.util.HashMap">	 
	 	 
	         
     select o.org_id,o.org_name,o.org_parent_id,o.org_parent_name,o.org_grade,
    
         PAY_0_NUM,PAY_0_LM_NUM,PAY_0_50_NUM,PAY_0_50_LM_NUM,PAY_50_80_NUM,PAY_50_80_LM_NUM,PAY_80_NUM,PAY_80_LM_NUM,NEW_DEV_NUM,NEW_DEV_LM_NUM,
         PAY_0_RATE,PAY_0_RATE_LM,PAY_50_RATE,PAY_50_RATE_LM,PAY_80_RATE,PAY_80_RATE_LM,
       (case
         when exists (select 1
                 from G_GA.G_GA_DEV_PAY_D b
                where o.org_id = b.ORG_PARENT_ID
                  and b.month_id ||b.day_id between
                      to_number('$dayId$') and
                      to_number('$dayId$')
                  ) then
          'false'
         else
          'true'
       end) "isLeaf"
    from G_GA.G_GA_DEV_PAY_D o
    
   where o.month_id ||o.day_id between
                      to_number('$dayId$') and
                      to_number('$dayId$')
     and o.org_id = '$orgIds$' 
     
     
     union all  
     select t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade, 
     
            
         PAY_0_NUM,PAY_0_LM_NUM,PAY_0_50_NUM,PAY_0_50_LM_NUM,PAY_50_80_NUM,PAY_50_80_LM_NUM,PAY_80_NUM,PAY_80_LM_NUM,NEW_DEV_NUM,NEW_DEV_LM_NUM,
         PAY_0_RATE,PAY_0_RATE_LM,PAY_50_RATE,PAY_50_RATE_LM,PAY_80_RATE,PAY_80_RATE_LM,
     
       (case
         when exists (select 1
                 from G_GA.G_GA_DEV_PAY_D b
                where t.org_id = b.ORG_PARENT_ID
                  and b.month_id||b.day_id between
                      to_number('$dayId$') and
                      to_number('$dayId$')
                  
                  ) then
          'false'
         else
          'true'
       end) "isLeaf"
  from (select * from G_GA.G_GA_DEV_PAY_D t1 order by t1.org_id) t
  
 where t.month_id ||t.day_id between
                      to_number('$dayId$') and
                      to_number('$dayId$')
   and t.org_parent_id = '$orgIds$'
   
   order by org_id  


	 	 
	 	 
	</select>
    <select id="getListByParentId" resultClass="java.util.HashMap">
	 
	   select t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade,
      
          PAY_0_NUM,PAY_0_LM_NUM,PAY_0_50_NUM,PAY_0_50_LM_NUM,PAY_50_80_NUM,PAY_50_80_LM_NUM,PAY_80_NUM,PAY_80_LM_NUM,NEW_DEV_NUM,NEW_DEV_LM_NUM,
         PAY_0_RATE,PAY_0_RATE_LM,PAY_50_RATE,PAY_50_RATE_LM,PAY_80_RATE,PAY_80_RATE_LM, 
   
   (case
         when exists (select 1
                 from G_GA.G_GA_DEV_PAY_D b
                where t.org_id = b.ORG_PARENT_ID
                  and (CONCAT(b.month_id ,b.day_id) between
                      to_number('$dayId$') and
                      to_number('$dayId$'))) then
         'false'
         else
         'true'
       end)  "isLeaf",
       
       (case
         when exists (select 1
                 from G_GA.G_GA_DEV_PAY_D b
                where t.org_id = b.ORG_PARENT_ID
                  and (CONCAT(b.month_id ,b.day_id) between
                      to_number('$dayId$') and
                      to_number('$dayId$'))) then
         'false'
         else
         'true'
       end)  "expanded"    
               
          
    from G_GA.G_GA_DEV_PAY_D t
    where (CONCAT(t.month_id ,t.day_id) between
                      to_number('$dayId$') and
                      to_number('$dayId$'))
    and t.org_parent_id='$orgIds$'    
    order by t.org_id  
   
   
	 </select>
    
    
</sqlMap>