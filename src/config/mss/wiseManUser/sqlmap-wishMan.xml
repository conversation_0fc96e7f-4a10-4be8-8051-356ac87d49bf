<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="mss.userManage.wishManUser">
	
	<!--地市列表-->
    <select id="listAreas" resultClass="java.util.HashMap" parameterClass="Map">
        select t.* from pure_gs.code_area t 
        <isNotEmpty property="code">
			<isNotEqual property="code" compareValue="">
				WHERE t.AREA_NO = '$code$'
			</isNotEqual>
		</isNotEmpty>
        order by t.ord asc
    </select>
    
    <!-- 查询智家工程师列表 -->
    <select id="selectWishManUsers" resultClass="java.util.HashMap">
    	select t.*,a.area_desc 
		from pure_gs.PURE_ZJGCS_USER_view t,
		     pure_gs.code_area a
		where t.area_no = a.area_no
		<isNotEmpty property="orgCode">
			<isNotEqual property="orgCode" compareValue="">
				<isNotEqual property="orgCode" compareValue="087">
					and (t.AREA_NO = '$orgCode$' or 
					     t.M_ID = '$orgCode$' or
					     t.G_ID = '$orgCode$' or
					     t.SALE_ID = '$orgCode$' )
				     </isNotEqual>
			</isNotEqual>
		</isNotEmpty>
		<isNotEmpty property="key">
			<isNotEqual property="key" compareValue="">
				and (t.USER_NAME like '%$key$%' or 
				     t.MOBILE like '%$key$%' or
				     t.ZB_EMAIL like '%$key$%' or
				     t.DEV_CODE like '%$key$%' )
				     
			</isNotEqual>
		</isNotEmpty>
		order by t.area_no,t.M_ID,t.G_ID,t.SALE_ID
    </select>
    
    <!-- 查询组织结构 -->
	<select id="getOrgList" resultClass="java.util.HashMap">
		select t.id as "id",
		       t.name as "text",
		       t.PARENT_ID as "parentId",
		       (select decode(count(*), 0, 1, 0)
		          from pure_gs.org_organization_mod b
		         where b.parent_id = t.id) "leaf"
		  from pure_gs.org_organization_mod t
		 where 1 = 1
		
		 START WITH T.id = '$orgCode$'
		CONNECT BY PRIOR T.id = T.PARENT_ID
		 ORDER SIBLINGS BY T.PARENT_ID
		
	</select>
	
	<!-- 智家工程师的发展人编码 -->
	<select id="getDevelopList" resultClass="java.util.HashMap">
		select a.mobile,
		       a.develop_no,
		       c.dev_code,
		       c.dev_name,
		       b.sale_id,
		       d.name,
		       e.id         grid_id,
		       e.name       grid_name
		  from G_GA.G_GA_SELE_MEMBER        a,
		       pure_gs.code_chnl_depart_all b,
		       DIM.DIM_DEVLOP               c,
		       pure_gs.org_organization_mod d,
		       pure_gs.org_organization_mod e
		 where a.develop_no = c.dev_code
		   and b.chnl_code = c.chnl_id
		   and b.sale_id = d.id
		   and d.parent_id = e.id
		   and a.mobile = '$mobile$'
		
	</select>
	
	<!-- 智家工程师的发展人编码 -->
	<select id="getDevelopInfo" resultClass="java.util.HashMap">
		select 
		      a.mobile, 
		      a.user_name, 
		      a.area_no,
		      listagg(a.dev_code, ',') within GROUP(order by a.dev_code) "devCode"
		  from pure_gs.PURE_ZJGCS_USER a
		 where a.mobile = '$mobile$'
		 group by a.mobile, 
		          a.user_name, 
		          a.area_no
		
	</select>
	
	<!-- 根据电话号码查询用户信息 -->
	<select id="getUserInfo" resultClass="java.util.HashMap">
		select * from pure_gs.pure_user a 
		where a.mobile = '$mobile$'
		and a.is_mp = '1'
		and a.state = '1' 
		
	</select>
	<!-- 新增智家工程师信息 -->
	<insert id="addWiseManInfo">
		insert into pure_gs.PURE_ZJGCS_USER
		(MOBILE,USER_NAME,DEV_CODE,AREA_NO,CREATE_ID,CREATE_TIME)
		values
		('$MOBILE$','$USER_NAME$','$DEV_CODE$','$AREA_NO$','$CREATE_ID$',sysdate)
	</insert>
	
	<!-- 查询当前发展人是否存在，存在进行修改，不存在新增 -->
	<select id="getIsHave" resultClass="java.util.HashMap">
		select * from pure_gs.PURE_ZJGCS_USER
		where mobile = '$MOBILE$'
		and dev_code = '$DEV_CODE$'
	</select>
	<!-- 修改智家工程师发展人信息 -->
	<update id="updataWiseManInfo">
		update pure_gs.PURE_ZJGCS_USER
		set UPDATE_ID = '$UPDATE_ID$',
			UPDATE_TIME = sysdate
		where mobile = '$MOBILE$'
		and DEV_CODE = '$DEV_CODE$'
	</update>
	
	<!-- 删除智家工程师信息 -->
	<delete id="delWiseMan">
		delete pure_gs.PURE_ZJGCS_USER
		where mobile = '$mobile$'
		and dev_code = '$developNo$'
	</delete>
	
	
	<delete id="delWiseManByMobile">
		delete pure_gs.PURE_ZJGCS_USER
		where mobile = '$MOBILE$'
		and dev_code not in ($devCodes$)
	</delete>
</sqlMap>
