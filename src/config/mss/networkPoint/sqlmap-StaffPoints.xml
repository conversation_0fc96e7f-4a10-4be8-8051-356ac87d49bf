<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="mss.networkPoint.StaffPoints">

<!-- 查询地市列表 -->






	<select id="getAreas" resultClass="java.util.HashMap">
		<isNotEmpty property="areaNo">
		<isEqual property="areaNo" compareValue="087">
		SELECT '087' AREA_NO, '全省' AREA_NAME, -1 ORD
  		FROM DUAL

		UNION ALL

		SELECT AREA_NO, AREA_NAME, ORD
 		 FROM G_GA.G_GA_WZ_AREA T
 		ORDER BY ORD
 		</isEqual>
 		</isNotEmpty>
 		  
 		 <isNotEmpty property="areaNo">
		  	<isNotEqual property="areaNo" compareValue="087">  
		  	
		  	SELECT AREA_NO, AREA_NAME, ORD  
 		 FROM G_GA.G_GA_WZ_AREA T where t.area_no='$areaNo$'  or ('$areaNo$'='8700' and t.area_no in ('8700','8690') )
 		ORDER BY ORD
		 		 
		  	</isNotEqual>
		  </isNotEmpty>	
		  
		  
		  
	</select>
	

  
	
	

  

	<!-- 营业员 -->
   <select id="selectDepResult" resultClass="java.util.HashMap">
   
   select t2.area_name,t.staff_name, t.staff_id,  t.mobile,'$monthId$' MONTH_ID, nvl(t1.score,0) score 
  from G_GA.G_GA_WZ_STAFF t,
       
       (select t.staff_id, sum(l.task_score) score
          from G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_PROJECT_TASK_STAFF t,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where p.project_id = t.project_id
           and t.task_id = l.task_id
           and p.project_status = '1'
           and to_char(p.project_end_time) between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))  
         group by t.staff_id) t1,
       G_GA.G_GA_WZ_AREA t2
 where t.staff_id = t1.staff_id(+)
   and t.area_no = t2.area_no
  
   
         <isNotEmpty property="areaNo">
		  	<isNotEqual property="areaNo" compareValue="087">
		 		and t.area_no='$areaNo$'
		  	</isNotEqual>
		  </isNotEmpty>	
		  
    	<isNotEmpty property="selectId">
		  	<isNotEqual property="selectId" compareValue="">
		 		and t.staff_id like '%' || trim(#selectId#) || '%' 	
		  	</isNotEqual>
		  </isNotEmpty>	

		  <isNotEmpty property="selectName">
		  	<isNotEqual property="selectName" compareValue="">
		 		and t.staff_name like '%' || trim(#selectName#) || '%' 	
		  	</isNotEqual>
		  </isNotEmpty>
		  
		  
 order by t2.ord, to_number(t1.score) 
   
   
   
   
   
   
   
  
    
   
     
  </select>
  

  
    
  
  
 
   <select id="getUserResult" resultClass="java.util.HashMap">
 
		  

	select 
       p.project_name, 
       l.task_name,   
       l.task_score,
       l.skill_level,
       l.org_name,
       s.staff_id,
       s.staff_name 
  from G_GA.G_GA_WZ_STAFF              s,
       G_GA.G_GA_WZ_PROJECT_INFO       p,
       G_GA.G_GA_WZ_PROJECT_TASK_STAFF t,
       G_GA.G_GA_WZ_TASK_LISTS         l 

 where p.project_id = t.project_id
   and t.task_id = l.task_id
   and p.project_status = '1'
   and s.staff_id = t.staff_id
   and to_char(p.project_end_time) between
       trunc(add_months(last_day(to_date('$monthId$', 'yyyymm')), -1) + 1) and
       last_day(to_date('$monthId$', 'yyyymm'))
   and t.staff_id ='$loginId$'	  
		  
		  

   
   
   
   
   
   
  
   </select>
  
  



</sqlMap>