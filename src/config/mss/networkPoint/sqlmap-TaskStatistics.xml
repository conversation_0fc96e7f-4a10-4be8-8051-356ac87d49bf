<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="mss.networkPoint.TaskStatistics">

	<select id="getData" resultClass="java.util.HashMap">
            select * from (
            SELECT
            '甘肃省'                            AREA_NAME       ,
            '087'                               AREA_NO         ,
            NVL(X2.PROJECT_NUMBER   , 0)        PROJECT_NUMBER  ,
            NVL(TASK_NUMBER         , 0)        TASK_NUMBER     ,
            NVL(TASK_1_NUMBER       , 0)        TASK_1_NUMBER   ,
            NVL(TASK_2_NUMBER       , 0)        TASK_2_NUMBER   ,
            NVL(TASK_3_NUMBER       , 0)        TASK_3_NUMBER   ,
            NVL(EXEC_ZG_TASK_COVER_RATE, 0)     EXEC_ZG_TASK_COVER_RATE
            FROM  (
            SELECT
            COUNT(DISTINCT T1.PROJECT_ID)                           PROJECT_NUMBER  ,
            COUNT(T2.TASK_ID)                                       TASK_NUMBER     ,
            SUM(CASE WHEN T3.TASK_SCORE = 1 THEN 1 ELSE 0 END)      TASK_1_NUMBER   ,
            SUM(CASE WHEN T3.TASK_SCORE = 2 THEN 1 ELSE 0 END)      TASK_2_NUMBER   ,
            SUM(CASE WHEN T3.TASK_SCORE = 3 THEN 1 ELSE 0 END)      TASK_3_NUMBER   ,
            CASE WHEN COUNT(T2.TASK_ID) = 0 THEN 0
            ELSE ROUND((SUM(CASE WHEN T3.TASK_SCORE IN (2, 3) THEN 1 ELSE 0 END)/ COUNT(T2.TASK_ID) * 100), 2)
            END                                                     TASK_ZG_RATE    ,
            COUNT(DISTINCT CASE WHEN T3.TASK_SCORE IN (2, 3) THEN T2.TASK_ID END)   EXEC_ZG_TASK_NUM,
            MAX(ZG_TASK_NUM) ZG_TASK_NUM,
            CASE WHEN MAX(ZG_TASK_NUM) = 0 THEN 0
            ELSE ROUND(COUNT(DISTINCT CASE WHEN T3.TASK_SCORE IN (2, 3) THEN T2.TASK_ID END)/MAX(ZG_TASK_NUM)*100, 2)
            END EXEC_ZG_TASK_COVER_RATE

            FROM G_GA.G_GA_WZ_PROJECT_INFO T1,
            G_GA.G_GA_WZ_PROJECT_TASK T2,
            G_GA.G_GA_WZ_TASK_LISTS T3,
            (
            SELECT COUNT(DISTINCT T4.TASK_ID) ZG_TASK_NUM FROM G_GA.G_GA_WZ_TASK_LISTS T4 WHERE T4.TASK_SCORE IN (2, 3)
            )
            WHERE T1.PROJECT_ID = T2.PROJECT_ID
            AND TO_CHAR(T1.CREATE_TIME, 'YYYYMM') = '$monthId$'
            AND T2.TASK_ID = T3.TASK_ID
            ) X2
            UNION ALL
            SELECT
            X1.AREA_NAME                        AREA_NAME       ,
            X1.AREA_NO                          AREA_NO         ,
            NVL(X2.PROJECT_NUMBER   , 0)        PROJECT_NUMBER  ,
            NVL(TASK_NUMBER         , 0)        TASK_NUMBER     ,
            NVL(TASK_1_NUMBER       , 0)        TASK_1_NUMBER   ,
            NVL(TASK_2_NUMBER       , 0)        TASK_2_NUMBER   ,
            NVL(TASK_3_NUMBER       , 0)        TASK_3_NUMBER   ,
            NVL(EXEC_ZG_TASK_COVER_RATE, 0)     EXEC_ZG_TASK_COVER_RATE
            FROM  G_GA.G_GA_WZ_AREA X1
            LEFT JOIN (
            SELECT
            T1.AREA_NO                                              AREA_NO         ,
            COUNT(DISTINCT T1.PROJECT_ID)                           PROJECT_NUMBER  ,
            COUNT(T2.TASK_ID)                                       TASK_NUMBER     ,
            SUM(CASE WHEN T3.TASK_SCORE = 1 THEN 1 ELSE 0 END)      TASK_1_NUMBER   ,
            SUM(CASE WHEN T3.TASK_SCORE = 2 THEN 1 ELSE 0 END)      TASK_2_NUMBER   ,
            SUM(CASE WHEN T3.TASK_SCORE = 3 THEN 1 ELSE 0 END)      TASK_3_NUMBER   ,
            CASE WHEN COUNT(T2.TASK_ID) = 0 THEN 0
            ELSE ROUND((SUM(CASE WHEN T3.TASK_SCORE IN (2, 3) THEN 1 ELSE 0 END)/ COUNT(T2.TASK_ID) * 100), 2)
            END                                                     TASK_ZG_RATE    ,
            COUNT(DISTINCT CASE WHEN T3.TASK_SCORE IN (2, 3) THEN T2.TASK_ID END)   EXEC_ZG_TASK_NUM,
            MAX(ZG_TASK_NUM) ZG_TASK_NUM,
            CASE WHEN MAX(ZG_TASK_NUM) = 0 THEN 0
            ELSE ROUND(COUNT(DISTINCT CASE WHEN T3.TASK_SCORE IN (2, 3) THEN T2.TASK_ID END)/MAX(ZG_TASK_NUM)*100, 2)
            END EXEC_ZG_TASK_COVER_RATE

            FROM G_GA.G_GA_WZ_PROJECT_INFO T1,
            G_GA.G_GA_WZ_PROJECT_TASK T2,
            G_GA.G_GA_WZ_TASK_LISTS T3,
            (
            SELECT COUNT(DISTINCT T4.TASK_ID) ZG_TASK_NUM FROM G_GA.G_GA_WZ_TASK_LISTS T4 WHERE T4.TASK_SCORE IN (2, 3)
            )
            WHERE T1.PROJECT_ID = T2.PROJECT_ID
            AND TO_CHAR(T1.CREATE_TIME, 'YYYYMM') = '$monthId$'
            AND T2.TASK_ID = T3.TASK_ID
            GROUP BY T1.AREA_NO
            ) X2 ON X1.AREA_NO = X2.AREA_NO
            )
            order by 2
    </select>


  

  
    
  
  
 
  
  



</sqlMap>