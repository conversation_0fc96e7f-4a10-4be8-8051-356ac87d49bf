<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.networkPoint.taskBank">

	<!--查询数据字典信息 -->
	<select id="queryDataDictList" resultClass="java.util.HashMap">
	
	 select *
  from (select t.task_id as "id",
               t.task_name_jb as "text",
               t.task_parent_id as"parentId",
               t.task_id as "ord",
               '1' "leaf"
          from G_GA.G_GA_WZ_TASK_LISTS t where  t.task_parent_id !='root'   
         order by t.task_id)
union all      
select *
  from (select t.task_id as "id",
               t.task_name_jb as "text",
              t.task_parent_id as "parentId",
               t.task_id as "ord",
               '0' "leaf"
          from G_GA.G_GA_WZ_TASK_LISTS t where t.task_parent_id='root'
         order by t.task_id
        
        )
	</select>
	
	
	
	
	<!-- 插入数据字典 -->
	<insert id="saveData" parameterClass="Map">
	
          insert into G_GA.G_GA_WZ_TASK_LISTS(TASK_ID,TASK_NAME_JB,TASK_PARENT_ID,TASK_SCORE,SKILL_LEVEL,IS_TYPE)
	   	  values(#id#,'$taskName$','$parentId$','$taskScore$','$taskLevel$','$isType$')
	</insert>
	
	
	
	
	
	<!-- 更新资源信息 -->
	<update id="updateData" parameterClass="java.util.HashMap">
		update G_GA.G_GA_WZ_TASK_LISTS    
		set TASK_NAME_JB='$taskName$',TASK_SCORE='$taskScore$',SKILL_LEVEL='$taskLevel$',IS_TYPE='$isType$'
		where TASK_ID='$id$'  
	</update>
	
	
	
	
	
	<!-- 根据节点ID获取节点信息 -->
	<select id="getDataInfo" resultClass="java.util.HashMap">
		
select d.*,
       d.task_name_jb as "text",
       d.task_id      as "id",
       d.task_id             as "ord",
       d.task_parent_id     as "parentId" 
     
  from G_GA.G_GA_WZ_TASK_LISTS d
 where d.task_id = '$id$'   
  
	</select>
	
	<!-- 更具节点ID删除数据 -->
	<delete id="deleteData">
		delete from G_GA.G_GA_WZ_TASK_LISTS r where r.TASK_ID = '$id$'  
	</delete>
	
	
	
	
	
	
	
	
	
</sqlMap>