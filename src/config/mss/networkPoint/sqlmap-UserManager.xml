<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.networkPoint.UserManager">

	
	<!-- 1.商机操作日志记录信息 -->
	<select id = "hrUserManagerList" resultClass="java.util.HashMap">
	
	 select t.*, a.area_name  
    from G_GA.G_GA_WZ_STAFF t, G_GA.G_GA_WZ_AREA a  
 where t.area_no = a.area_no  
         <isNotEmpty property="areaNo">
        <isNotEqual property="areaNo" compareValue="087">
        and t.area_no='$areaNo$'   
        </isNotEqual>
      </isNotEmpty> 
      <isNotEmpty property="key">
        <isNotEqual property="key" compareValue="">
        and t.staff_name like '%' || trim(#key#) || '%'  
        </isNotEqual>
      </isNotEmpty> 
      
       order by a.ord
		 
	</select>
	
	<!-- 插入hr人员信息 -->
	<insert id="addHrUserInfo">
		insert into G_GA.G_GA_WZ_STAFF
		 (  AREA_NO,
			STAFF_ID,
			STAFF_NAME,
			MOBILE
		  )
		values
		 (
		  #AREA_NO#,
	      #STAFF_ID#,
	      #STAFF_NAME#,
	      #MOBILE#
		 )
	</insert>
	
	
	
	<!-- 插入hr人员信息 -->
	<insert id="updateHrUser">
		 update G_GA.G_GA_WZ_STAFF   
		set	STAFF_NAME = '$STAFF_NAME$',  
			MOBILE = '$MOBILE$',
		AREA_NO = '$AREA_NO$'
		where STAFF_ID ='$STAFF_ID$'   
		
	</insert>
	
	
	
	
	<!-- 更新领导信息 -->
	<update id="saveUserUpdate" >
		update pure_gs.pure_hr_user_message b
		set b.IS_LEADER = #isLeader#
		where b.HR_ID = #hrId#
		and b.DEPART_ID = #departId#
		and b.IS_REMOVE = '0'
	</update>
	
	<!-- 设置当前部门所有人状态为可用 -->
	<update id="updateUserListByDepartIdEnabled">
		update pure_gs.pure_hr_user_message b
		set b.IS_ENABLE = '1'
		where b.DEPART_ID = #departId#
		and b.IS_ENABLE = '0'
		and b.IS_REMOVE = '0'
	</update>
	
	
	<!-- 设置当前部门所有人状态为不可用 -->
	<update id="updateUserListByDepartIdDisabled">
		update pure_gs.pure_hr_user_message b
		set b.IS_ENABLE = '0'
		where b.DEPART_ID = #departId#
		and b.IS_ENABLE = '1'
		and b.IS_REMOVE = '0'
	</update>
	
	<!-- 2021-05-08 -->
	<update id="updateHRUserEnable">
		update pure_gs.pure_hr_user_message b
		set b.IS_ENABLE = '1'
		where b.DEPART_ID = #departId#
		and b.MOBILE = #mobile#
		and b.IS_REMOVE = '0'
	</update>
	<!-- 查询当前部门是否有领导 -->
	<select id="getDepartLeaderList" resultClass="java.util.HashMap">
		select c.* 
		from pure_gs.pure_hr_user_message c
		where c.DEPART_ID = #departId#
		and c.is_leader = '1'
		and c.is_remove = '0'
	</select>
	
	<!-- 查询部门 -->
	<select id="getDepartList" resultClass="java.util.HashMap">
		<isEqual property="areaNo" compareValue="087">
			select t.depart_id   as "id",
			        t.depart_name as "text",
			         t.PARENT_ID   as "parentId",
			         1             "leaf"
			          from pure_gs.pure_hr_user_org t
			         where depart_id = 'root'
			        union all
		</isEqual>
		
        select *
          from (select t.depart_id as "id",
                 t.depart_name as "text",
                 t.PARENT_ID as "parentId",
                 (select decode(count(*), 0, 1, 0)
                    from pure_gs.pure_hr_user_org b
                   where b.PARENT_ID = t.depart_id) "leaf"
            from pure_gs.pure_hr_user_org t
            where 1 = 1
            <isNotEmpty prepend="and" property="areaNo">
			  <isNotEqual property="areaNo" compareValue="">
				  <isNotEqual property="areaNo" compareValue="087">
				  		 <isNotEqual property="areaNo" compareValue="root">
			      			t.area_no = '$areaNo$'
			      		</isNotEqual>
			   	  </isNotEqual>
		   	  </isNotEqual>
	      </isNotEmpty>
           START WITH T.PARENT_ID = 'root'
          CONNECT BY PRIOR T.depart_id = T.PARENT_ID
           ORDER SIBLINGS BY T.PARENT_ID)
		
	</select>
	
	
	<!-- 移除HR人员 -->
	<update id="removeHrUser">
	
	delete from G_GA.G_GA_WZ_STAFF t where t.staff_id='$hrId$'  
		
	</update>
	
	
	<!-- 地市 -->
	<select id="selectArea" resultClass="java.util.HashMap">
		select t.area_no,t.area_desc from CODE_AREA t where 1=1      
		  <isNotEmpty prepend="and" property="areaNo">
			<isNotEqual property="areaNo" compareValue="">
			<isNotEqual property="areaNo" compareValue="087">
		      T.AREA_NO = '$areaNo$'
		   	</isNotEqual>
		   	</isNotEqual>
		 </isNotEmpty>
		order by t.ord
	</select>
	
	<!-- 查询职务 -->
	<select id="selectRoleList" resultClass="java.util.HashMap">
		select * from g_dmcode.g_dmcode_hr_role d  order by d.role_id
	</select>
	
	
	<!-- 查询类型 -->
	<select id="selectUserTypeList" resultClass="java.util.HashMap">
		select * from g_dmcode.PURE_HR_USER_MESSAGE_TYPE d  order by d.HR_USER_TYPE_CODE
	</select>
	
	<!-- 查询营销工号 -->
	<select id="selectYFLoginId" resultClass="java.util.HashMap">
		  select t.*,a.jname,a.orgrank,b.area_desc
	      from PURE_gs.pure_user t,
	           PURE_gs.org_organization_mod a,
	           pure_gs.code_area b
	     where t.SALE_AREA = a.id
	     and b.area_no = t.area_no
	     and t.state = '1'
	     and t.is_mp = '1'
	     <isNotEmpty prepend="and" property="zbEmail">
	     	 <isNotEmpty property="mobile">
	     	 (t.zb_email = #zbEmail# or t.mobile = #mobile#)
	     	 </isNotEmpty>
	     </isNotEmpty>
	     <isEmpty prepend="and" property="zbEmail">
	     	  t.mobile = #mobile#
	     </isEmpty>
		<isEmpty prepend="and" property="mobile">
	     	  t.zb_email = #zbEmail#
	     </isEmpty>
	</select>
	
	
	
	<!-- 根据OA账号或电话号码查询对应的所有营服工号 -->
	<select id="selectAllYFLoginId" resultClass="java.util.HashMap">
		select  to_char(replace(wm_concat(t.login_id),',','|')) YF_ALL_LOGIN_ID,
        		to_char(replace(wm_concat(t.sale_area),',','|')) YF_ALL_SAIL_ID  
		from pure_gs.pure_user t
		where (t.zb_email = #zbEmail# or t.mobile = #mobile#)
		and t.state = '1'
	</select>
	
	
	<!-- 根据营服工号查询营服信息 -->
	<select id="selectYFUserInfo" resultClass="java.util.HashMap">
		select t.login_id YF_LOGIN_ID,
		       t.sale_area YF_SALE_ID, 
		       t.area_no YF_LOGIN_ID_AREA_NO,
		       o.jname YF_SALES_JNAMES
		from pure_gs.pure_user t,
     		 pure_gs.org_organization_mod o
        where t.login_id = #yfLoginId#
        and t.sale_area = o.id
        and t.state = '1'
	</select>
	
	
	
	<!-- 根据部门ID查询部门信息 -->
	<select id="selectDepartInfo" resultClass="java.util.HashMap">
		select * 
		from pure_gs.pure_hr_user_org b
		where b.depart_id = #departId#
	</select>
	
	
	<!-- 查询HR人员信息 -->
	<select id="selectHrUserInfo" resultClass="java.util.HashMap">
		select * 
		from G_GA.G_GA_WZ_STAFF b
		where 1 = 1
		and  b.STAFF_ID = #oaLoginId#  
		
	</select>



	<!-- 查询STAFF_ID是否存在 -->
	<select id="selectStaffId" resultClass="java.util.HashMap">
		select *
		from G_GA.G_GA_WZ_STAFF b
		where 1 = 1
		and  b.STAFF_ID = #staffId#

	</select>
	
</sqlMap>