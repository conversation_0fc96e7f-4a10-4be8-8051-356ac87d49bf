<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="mss.networkPoint.AreaNetworkPoints">

<!-- 查询地市列表 -->






	<select id="getAreas" resultClass="java.util.HashMap">
		<isNotEmpty property="areaNo">
		<isEqual property="areaNo" compareValue="087">
		SELECT '087' AREA_NO, '全省' AREA_NAME, -1 ORD
  		FROM DUAL

		UNION ALL

		SELECT AREA_NO, AREA_NAME, ORD
 		 FROM G_GA.G_GA_WZ_AREA T
 		ORDER BY ORD
 		</isEqual>
 		</isNotEmpty>
 		  
 		 <isNotEmpty property="areaNo">
		  	<isNotEqual property="areaNo" compareValue="087">  
		  	
		  	SELECT AREA_NO, AREA_NAME, ORD  
 		 FROM G_GA.G_GA_WZ_AREA T where t.area_no='$areaNo$'  
 		ORDER BY ORD
		 		 
		  	</isNotEqual>
		  </isNotEmpty>	
		  
		  
		  
	</select>
	

  
	
	

  

	<!-- 营业员 -->
   <select id="selectDepResult" resultClass="java.util.HashMap">
   
   select a.area_no,
       a.area_name,
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1'
           and to_char(p.project_end_time) between
               trunc(add_months(last_day(to_date('$monthId$', 'yyyymm')), -1) + 1) and 
               last_day(to_date('$monthId$', 'yyyymm'))
           and p.area_no = a.area_no) SCORE,
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1'
           and to_char(p.project_end_time) between
               trunc(add_months(last_day(to_date('$monthId$', 'yyyymm')), -1) + 1) and 
               last_day(to_date('$monthId$', 'yyyymm'))
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '01'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_1,
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1'
                and to_char(p.project_end_time) between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))     
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '02'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_2,
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1' 
                and to_char(p.project_end_time) between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))     
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '03'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_3,
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1' 
                and to_char(p.project_end_time)  between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))     
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '04'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_4,
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1' 
                and to_char(p.project_end_time)  between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))     
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '05'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_5,
       
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1' 
                and to_char(p.project_end_time)  between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))    
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '06'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_6,
       
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1'  
                and to_char(p.project_end_time) between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))     
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '07'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_7,
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1' 
                and to_char(p.project_end_time) between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))     
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '08'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_8,
       
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1'  
                and to_char(p.project_end_time)  between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))     
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '09'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_9,
       
       (select nvl(sum(l.task_score), 0)
          from G_GA.G_GA_WZ_PROJECT_TASK_STAFF o,
               G_GA.G_GA_WZ_PROJECT_INFO       p,
               G_GA.G_GA_WZ_TASK_LISTS         l
         where o.task_id = l.task_id
           and p.project_id = o.project_id
           and p.project_status = '1' 
                and to_char(p.project_end_time)  between
           trunc(add_months(last_day(to_date('$monthId$','yyyymm')), -1) + 1)   
            and last_day(to_date('$monthId$','yyyymm'))     
           and o.task_id in
               (SELECT task_id
                  FROM G_GA.G_GA_WZ_TASK_LISTS
                 START WITH task_parent_id = '10'
                CONNECT BY PRIOR task_id = task_parent_id)
           and p.area_no = a.area_no) score_10

  from G_GA.G_GA_WZ_AREA a

 group by a.area_no, a.area_name, a.ord

 order by ord   
   
   
     
  </select>
  

  
    
  
  
 
  
  



</sqlMap>