<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.networkPoint.Project">
    <!-- 地市列表 -->
    <select id="queryAreas" resultClass="java.util.HashMap">
        select t.* from G_GA.G_GA_WZ_AREA t order by t.ORD
    </select>
    <!-- 运维人员 -->
    <select id="queryStaff" resultClass="java.util.HashMap">
        select t.* from G_GA.G_GA_WZ_STAFF t where 1=1
        <isNotEmpty prepend="and" property="areaNo">
            t.AREA_NO = '$areaNo$'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="staffName">
            t.STAFF_NAME like '%$staffName$%'
        </isNotEmpty>

    </select>
    <!-- 项目列表 -->
    <select id="queryProjects" resultClass="java.util.HashMap">
        select a.*, b.AREA_NAME,c.USER_NAME
        from g_ga.G_GA_WZ_PROJECT_INFO a
        left join g_ga.G_GA_WZ_AREA b
        on a.AREA_NO = b.AREA_NO
        left join pure_gs.PURE_USER c
        on a.PROJECT_CEO = c.LOGIN_ID
        where 1 =1
        <isNotEmpty prepend="and" property="proId">
            a.PROJECT_ID = '$proId$'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="areaNo">
            a.AREA_NO = '$areaNo$'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="proState">
            a.PROJECT_STATUS = '$proState$'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="proName">
            a.PROJECT_NAME like '%$proName$%'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="ceoId">
            a.PROJECT_CEO = '$ceoId$'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startDate">
            a.CREATE_TIME between to_date(substr('$startDate$',0,10) || ' 00:00:00','yyyy-mm-dd hh24:mi:ss')
            and to_date(substr('$startDate$',14,11) || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDate">
            a.PROJECT_END_TIME between to_date(substr('$endDate$',0,10) || ' 00:00:00','yyyy-mm-dd hh24:mi:ss')
            and to_date(substr('$endDate$',14,11) || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
        </isNotEmpty>
        order by a.CREATE_TIME desc
    </select>
    <!-- 查询任务和人员 -->
    <select id="queryTaskStaffs" resultClass="java.util.HashMap">
        select a.*,b.TASK_NAME,b.TASK_SCORE,c.STAFF_NAME
        from G_GA.G_GA_WZ_PROJECT_TASK_STAFF a
        left join G_GA.G_GA_WZ_TASK_LISTS b
        on a.TASK_ID = b.TASK_ID
        left join G_GA.G_GA_WZ_STAFF c
        on a.STAFF_ID = c.STAFF_ID
        where a.PROJECT_ID = '$proId$'
    </select>
    <!-- 查询附件 -->
    <select id="queryAttachs" resultClass="java.util.HashMap">
        select * from G_GA.G_GA_WZ_PROJECT_ATTACH where 1=1
        <isNotEmpty prepend="and" property="proId">
            PROJECT_ID = '$proId$'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="fileId">
            FILE_ID = '$fileId$'
        </isNotEmpty>
    </select>
    <!-- 根据当前登录人loginId查询是否小ceo -->
    <select id="queryCeo" resultClass="java.util.HashMap">
        select a.login_id from  pure_gs.pure_user a inner join  pure_gs.pure_user_role b
        on a.USER_ID = b.USER_ID and b.ROLE_ID = 'dishi_wlb_ceo' where a.login_id = '$loginId$'
    </select>
    <!-- 查询任务树 -->
    <select id="queryTaskTree" resultClass="java.util.HashMap">
        select * from G_GA.G_GA_WZ_TASK_LISTS
    </select>
    <!-- 更新项目状态 -->
    <update id="updateState">
        update G_GA.G_GA_WZ_PROJECT_INFO t set t.PROJECT_STATUS = '$proState$'
        <isEqual property="proState" compareValue="1">
            ,t.PROJECT_END_TIME = sysdate
        </isEqual>
        <isEqual property="proState" compareValue="0">
            ,t.PROJECT_END_TIME = null
        </isEqual>
        where t.PROJECT_ID = '$proId$'
    </update>
    <!-- 更新项目信息 -->
    <!--<update id="updatePro">
        update G_GA.G_GA_WZ_PROJECT_INFO t set t.PROJECT_STATUS = '$proState$' where t.PROJECT_ID = '$proId$'
    </update>-->
    <!-- 新增项目信息 -->
    <insert id="addPro">
        insert into G_GA.G_GA_WZ_PROJECT_INFO
        (PROJECT_ID, PROJECT_NAME, AREA_NO, PROJECT_CEO,CREATE_TIME,PROJECT_STATUS,PROJECT_TYPE)
        values
        ('$proId$', '$proName$', '$areaNo$', '$creater$', sysdate, '0','$proType$')
    </insert>
    <!-- 新增项目、任务-->
    <insert id="addProTask">
        insert into G_GA.G_GA_WZ_PROJECT_TASK
        (PROJECT_ID, TASK_ID, TASK_CREATE_TIME)
        values
        ('$proId$', '$taskId$', sysdate)
    </insert>
    <!-- 新增项目、任务 、人员-->
    <insert id="addTaskStaff">
        insert into G_GA.G_GA_WZ_PROJECT_TASK_STAFF
        (PROJECT_ID, TASK_ID, STAFF_ID)
        values
        ('$proId$', '$taskId$', '$staffId$')
    </insert>
    <!-- 新增附件-->
    <insert id="addAttach">
        insert into G_GA.G_GA_WZ_PROJECT_ATTACH
        (PROJECT_ID, FILE_ID, FILE_PATH, FILE_TIME, FILE_SUFFIX, FILE_ORI_NAME, FILE_NEW_NAME)
        values
        ('$proId$', '$fileId$', '$filePath$', to_date('$filetime$','yyyy-mm-dd hh24:mi:ss'), '$suffix$', '$fileOriginalName$', '$newFileName$')
    </insert>
    <!-- 删除项目 -->
    <delete id="delPro">
        delete from G_GA.G_GA_WZ_PROJECT_INFO t where t.PROJECT_ID = '$proId$'
    </delete>
    <!-- 删除项目、任务-->
    <delete id="delProTask">
        delete from G_GA.G_GA_WZ_PROJECT_TASK t where t.PROJECT_ID = '$proId$'
    </delete>
    <!-- 删除项目、任务、人员-->
    <delete id="delTaskStaff">
        delete from G_GA.G_GA_WZ_PROJECT_TASK_STAFF t where t.PROJECT_ID = '$proId$'
    </delete>
    <!-- 删除附件-->
    <delete id="delAttach">
        delete from G_GA.G_GA_WZ_PROJECT_ATTACH t where t.PROJECT_ID = '$proId$'
    </delete>

</sqlMap>
