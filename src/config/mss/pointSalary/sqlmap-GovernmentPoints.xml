<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.pointSalary.GovernmentPoints">

	<!-- 商企楼长、政企要客经理 -->
	<select id="selectRoleList" resultClass="java.util.HashMap">
		select PRP_ROLE as ROLE_ID,PRP_ROLE_DESC as ROLE_DESC from G_GA.G_GA_NPRP_ROLE where prp_role in('zqkhjl','zqykjl')
</select>
<!-- 根据组织结构Id获取组织结构信息 -->
	<select id="getOrgById" resultClass="java.util.HashMap">
		select o.name TEXT, o.id ID, o.parent_id, '0' IS_ORG, o.grade GRADE, o.ord, o.areacode AREA_ID
		from pure_gs.ORG_ORGANIZATION_MOD_V16 o
		where o.id = '$orgId$' and rownum = 1
	</select>
	
	<!-- 查询个人列表 -->
	<select id="queryPerlList" resultClass="java.util.HashMap">
		select *  from G_GA.G_GA_NPRP_SCORE_ZQDTL_M t
		where t.month_id = substr('$monthId$',0,6) 
		<isEqual property="grade" compareValue="2">
			<isNotEmpty property="orgId">
				and t.AREA_ID = '$orgId$'
			</isNotEmpty>
		</isEqual>
		<isEqual property="grade" compareValue="3">
			<isNotEmpty property="orgId">
				and t.MARKET_ID = '$orgId$'
			</isNotEmpty>
		</isEqual>
		<isEqual property="grade" compareValue="4">
			<isNotEmpty property="orgId">
				and t.GRID_ID = '$orgId$'
			</isNotEmpty>
		</isEqual>
		<isEqual property="grade" compareValue="5">
			<isNotEmpty property="orgId">
				and t.SALE_ID = '$orgId$'
			</isNotEmpty>
		</isEqual>
		<isNotEqual property="roleId" compareValue="all">
			and  t.PRP_ROLE in ('$roleId$')
		</isNotEqual>
		<isNotEmpty property="objId">
		<isNotEqual property="objId" compareValue="">
			and  t.obj_id = '$objId$'
		</isNotEqual>
		</isNotEmpty>
		order by t.AREA_ID,t.MARKET_ID,t.GRID_ID
	</select>

	<select id="queryRole" resultClass="java.util.HashMap">
		select *
		from (select a2.user_id,a.prp_role role_id,a.obj_id
		from g_ga.g_ga_nprp_obj_m a
		left join pure_gs.pure_user a2
		on a.obj_id = a2.login_id
		join g_ga.g_ga_nprp_role a1
		on a.prp_role = a1.prp_role
		join (select r.role_id
		from pure_gs.PURE_ROLE r
		where r.IS_READ_SELF = '1') r
		on a1.role_id = r.role_id
		where a.month_id=substr('$monthId$',0,6)) where
		<isNotEmpty property="userId">
			user_id='$userId$'
		</isNotEmpty>
	</select>
	
    
</sqlMap>