<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.pointSalary.SalaryScoringMonth">

    <select id="selectArea" resultClass="java.util.HashMap">
		select t.area_no,t.area_desc from PURE_GS.REGION t where 1=1
		  <isNotEmpty prepend="and" property="areaNo">
			<isNotEqual property="areaNo" compareValue="">
			<isNotEqual property="areaNo" compareValue="087">
		      T.AREA_NO = '$areaNo$'
		   	</isNotEqual>
		   	</isNotEqual>
		 </isNotEmpty>
		order by t.ord
	</select>
	
	
	
	 <!-- 查询组织结构 -->
    <select id="getOrg" resultClass="java.util.HashMap">
        select a.* from
        ( select o.name TEXT, o.id ID, o.parent_id, '0' IS_ORG, o.grade, o.ord
        from PURE_GS.ORG_ORGANIZATION_MOD_V16_M o
        where o.month_id = '$monthId$'
        <isNotEmpty prepend="and" property="sellArea">
            o.id = '$sellArea$'
        </isNotEmpty>
        and o.grade &lt;= 5
        union all
        select o.name TEXT,
        o.id ID,
        o.parent_id,
        '0' IS_ORG,
        o.grade,
        o.ord
        from (select * from PURE_GS.ORG_ORGANIZATION_MOD_V16_M t
        where t.month_id='$monthId$'
        and t.grade &lt;= 5 ) o
        CONNECT BY PRIOR o.id = o.parent_id
        START WITH
        <isNotEmpty  property="sellArea">
            o.parent_id = '$sellArea$'
        </isNotEmpty>
        ) a
        order by a.grade, a.ord
    </select>
    <!-- 查询全部角色 -->
    <select id="selectRoleList" resultClass="java.util.HashMap">
        select 'all' as ROLE_ID,'全部' as ROLE_DESC from dual
        union all
        select PRP_ROLE as ROLE_ID,PRP_ROLE_DESC as ROLE_DESC from G_GA.G_GA_NPRP_ROLE
    </select>

	
	<select id="getListById" resultClass="java.util.HashMap">

 	 select org_id,
       org_name,
       org_parent_id,
       org_parent_name,
       org_grade,
       sum(OBJ_NUM) as OBJ_NUM,
       round(sum(ALL_SCORE),2) as ALL_SCORE,
       round(sum(XS_SCORE),2) as XS_SCORE,
        round(sum(GXH_SCORE),2) as GXH_SCORE,
       round(sum(CLWX_SCORE),2) as CLWX_SCORE,
       round(sum(SL_SCORE),2) as SL_SCORE,
       round(sum(KF_SCORE),2) as KF_SCORE,
       round(sum(XSJF_SCORE),2) as XSJF_SCORE,
       round(sum(ALL_SCORE_XS),2) as ALL_SCORE_XS,
       (case
         when (select count(1)
                 from G_GA.G_GA_NPRP_SCORE_UNION_M m
                where m.month_id =substr('$monthId$',0,6) 
                  and m.org_parent_id = o.org_id) = 0 then
          'true'
         else
          'false'
       end) "isLeaf"
  from G_GA.G_GA_NPRP_SCORE_UNION_M o
 where o.month_id = substr('$monthId$',0,6) 
   and o.org_id = '$orgIds$'   and o.prp_role!='root'  
   
    <isNotEmpty property="roleId">
            <isNotEqual property="roleId" compareValue="all">
                and  PRP_ROLE = '$roleId$'
            </isNotEqual>
        </isNotEmpty>      
        
        
   
  group by org_id,org_name,org_parent_id,org_parent_name,org_grade,org_id 
   
union all    
select 
       org_id,
       org_name,
       org_parent_id,
       org_parent_name,
       org_grade,
       sum(OBJ_NUM) as OBJ_NUM,
       round(sum(ALL_SCORE),2) as ALL_SCORE,
       round(sum(XS_SCORE),2) as XS_SCORE,
        round(sum(GXH_SCORE),2) as GXH_SCORE,
       round(sum(CLWX_SCORE),2) as CLWX_SCORE,
       round(sum(SL_SCORE),2) as SL_SCORE,
       round(sum(KF_SCORE),2) as KF_SCORE,
        round(sum(XSJF_SCORE),2) as XSJF_SCORE,
        round(sum(ALL_SCORE_XS),2) as ALL_SCORE_XS,
       (case
         when (select count(1)
                 from G_GA.G_GA_NPRP_SCORE_UNION_M m
                where m.month_id = substr('$monthId$',0,6)  
                  and m.org_parent_id = t.org_id) = 0 then
          'true'
         else
          'false'
       end) "isLeaf"
  from (select * from G_GA.G_GA_NPRP_SCORE_UNION_M t1 order by t1.org_id) t
 where t.month_id =substr('$monthId$',0,6) 
   and t.org_parent_id = '$orgIds$'    and t.prp_role!='root'   
   
   
     <isNotEmpty property="roleId">
            <isNotEqual property="roleId" compareValue="all">
                and PRP_ROLE = '$roleId$'
            </isNotEqual>
        </isNotEmpty>   
   
  group by t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade,t.org_id 
   
     order by org_grade,org_id
 
	</select>
    <select id="getListByParentId" resultClass="java.util.HashMap">
       select  org_id,
       org_name,
       org_parent_id,
       org_parent_name,
       org_grade,
       sum(OBJ_NUM) as OBJ_NUM,
       round(sum(ALL_SCORE),2) as ALL_SCORE,
       round(sum(XS_SCORE),2) as XS_SCORE,
        round(sum(GXH_SCORE),2) as GXH_SCORE,
       round(sum(CLWX_SCORE),2) as CLWX_SCORE,
       round(sum(SL_SCORE),2) as SL_SCORE,
       round(sum(KF_SCORE),2) as KF_SCORE,
        round(sum(XSJF_SCORE),2) as XSJF_SCORE,
        round(sum(ALL_SCORE_XS),2) as ALL_SCORE_XS,
	       
          (case when (select count(1)
                     from G_GA.G_GA_NPRP_SCORE_UNION_M m
                    where m.month_id = substr('$monthId$', 0, 6)
                     
                      and m.org_parent_id =t.org_id )=0 then 'true' else 'false' end) "isLeaf",
          (case when (select count(1)
                     from G_GA.G_GA_NPRP_SCORE_UNION_M m
                    where m.month_id = substr('$monthId$', 0, 6)
                      
                      and m.org_parent_id =t.org_id )=0 then 'true' else 'false' end) "expanded"
                      
    from G_GA.G_GA_NPRP_SCORE_UNION_M t
    where t.month_id = substr('$monthId$',0,6)
   
    and t.org_parent_id='$orgIds$'     and t.prp_role!='root'    
    
      <isNotEmpty property="roleId">
            <isNotEqual property="roleId" compareValue="all">
                and PRP_ROLE = '$roleId$'
            </isNotEqual>
        </isNotEmpty>    
        
        
    
    group by t.org_id,t.org_name,t.org_parent_id,t.org_parent_name,t.org_grade,t.org_id   
    order by t.org_id 
   


	 </select>
    
    
</sqlMap>