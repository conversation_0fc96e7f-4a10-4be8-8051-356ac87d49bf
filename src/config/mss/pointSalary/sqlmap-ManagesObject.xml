<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.pointSalary.managesObject">
    <!-- 查询用户对象 -->
    <select id="getObjList" resultClass="java.util.HashMap">
        select a.obj_id login_id,a.obj_name user_name,a.mobile,b3.PRP_ROLE_DESC, b.develop_no, b1.accept_no, b2.chnl_no, replace(c.orgrank_name, '甘肃省>', '') org_name
        from G_GA.G_GA_NPRP_OBJ_M a,
        (select OBJ_ID,
        listagg(DIM_CODE, '、') within group (order by DIM_CODE) develop_no
        from (select *
        from G_GA.G_GA_NPRP_OBJ2DIM_M
        where MONTH_ID = '$monthId$'
        and DIM_CODE_TYPE = '01')
        group by OBJ_ID) b,
        (select OBJ_ID,
        listagg(DIM_CODE, '、') within group (order by DIM_CODE) accept_no
        from (select *
        from G_GA.G_GA_NPRP_OBJ2DIM_M
        where MONTH_ID = '$monthId$'
        and DIM_CODE_TYPE = '02')
        group by OBJ_ID) b1,
        (select PRP_ROLE,PRP_ROLE_DESC from G_GA.G_GA_NPRP_ROLE
       ) b3,
        (select OBJ_ID,
        listagg(DIM_CODE, '、') within group (order by DIM_CODE) chnl_no
        from (select *
        from G_GA.G_GA_NPRP_OBJ2DIM_M
        where MONTH_ID = '$monthId$'
        and DIM_CODE_TYPE = '03')
        group by OBJ_ID) b2,
        (select id,ORGRANK_NAME
        from pure_gs.ORG_ORGANIZATION_MOD_V16_M
        where month_id = '$monthId$') c
        where a.MONTH_ID = '$monthId$'
        <isNotEqual property="treeId" compareValue="root">
            and a.PRP_ROLE = '$treeId$'
        </isNotEqual>
        <isNotEmpty property="areaNo">
            <isNotEqual property="areaNo" compareValue="087">
                and a.AREA_ID = '$areaNo$'
            </isNotEqual>
        </isNotEmpty>
        <isNotEmpty property="key">
            and (a.OBJ_NAME like '%$key$%' or a.MOBILE = '$key$' or a.OBJ_ID = '$key$')
        </isNotEmpty>
        and a.OBJ_ID = b.OBJ_ID(+)
        and a.OBJ_ID = b1.OBJ_ID(+)
        and a.OBJ_ID = b2.OBJ_ID(+)
        and a.PRP_ROLE = b3.PRP_ROLE(+)
        and a.SALE_ID = c.id
    </select>

    <!-- 地市列表 -->
    <select id="getAreas" resultClass="java.util.HashMap">
        select * from pure_gs.REGION a
        <isNotEqual property="areaNo" compareValue="087">
            where a.AREA_NO = '$areaNo$'
        </isNotEqual>
        order by a.ord asc
    </select>

    <select id="getRoles" resultClass="java.util.HashMap">
        select 'root' "id",'考核岗位' "text",'' "pid",'营业员' "btnText",0 ord_id from dual
        union all
        select t.prp_role "id",t.prp_role_desc "text", 'root' "pid",t.prp_role_desc "btnText",ord_id from
        G_GA.G_GA_NPRP_ROLE t order by ord_id
    </select>


    <!-- 批量插入管理人员 -->
    <insert id="saveManagePatch" parameterClass="java.util.List">
        insert all
        <iterate conjunction="">
            into G_GA.G_GA_NPRP_OBJ(AREA_ID,OBJ_ID,MOBILE,SALE_ID,PRP_ROLE,FLAG,OPERATER,OPERATE_DATE)
            values(#list[].AREA_NO#,#list[].LOGIN_ID#,#list[].MOBILE#,#list[].pid#,#list[].manageType#,'1',#list[].CURRENT_USER#,sysdate)
        </iterate>
        select * from dual
    </insert>


    <delete id="removeManage">
        delete from G_GA.G_GA_NPRP_OBJ t where t.OBJ_ID='$id$' and t.PRP_ROLE='$treeId$'
    </delete>

    <!-- 获取人员组织结构树 -->
    <select id="getTreeData" resultClass="java.util.HashMap">
        select t.id as "id",
        t.name as "text",
        t.parent_id as "pid",
        '1' is_org,
        t.areacode area_no,
        '' LOGIN_ID,
        -1 user_id,
        '' mobile
        (select * from pure_gs.ORG_ORGANIZATION_MOD_V16_M where month_id = '$monthId$') t where 1=1
        <isNotEqual property="areaNo" compareValue="087">
            and t.areacode = '$areaNo$'
        </isNotEqual>
        union all
        select t2.login_id as "id",
        t2.user_name as "text",
        t2.sale_area as "pid",
        '0' is_org,
        t2a.areacode area_no,
        t2.login_id,
        t2.user_id,
        t2.mobile mobile
        from pure_gs.pure_user t2,(select * from pure_gs.ORG_ORGANIZATION_MOD_V16_M where month_id = '$monthId$') t2a,G_GA.G_GA_NPRP_ROLE t2b
        where 1=1
        and t2.SALE_AREA = t2a.ID and t2a.GRADE = t2b.org_grade and t2b.PRP_ROLE = '$treeId$'
        <isNotEqual property="areaNo" compareValue="087">
            and ( t2a.areacode='$areaNo$' or ('$areaNo$'='8700' and t2a.areacode in ('8700','8690') ) )
        </isNotEqual>
        and t2.login_id not in
        (select t4.obj_id
        from (select * from G_GA.G_GA_NPRP_OBJ_M where month_id = '$monthId$') t4
        where 1=1
        and t4.obj_id is not null)
    </select>

</sqlMap>