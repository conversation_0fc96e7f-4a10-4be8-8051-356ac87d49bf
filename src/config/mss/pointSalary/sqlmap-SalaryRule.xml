<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.pointSalary.SalaryRule">

    <!--查询数据字典信息 -->
    <select id="queryDataDictList" resultClass="java.util.HashMap">
        select * from
            (select t.calc_kpi as "id", t.calc_kpi_desc as "text", t.parent_id as"parentId", t.ord as "ord", '1' "leaf"
            from G_GA.G_GA_NPRP_SALARY_RULE t where t.parent_id !='root'
            order by t.ord)
        union all
        select * from
            (select t.calc_kpi as "id", t.calc_kpi_desc as "text", t.parent_id as "parentId", t.ord as "ord", '0' "leaf"
            from G_GA.G_GA_NPRP_SALARY_RULE t where t.parent_id='root'
            order by t.ord )
    </select>
    <select id="getGridList" resultClass="java.util.HashMap">
        select * from G_GA.G_GA_NPRP_SALARY_RULE t where t.CALC_KPI='$ruleId$'
    </select>

    <select id="getRuleMap" resultClass="java.util.HashMap">
        SELECT t.calc_kpi
        　　FROM G_GA.G_GA_NPRP_SALARY_RULE t
        　　WHERE connect_by_isleaf = 1
        　　START WITH t.calc_kpi = '$value$'
        　　CONNECT BY PRIOR t.parent_id = t.calc_kpi
    </select>


    <!-- 插入数据字典 -->
    <insert id="saveData" parameterClass="Map">
        <selectKey resultClass="string" keyProperty="newId">
            SELECT 'salary'||PURE.CALC_KPI_SEQ.NEXTVAL AS ID FROM DUAL
        </selectKey>
        insert into
        G_GA.G_GA_NPRP_SALARY_RULE(CALC_KPI,CALC_KPI_DESC,RULE_REMARK,RULE_CONTENT,RULE_KPIS,PARENT_ID,RULE_ENGLISH,RULE_CHINESE,ORD,PRP_ROLE)
        values(#newId#,'$calcKpiDesc$','$ruleRemark$','$ruleContent$','$ruleKpis$','$parentId$','$ruleEnglish$','$ruleChinese$','$ruleOrd$','$prpRole$')
    </insert>


    <!-- 更新资源信息 -->
    <update id="updateData" parameterClass="java.util.HashMap">
        update G_GA.G_GA_NPRP_SALARY_RULE set
        CALC_KPI_DESC='$calcKpiDesc$',RULE_REMARK='$RULE_REMARK$',RULE_CONTENT='$RULE_CONTENT$',RULE_KPIS='$RULE_KPIS$',
        RULE_ENGLISH='$ruleEnglish$' ,RULE_CHINESE= '$ruleChinese$', ORD = '$ruleOrd$' ,PRP_ROLE='$prpRole$'
        where CALC_KPI='$calcKpi$'
    </update>


    <!-- 根据节点ID获取节点信息 -->
    <select id="getDataInfo" resultClass="java.util.HashMap">
        select d.*,d.calc_kpi_desc as "text", d.calc_kpi as "id", ord as "ord",d.parent_id as "parentId", ORD RULE_ORD
        from G_GA.G_GA_NPRP_SALARY_RULE d
        where d.calc_kpi = '$id$'
    </select>

    <!-- 更具节点ID删除数据 -->
    <delete id="deleteData">
        delete from G_GA.G_GA_NPRP_SALARY_RULE r where r.CALC_KPI = '$id$'
    </delete>


    <select id="selectDept" resultClass="java.util.HashMap">
        <!-- 指标库 -->
        select t.kpi_code as "id",
        t.kpi_name as "text",
        t.par_kpi_code as "parentId",
        (case
        when exists (select 1 from G_GA.G_GA_NPRP_KPI_MANAGE a where a.par_kpi_code = t.kpi_code) then 0 else 1 end) "leaf"
        from G_GA.G_GA_NPRP_KPI_MANAGE t
        CONNECT BY T.PAR_KPI_CODE = PRIOR T.KPI_CODE
        START WITH T.KPI_CODE = 'root'
        <!-- 积分规则库 -->
        union all
        select 'pointRule' as "id", '积分规则库' as "text", '' as "parentId", 0 "leaf" from dual
        union all
        select t.prp_role as "id", t.prp_role_desc as "text", 'pointRule' as "parentId", 0 "leaf"
        from (select * from G_GA.G_GA_NPRP_ROLE order by ORD_ID) t
        union all
        select t.calc_kpi      as "id",
        t.calc_kpi_desc as "text",
        t.parent_id     as "parentId",
        (case
        when exists(select 1 from G_GA.G_GA_NPRP_RULE_BANK a where a.parent_id = t.calc_kpi) then 0
        else 1 end)   "leaf"
        from (select * from G_GA.G_GA_NPRP_RULE_BANK b order by b.ord) t
        CONNECT BY T.PARENT_ID = PRIOR T.CALC_KPI
        START WITH T.parent_id in (select prp_role from G_GA.G_GA_NPRP_ROLE)
        <!-- 薪酬规则库 -->
        union all
        select 'salaryRoot' as "id", '薪酬规则库' as "text", '' as "parentId", 0 "leaf" from dual
        union all
        select t.calc_kpi      as "id",
        t.calc_kpi_desc as "text",
        t.parent_id     as "parentId",
        (case
        when exists(select 1 from G_GA.G_GA_NPRP_SALARY_RULE a where a.parent_id = t.calc_kpi) then 0
        else 1 end)   "leaf"
        from (select * from G_GA.G_GA_NPRP_SALARY_RULE b order by b.ord) t
        CONNECT BY T.PARENT_ID = PRIOR T.CALC_KPI
        START WITH T.parent_id = 'salaryRoot'
    </select>


</sqlMap>