<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mss.pointSalary.ResultAccounting">

    <!-- 查询所有的角色信息 -->
    <select id="queryRoleList" resultClass="java.util.HashMap">
        select t.prp_role as "id",
        t.prp_role_desc as "text",
        'root' as "parentId",
        t.ord_id as "ord",
        '1' "leaf"
        from G_GA.G_GA_NPRP_ROLE t
        order by t.ord_id
    </select>
    <!-- 记录核算日志 -->
    <insert id="addacctlog">
        insert into G_GA.G_GA_NPRP_ACCOUNTING_LOG(TASK_ID, MONTH_ID, START_DATE, TASK_RESULT, ROLE_NAME,
        CREATE_USER,AREA_NAME,TYPE )
        values('$taskId$','$monthId$','$startDate$','正在核算中','$roleText$','$loginId$','$areaText$','$type$')
    </insert>
    <!-- 更新核算日志 -->
    <update id="updatelog">
        update G_GA.G_GA_NPRP_ACCOUNTING_LOG set END_DATE='$endDate$', TASK_RESULT='$taskResult$'
        where TASK_ID='$taskId$'
    </update>
    <!-- 查询日志列表 -->
    <select id="getGridData" resultClass="java.util.HashMap">
        select * from G_GA.G_GA_NPRP_ACCOUNTING_LOG t where type='$type$' order by START_DATE desc
    </select>
    <!-- 删除核算积分结果 -->
    <delete id="deleteScoreData">
        delete from G_GA.G_GA_NPRP_SCORE_DATA_M where ACCT_MONTH='$monthId$'
        and ORG_PRP_ROLE in ($roleIds$)
        and area_no in ($areaIds$)
    </delete>
    <!-- 查询考核对象信息 -->
    <select id="queryStaffList" resultClass="java.util.HashMap">
        select t.*, x.MARKET_ID, x.GRID_ID
        from G_GA.G_GA_NPRP_OBJ_M t
        left join (select SYS_CONNECT_BY_PATH(id, '/') orgrank, grade, id,
        REGEXP_SUBSTR(SYS_CONNECT_BY_PATH(id, '/'), '[^/]+', 1, 3) market_id,
        REGEXP_SUBSTR(SYS_CONNECT_BY_PATH(id, '/'), '[^/]+', 1, 4) grid_id
        from (select * from pure_gs.org_organization_mod_v16_m where month_id = '$monthId$')
        start with id = 'root'
        connect by prior id = parent_id) x on t.sale_id = x.id
        where t.month_id = '$monthId$'
        and t.prp_role = '$roleId$'
        and t.area_id in ($areaIds$)
    </select>
    <!-- 查询叶子节点积分规则 -->
    <select id="queryRuleListLeaf" resultClass="java.util.HashMap">
        select * from (
        select t.calc_kpi,t.calc_kpi_desc,t.parent_id,t.rule_english,t.rule_remark,t.rule_content,t.rule_kpis,DEAL_TYPE,
        level,
        (select decode(count(*), 0, 1, 0)
        from G_GA.G_GA_NPRP_RULE_BANK b
        where b.PARENT_ID = t.calc_kpi) leaf
        from G_GA.G_GA_NPRP_RULE_BANK t
        start with t.calc_kpi ='$roleId$'
        connect by prior t.calc_kpi = t.parent_id
        order by level desc
        ) where leaf='1'
    </select>
    <!-- 查询非叶子节点 -->
    <select id="queryRuleListNonLeaf" resultClass="java.util.HashMap">
        select * from (
        select t.calc_kpi,t.calc_kpi_desc,t.parent_id,t.rule_english,t.rule_remark,t.rule_content,t.rule_kpis,DEAL_TYPE,
        level,
        (select decode(count(*), 0, 1, 0)
        from G_GA.G_GA_NPRP_RULE_BANK b
        where b.PARENT_ID = t.calc_kpi) leaf
        from G_GA.G_GA_NPRP_RULE_BANK t
        start with t.calc_kpi ='$roleId$'
        connect by prior t.calc_kpi = t.parent_id
        order by level desc
        ) where leaf='0'
    </select>
    <!--参与计算人员信息 -->
    <select id="userList" resultClass="java.util.HashMap">
        select distinct USER_ID, DIM_CODE, DIM_CODE_TYPE, SERIAL_NUMBER, IS_COMMON_KPI
        from (select t.*, nvl(a.is_common_kpi, '0') is_common_kpi
        from G_GA.G_GA_NPRP_SCORE_DATA_PRO_M t
        left join (select a.KPI_CODE, a.is_common_kpi
        from G_GA.G_GA_NPRP_KPI_MANAGE a) a
        on t.KPI_CODE = a.KPI_CODE
        where t.month_id = '$monthId$'
        and t.prp_role = '$roleId$'
        and t.kpi_code in ($rule_str$)) t
        where t.org_id = '$objId$'
        and USER_ID is not null
    </select>
    <!-- 查询指标库中的共性指标 -->
    <select id="commonKpis" resultClass="java.util.HashMap">
        select KPI_CODE,KPI_NAME from g_ga.g_ga_nprp_kpi_manage t where t.is_common_kpi = 1
    </select>
    <!-- 计算考核对象（staff）,计算userId 时进行查询，此时只有指标 G_GA_PRP_KPI_MANAGE -->
    <select id="kpiListLeaf" resultClass="java.util.HashMap">
        select '$userId$' USER_ID, T.KPI_CODE, SUM(NVL(NVL(T1.KPI_VALUE,T.DEFAULT_KPI_VALUE), 0)) KPI_VALUE
        from (
        select *
        from G_GA.G_GA_NPRP_KPI_MANAGE t
        where t.kpi_code in ($rule_str$)) t,
        (
        select USER_ID, kpi_code, sum(kpi_value) kpi_value
        from G_GA.G_GA_NPRP_SCORE_DATA_PRO_M t
        where 1 = 1
        and t.month_id = '$monthId$'
        and t.USER_ID = '$userId$'
        and t.ORG_ID = '$objId$'
        and t.kpi_code in ($rule_str$)
        group by USER_ID, kpi_code
        <!-- 定向系数 -->
        union all
        select '$userId$' USER_ID, t.kpi_code, to_number(NVL(t.direct_num, 1)) kpi_value
        from G_GA.G_GA_NPRP_GUIDE_IMP t
        where 1 = 1
        and t.kpi_code in ($rule_str$)
        <!-- 统一业务系数 -->
        union all
        select '$userId$' USER_ID, t.kpi_code, to_number(NVL(t.RATIO_NUM, 1)) kpi_value
        from G_GA.G_GA_NPRP_UNITY_IMP t
        where 1 = 1
        and t.kpi_code in ($rule_str$)
        <!-- 重点业务系数 -->
        union all
        select '$userId$' USER_ID,
        t.kpi_code,
        to_number(NVL(t.DIRECT_NUM, 1)) kpi_value
        from G_GA.G_GA_NPRP_MANAGE_IMP_M t
        where t.AREA_NO = '$areaId$' and t.MONTH_ID = '$monthId$'
        <!-- 社会渠道个数指标表 -->
        union all
        select '$userId$' USER_ID,
        t.kpi_code,
        to_number(NVL(t.KPI_VALUE, 1)) kpi_value
        from G_GA.G_GA_NPRP_OBJ2SCHNL_M t
        where t.AREA_ID = '$areaId$' and t.MONTH_ID = '$monthId$' and t.DIM_CODE = '$dimCode$' and t.OBJ_ID = '$objId$'
        ) T1
        WHERE T.KPI_CODE = T1.KPI_CODE(+)
        GROUP BY T.KPI_CODE

    </select>
    <!--  全部指标、规则 ,计算loginId -->
    <select id="ruleKpiList" resultClass="java.util.HashMap">
        select '$objId$' ORG_ID, T.KPI_CODE, SUM(NVL(NVL(T1.KPI_VALUE,T.DEFAULT_KPI_VALUE), 0)) KPI_VALUE
        from (select * from
        (select t.kpi_code,t.DEFAULT_KPI_VALUE from G_GA.G_GA_NPRP_KPI_MANAGE t
        union
        select t1.calc_kpi KPI_CODE,null DEFAULT_KPI_VALUE from G_GA.G_GA_NPRP_RULE_BANK t1 where t1.PRP_ROLE='$roleId$'
        )
        ) t,

        ( select ORG_ID, kpi_code, NVL(sum(kpi_value), 0) kpi_value from G_GA.G_GA_NPRP_SCORE_DATA_PRO_M t
        where 1 = 1 and t.month_id = '$monthId$' and t.ORG_ID = '$objId$'
        group by ORG_ID, kpi_code
        union ALL
        select t1.ORG_ID,
        t1.calc_kpi kpi_code,
        NVL(sum(t1.calc_kpi_score), 0) kpi_value
        from G_GA.G_GA_NPRP_SCORE_DATA_M t1
        where 1 = 1
        and t1.acct_month = '$monthId$'
        and t1.ORG_ID = '$objId$'
        group by t1.ORG_ID, t1.calc_kpi
        <!-- 定向系数 -->
        union all
        select '$objId$' ORG_ID,
        t.kpi_code,
        to_number(NVL(t.direct_num, 1)) kpi_value
        from G_GA.G_GA_NPRP_GUIDE_IMP t
        <!-- 统一业务系数 -->
        union all
        select '$objId$' ORG_ID,
        t.kpi_code,
        to_number(NVL(t.RATIO_NUM, 1)) kpi_value
        from G_GA.G_GA_NPRP_UNITY_IMP t
        <!-- 重点业务系数 -->
        union all
        select '$objId$' ORG_ID,
        t.kpi_code,
        to_number(NVL(t.DIRECT_NUM, 1)) kpi_value
        from G_GA.G_GA_NPRP_MANAGE_IMP_M t
        where t.AREA_NO = '$areaId$' and t.MONTH_ID = '$monthId$'
        <!-- 社会渠道个数指标表 -->
        union all
        select '$objId$' ORG_ID,
        t.kpi_code,
        to_number(NVL(t.KPI_VALUE, 1)) kpi_value
        from G_GA.G_GA_NPRP_OBJ2SCHNL_M t
        where t.AREA_ID = '$areaId$' and t.MONTH_ID = '$monthId$' and t.OBJ_ID = '$objId$'
        ) T1
        WHERE T.KPI_CODE = T1.KPI_CODE(+)
        GROUP BY T.KPI_CODE
    </select>

    <insert id="saveKpiValueBatch" parameterClass="java.util.List">
        insert all
        <iterate conjunction="">
            into G_GA.G_GA_NPRP_SCORE_DATA_M(ACCT_MONTH,
            AREA_NO,
            ORG_ID,
            USER_ID,
            DIM_CODE,
            CALC_KPI,
            CALC_KPI_SCORE,
            UPDATE_DATE,
            ORG_PRP_ROLE,
            SERIAL_NUMBER )
            values(#batchList[].monthId#,#batchList[].areaId#,#batchList[].objId#,#batchList[].userId#,
            #batchList[].dimCode#,#batchList[].calcKpi#,#batchList[].kpiScore#,sysdate,#batchList[].roleId#,#batchList[].serialNumber#)
        </iterate>
        select * from dual
    </insert>

    <select id="querySaleAreaTree" resultClass="java.util.HashMap">
        select t.parent_id "pId", t.id "id", t.name "text", 0 "leaf"
        From
        (select * from pure_gs.ORG_ORGANIZATION_MOD_V16_M where month_id = '$monthId$') t
        where t.org_type in (201)
        start with t.id = 'root'
        connect by prior t.id = t.parent_id
        order siblings by t.ord
    </select>

    <!-- 删除薪酬结果表数据 -->
    <delete id="deleteSalaryData">
        delete from G_GA.G_GA_NPRP_SALARY_DATA_M where ACCT_MONTH='$monthId$'
        and ORG_PRP_ROLE in ($roleIds$)
        and area_no in ($areaIds$)
    </delete>


    <!-- 查询薪酬规则树 G_GA_PRP_SALARY_RULE-->
    <select id="querySalaryTree" resultClass="java.util.HashMap">
        select t.calc_kpi,t.calc_kpi_desc,t.parent_id,t.rule_english,t.rule_remark,t.rule_content,t.rule_kpis,DEAL_TYPE,
        level,
        (select decode(count(*), 0, 1, 0)
        from G_GA.G_GA_NPRP_SALARY_RULE b
        where b.PARENT_ID = t.calc_kpi) leaf
        from G_GA.G_GA_NPRP_SALARY_RULE t
        start with t.calc_kpi ='$roleId$_salary'
        connect by prior t.calc_kpi = t.parent_id
        order by level desc
    </select>
    <!--  根据角色查询薪酬指标、规则、积分单价、地区调节系数、角色调节系数、营业厅店长薪酬倍数、网格小CEO薪酬倍数、基本薪酬导入表-->
    <select id="salaryKpiList" resultClass="java.util.HashMap">
        select '$objId$' ORG_ID, T.KPI_CODE, SUM(NVL(NVL(T1.KPI_VALUE,T.DEFAULT_KPI_VALUE), 0)) KPI_VALUE
        from (select * from
        (select t.kpi_code,t.DEFAULT_KPI_VALUE from G_GA.G_GA_NPRP_KPI_MANAGE t
        union
        select t1.CALC_KPI,null DEFAULT_KPI_VALUE from G_GA.G_GA_NPRP_RULE_BANK t1 where t1.PRP_ROLE='$roleId$' )
        union
        select t2.CALC_KPI,null DEFAULT_KPI_VALUE from G_GA.G_GA_NPRP_SALARY_RULE t2 where t2.PRP_ROLE = '$roleId$' ) t,
        (
        <!-- 指标值 -->
        select ORG_ID, kpi_code, NVL(sum(kpi_value), 0) kpi_value from G_GA.G_GA_NPRP_SALARY_DATA_PRO_M t
        where 1 = 1 and t.month_id = '$monthId$' and t.ORG_ID = '$objId$'
        group by ORG_ID, kpi_code
        union all
        <!-- 积分值 -->
        select t1.ORG_ID, t1.calc_kpi kpi_code, NVL(sum(t1.calc_kpi_score), 0) kpi_value
        from G_GA.G_GA_NPRP_SCORE_DATA_M t1
        where 1 = 1
        and t1.acct_month = '$monthId$'
        and t1.ORG_ID = '$objId$'
        group by t1.ORG_ID, t1.calc_kpi
        union all
        <!-- 积分单价(到地市级) -->
        select '$objId$' ORG_ID, t.kpi_code, to_number(NVL(t.POINTS_NUM, 0)) kpi_value
        from G_GA.G_GA_NPRP_POINTS_IMP t
        where 1 = 1 and t.area_code = '$areaId$' and t.month_id = '$monthId$'
        union all
        <!-- 角色调节系数、地区调节系数、营业厅店长薪酬倍数、网格小CEO薪酬倍数（到网格级402） -->
        select '$objId$' ORG_ID, t.kpi_code,to_number(NVL(t.POINTS_NUM, 1)) kpi_value
        from
        (select KPI_CODE,to_char(RATIO_NUM) as POINTS_NUM,GRID_ID from G_GA.G_GA_NPRP_ROLE_IMP
        where ROLE_ID = '$roleId$' and MONTH_ID = '$monthId$'
        union
        select KPI_CODE,POINTS_NUM,GRID_ID from G_GA.G_GA_NPRP_ADJUST_IMP
        where MONTH_ID = '$monthId$'
        union
        select KPI_CODE,POINTS_NUM,GRID_ID from G_GA.G_GA_NPRP_YYTDZ_SALARY_IMP
        union
        select KPI_CODE,POINTS_NUM,GRID_ID from G_GA.G_GA_NPRP_CEO_SALARY_IMP ) t
        where 1=1 and t.grid_id = '$gridId$'
        union all
        <!--  ~~~ 责任绩效薪酬 ~~~ -->
        <!-- 责任绩效基数 账期、地市、角色 -->
        select '$objId$' ORG_ID, t.kpi_code, NVL(t.POINTS_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_DUTY_IMP t
        where t.MONTH_ID = '$monthId$' and t.AREA_CODE = '$areaId$' and t.ROLE_ID = '$roleId$'
        union all
        <!-- 业务指标目标值 账期、营销中心、角色-->
        select '$objId$' ORG_ID, t.kpi_code, NVL(t.POINTS_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_INDEX_IMP t
        where t.MONTH_ID = '$monthId$' and t.MARKET_ID = '$marketId$' and t.ROLE_ID = '$roleId$'
        union all
        <!-- 业务指标完成率 账期、营销中心、角色-->
        select '$objId$' ORG_ID, t.kpi_code, NVL(t.POINTS_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_PEAK_IMP t
        where t.MONTH_ID = '$monthId$' and t.MARKET_ID = '$marketId$' and t.ROLE_ID = '$roleId$'
        union all
        <!-- 权重 账期、营销中心、角色-->
        select '$objId$' ORG_ID, t.kpi_code, NVL(t.POINTS_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_WEIGHT_IMP t
        where t.MONTH_ID = '$monthId$' and t.MARKET_ID = '$marketId$' and t.ROLE_ID = '$roleId$'
        union all
        <!-- 产能调节系数-->
        select '$objId$' ORG_ID, t.kpi_code, NVL(t.RATIO_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_CAPACITY_IMP t
        union all
        <!-- 基本薪酬导入表 -->
        select '$objId$' ORG_ID, t.kpi_code, NVL(t.kpi_value, 0) kpi_value
        from G_GA.G_GA_NPRP_SALARY_IMP_M t
        where t.MONTH_ID = '$monthId$' and t.LOGIN_ID = '$objId$'
        ) T1
        WHERE T.KPI_CODE = T1.KPI_CODE(+)
        GROUP BY T.KPI_CODE


    </select>
    <!-- 批量保存薪酬结果 -->
    <insert id="saveSalaryBatch" parameterClass="java.util.List">
        insert all
        <iterate conjunction="">
            into G_GA.G_GA_NPRP_SALARY_DATA_M(ACCT_MONTH,
            AREA_NO,
            ORG_ID,
            USER_ID,
            DIM_CODE,
            CALC_KPI,
            CALC_KPI_SCORE,
            UPDATE_DATE,
            ORG_PRP_ROLE,
            SERIAL_NUMBER )
            values(#batchList[].monthId#,#batchList[].areaId#,#batchList[].objId#,#batchList[].userId#,
            #batchList[].dimCode#,#batchList[].calcKpi#,#batchList[].kpiScore#,sysdate,#batchList[].roleId#,#batchList[].serialNumber#)
        </iterate>
        select * from dual
    </insert>
</sqlMap>