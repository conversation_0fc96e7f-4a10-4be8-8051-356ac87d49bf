<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="mss.pointSalary.ResultSalary">

    <!-- 查询用户得分列表 -->
    <select id="selectDepResult" resultClass="java.util.HashMap">
        select t.AREA_NO,
        t.acct_month,
        t.calc_kpi,
        round(t.calc_kpi_score, 2) calc_kpi_score,
        t3.area_desc,
        t2.obj_name  user_name,
        t2.mobile,
        t2.oa zb_email,
        t.org_id login_id,
        t4.prp_role_desc,
        t4.PRP_ROLE,
        m.name,
        m.jname
        from G_GA.G_GA_NPRP_SALARY_DATA_M t,
        (select * from G_GA.G_GA_NPRP_OBJ_M where month_id = '$monthId$') t2,
        pure_gs.REGION t3,
        G_GA.G_GA_NPRP_ROLE t4,
        (select * from PURE_GS.ORG_ORGANIZATION_MOD_V16_M where month_id = '$monthId$') m
        where t.org_id = t2.obj_id
        and m.areacode = t3.area_no
        and t.ORG_PRP_ROLE = t4.prp_role
        and t2.SALE_ID = m.id
        and t.CALC_KPI = t.ORG_PRP_ROLE || '_salary'
        <isNotEqual property="roleId" compareValue="all">
            and t.ORG_PRP_ROLE = '$roleId$'
        </isNotEqual>
        and t.acct_month = '$monthId$'
        and t2.SALE_ID in
        (SELECT T.ID FROM (select * from PURE_GS.ORG_ORGANIZATION_MOD_V16_M where month_id = '$monthId$') T
        where DELFLAG != '2'
        CONNECT BY T.PARENT_ID = PRIOR T.ID
        START WITH T.ID = '$areaNo$')
        <isNotEmpty property="selectId">
            <isNotEqual property="selectId" compareValue="">
                and t.org_id like '%' || trim(#selectId#) || '%'
            </isNotEqual>
        </isNotEmpty>
        <isNotEmpty property="selectName">
            <isNotEqual property="selectName" compareValue="">
                and t2.obj_name like '%' || trim(#selectName#) || '%'
            </isNotEqual>
        </isNotEmpty>
        <isEqual property="isReadSelf" compareValue="1">
            and t2.OBJ_ID = '$objId$'
        </isEqual>
        order by t.calc_kpi_score desc

    </select>

    <!-- 查询薪酬规则 -->
    <select id="selectSalaryTree" resultClass="java.util.HashMap">
        SELECT distinct
        T.CALC_KPI ,
        T.CALC_KPI_DESC,
        T.PARENT_ID,
        T.RULE_REMARK,
        T.RULE_KPIS,
        round(sum(M.CALC_KPI_SCORE),2) CALC_KPI_SCORE,
        M.ORG_ID,
        t.ord,
        (select decode(count(*), 0, 1, 0)
        from G_GA.G_GA_NPRP_SALARY_RULE b
        where b.PARENT_ID = t.calc_kpi) LEAF
        FROM G_GA.G_GA_NPRP_SALARY_RULE T,G_GA.G_GA_NPRP_SALARY_DATA_M M
        WHERE T.CALC_KPI=M.CALC_KPI AND M.ACCT_MONTH='$monthId$'
        AND M.ORG_ID='$loginId$' AND T.CALC_KPI IN(
        SELECT T.CALC_KPI
        FROM G_GA.G_GA_NPRP_SALARY_RULE T
        START WITH T.CALC_KPI = '$roleId$_salary'
        CONNECT BY PRIOR T.CALC_KPI = T.PARENT_ID)
        group by T.CALC_KPI ,
        T.CALC_KPI_DESC,
        T.PARENT_ID,
        T.RULE_REMARK,
        T.RULE_KPIS, M.ORG_ID,
        t.ord
        order by t.ord
    </select>

    <!-- 查询薪酬计算明细 -->
    <select id="selectSalaryTreeDetail" resultClass="java.util.HashMap">
        select m.kpi_code       CALC_KPI,
        m.kpi_name       CALC_KPI_DESC,
        m.PARENT_ID      PARENT_ID,
        ''               RULE_REMARK,
        ''               RULE_KPIS,
        nvl(n.CALC_KPI_SCORE,0) CALC_KPI_SCORE,
        '$loginId$'   ORG_ID,
        ''               ord,
        0                LEAF
        from (
        <!--指标编码--> 
        select distinct t1.kpi_code, t1.kpi_name, calc_kpi parent_id
        from (select calc_kpi, regexp_substr(rule_kpis, '[^;]+', 1, level) rule_kpis
        from (select * from G_GA.G_GA_NPRP_SALARY_RULE where instr(rule_kpis, ';') > 0 and prp_role = '$roleId$') t
        connect by level <![CDATA[ <=  ]]> regexp_count(rule_kpis, ';') + 1
        and rule_kpis = prior rule_kpis
        and prior dbms_random.value is not null) t,

        (select a.KPI_CODE, a.KPI_NAME
        from G_GA.G_GA_NPRP_KPI_MANAGE a
        union all
        select b.CALC_KPI, b.CALC_KPI_DESC
        from G_GA.G_GA_NPRP_RULE_BANK b) t1
        where t.rule_kpis is not null
        and t.rule_kpis = t1.kpi_code) m,

        (
        <!-- 指标值 -->
        select KPI_CODE, round(kpi_value, 2) calc_kpi_score
        from G_GA.G_GA_NPRP_SALARY_DATA_PRO_M
        where MONTH_ID = '$monthId$'
        and ORG_ID = '$loginId$'
        union all
        <!-- 积分值 -->
        select CALC_KPI as KPI_CODE, round(calc_kpi_score,2) as calc_kpi_score
        from G_GA.G_GA_NPRP_SCORE_DATA_M
        where ACCT_MONTH = '$monthId$'
        and ORG_ID = '$loginId$'
        union all
        <!-- 积分单价 -->
        select KPI_CODE, to_number(NVL(POINTS_NUM, 1)) CALC_KPI_SCORE
        from G_GA.G_GA_NPRP_POINTS_IMP
        where AREA_CODE = '$areaId$' and month_id = '$monthId$'
        union all
        <!-- 角色调节系数、地区调节系数、营业厅店长薪酬倍数、网格小CEO薪酬倍数、 -->
        select t.kpi_code,to_number(NVL(t.POINTS_NUM, 1)) CALC_KPI_SCORE
        from
        ( select KPI_CODE,to_char(RATIO_NUM) as POINTS_NUM,GRID_ID from G_GA.G_GA_NPRP_ROLE_IMP
        where AREA_CODE = '$areaId$' and ROLE_ID = '$roleId$' and MONTH_ID = '$monthId$'
        union
        select KPI_CODE,POINTS_NUM,GRID_ID from G_GA.G_GA_NPRP_ADJUST_IMP
        where MONTH_ID = '$monthId$'
        union
        select KPI_CODE,POINTS_NUM,GRID_ID from G_GA.G_GA_NPRP_YYTDZ_SALARY_IMP
        union
        select KPI_CODE,POINTS_NUM,GRID_ID from G_GA.G_GA_NPRP_CEO_SALARY_IMP ) t
        where 1=1 and t.grid_id = '$gridId$'
        union all
        <!--  ~~~ 责任绩效薪酬 ~~~ -->
        <!-- 责任绩效基数 账期、地市、角色 -->
        select t.kpi_code, NVL(t.POINTS_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_DUTY_IMP t
        where t.MONTH_ID = '$monthId$' and t.AREA_CODE = '$areaId$' and t.ROLE_ID = '$roleId$'
        union all
        <!-- 业务指标目标值 账期、营销中心、角色 -->
        select t.kpi_code, NVL(t.POINTS_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_INDEX_IMP t
        where t.MONTH_ID = '$monthId$' and t.MARKET_ID = '$marketId$' and t.ROLE_ID = '$roleId$'
        union all
        <!-- 业务指标完成率 账期、营销中心、角色 -->
        select t.kpi_code, NVL(t.POINTS_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_PEAK_IMP t
        where t.MONTH_ID = '$monthId$' and t.MARKET_ID = '$marketId$' and t.ROLE_ID = '$roleId$'
        union all
        <!-- 权重 账期、营销中心、角色 -->
        select t.kpi_code, NVL(t.POINTS_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_WEIGHT_IMP t
        where t.MONTH_ID = '$monthId$' and t.MARKET_ID = '$marketId$' and t.ROLE_ID = '$roleId$'
        union all
        <!-- 产能系数 -->
        select t.kpi_code, NVL(t.RATIO_NUM, 1) kpi_value
        from G_GA.G_GA_NPRP_CAPACITY_IMP t
        union all
        <!-- 基本系数导入 -->
        select t.kpi_code, NVL(t.kpi_value, 0) CALC_KPI_SCORE from G_GA.G_GA_NPRP_SALARY_IMP_M t
        where t.MONTH_ID = '$monthId$' and t.LOGIN_ID = '$loginId$'
        ) n
        where m.KPI_CODE = n.KPI_CODE(+)

    </select>

    <!-- 查询详情页面头部用户信息 -->
    <select id="getUserResult" resultClass="java.util.HashMap">
        select distinct t.ACCT_MONTH,round(t.CALC_KPI_SCORE,2) CALC_KPI_SCORE,
        t3.area_desc,
        t2.OBJ_NAME user_name,
        t2.mobile,
        t2.OBJ_ID login_id,
        t4.prp_role_desc,
        t4.PRP_ROLE,
        m.name,
        m.jname
        from G_GA.G_GA_NPRP_SALARY_DATA_M t,
        (select * from G_GA.G_GA_NPRP_OBJ_M t where t.month_id = '$monthId$' and t.OBJ_ID = '$loginId$') t2,
        REGION t3,
        G_GA.G_GA_NPRP_ROLE t4,
        (select * from PURE_GS.ORG_ORGANIZATION_MOD_V16_M where month_id = '$monthId$' ) m
        where t.org_id = t2.OBJ_ID
        and m.areacode = t3.area_no
        and t.ORG_PRP_ROLE = t4.prp_role
        and t2.SALE_ID = m.id
        and t.calc_kpi = '$roleId$_salary'
        and t.acct_month = '$monthId$'
        and t.org_id='$loginId$'

    </select>

    <!-- 根据账期和考核对象ID查询考核对象信息 -->
    <select id="selectObjs" resultClass="java.util.HashMap">
        select t.*, a.grid_id,b.market_id
        from G_GA.G_GA_NPRP_OBJ_M t
        left join
        (select id as grid_id, '$loginId$' as OBJ_ID
        from (select s.id, s.org_type
        from (select id, PARENT_ID, ORG_TYPE from pure_gs.org_organization_mod_v16_m o where o.month_id = '$monthId$') s
        start with s.id in (select p.SALE_ID
        from G_GA.G_GA_NPRP_OBJ_M p,
        (select * from pure_gs.org_organization_mod_v16_m o where o.month_id = '$monthId$') o
        where p.month_id = '$monthId$'
        and p.SALE_ID = o.id
        and p.OBJ_ID = '$loginId$')
        CONNECT by s.id = PRIOR s.PARENT_ID)
        where org_type = '402') a
        on t.OBJ_ID = a.OBJ_ID
        left join
        (select id as market_id, '$loginId$' as OBJ_ID
        from (select s.id, s.org_type
        from (select id, PARENT_ID, ORG_TYPE from pure_gs.org_organization_mod_v16_m o where o.month_id = '$monthId$') s
        start with s.id in (select p.SALE_ID
        from G_GA.G_GA_NPRP_OBJ_M p,
        (select * from pure_gs.org_organization_mod_v16_m o where o.month_id = '$monthId$') o
        where p.month_id = '$monthId$'
        and p.SALE_ID = o.id
        and p.OBJ_ID = '$loginId$')
        CONNECT by s.id = PRIOR s.PARENT_ID)
        where org_type = '405') b
        on t.OBJ_ID = b.OBJ_ID
        where t.MONTH_ID = '$monthId$'
        and t.OBJ_ID = '$loginId$'
    </select>
</sqlMap>