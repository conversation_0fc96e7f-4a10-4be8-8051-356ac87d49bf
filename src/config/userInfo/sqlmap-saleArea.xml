<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="pure.gbs.salearea">

<select id="selectSaleAreaRoot" resultClass="java.util.HashMap">
		SELECT ID "id",
		       PARENT_ID "pId",
		       NAME "name",
		       CASE
		         WHEN EXISTS
		          (SELECT 1 FROM ORG_ORGANIZATION_MOD A WHERE A.PARENT_ID = T.ID) THEN
		          'true'
		         ELSE
		          'false'
		       END "isParent",
		       '|' || replace(ORGRANK_NAME,'&gt;','|') "namepath"
         FROM PURE.ORG_ORGANIZATION_MOD T
		WHERE T.ID = #saleArea#
		AND T.DELFLAG = 0
		AND INSTR(T.ORGRANK, 'root') > 0
		ORDER BY ORD
	</select>


<select id="selectSaleAreaChildren" resultClass="java.util.HashMap">
		SELECT ID "id",
		       PARENT_ID "pId",
		       NAME "name",
		       CASE
		         WHEN EXISTS
  (SELECT 1 FROM ORG_ORGANIZATION_MOD A WHERE A.PARENT_ID = T.ID) THEN
		          'true'
		         ELSE
		          'false'
		       END "isParent",
		       '|' || replace(ORGRANK_NAME,'&gt;','|') "namepath"
        FROM PURE.ORG_ORGANIZATION_MOD T
		WHERE T.PARENT_ID = #id#
		AND T.DELFLAG = 0
		AND INSTR(T.ORGRANK, 'root') > 0
		ORDER BY ORD
	</select>

   <select id="selectSaleAreaDesc" resultClass="java.util.HashMap">
		  select  
			t.id "id",
		    t.parent_id "pid",
		    t.name "name",
		    t.org_type  "orgType",
		    case when t.areacode = '21' then '-1' when t.areacode='2100' then '-1' else t.areacode end  "areaCode",
			'|' || replace(t.ORGRANK_NAME,'&gt;','|') "namepath"
           FROM ORG_ORGANIZATION_MOD T
		   where t.id=#saleId#
		   and instr(t.orgrank, #loginSale#) > 0
	</select>
	
	<select id="selectDeptDesc" resultClass="java.util.HashMap">
		    select '|' || replace(t.ORGRANK_NAME,'&gt;','|') "namepath", t.name "name", t.id DEPARTMENT
		    from org_organization_mod t
			where t.id=#deptCode#
			and instr(t.orgrank, #loginSale#) > 0
	</select>
	

</sqlMap>
