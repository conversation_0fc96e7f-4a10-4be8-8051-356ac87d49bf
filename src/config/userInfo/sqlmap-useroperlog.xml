<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="pure.gbs.userlog">
<insert id="insUserLog"> 
		
		 <![CDATA[
			insert into pure_useroper_log a
			  (OPER_LOG_ID,
			   OPER_USER_ID,
			   OPER_TIME,
			   USER_ID,
			   PASSWORD,
			   USER_NAME,
			   ADMIN,
			   SEX,
			   EMAIL,
			   MOBILE,
			   TELEPHONE,
			   STATE,
			   PWD_STATE,
			   LOGIN_ID_OLD,
			   AREA_NO,
			   SALE_AREA,
			   DEPT_ID,
			   DEPT_CODE,
			   DEPT_NO)
			  select seq_useroperlog_id.nextval OPER_LOG_ID,
			         #operUserId#  OPER_USER_ID,
			         sysdate,
			         USER_ID,
			         PASSWORD,
			         USER_NAME,
			         ADMIN,
			         SEX,
			         EMAIL,
			         MOBILE,
			         TELEPHONE,
			         STAT<PERSON>,
			         <PERSON><PERSON>_STATE,
			         LOGIN_ID_OLD,
			         AREA_NO,
			         SALE_AREA,
			         DEPT_ID,
			         DEPT_CODE,
			         DEPT_NO
			    from pure_user where user_id=#userId#
		    ]]>    
	</insert>
</sqlMap>
