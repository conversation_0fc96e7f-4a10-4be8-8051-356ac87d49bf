<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="pure.gbs.dept">

<select id="selectDeptRoot" resultClass="java.util.HashMap">
		select department "id",
	         parent_dept "pId",
	         department_desc "name",
	         case
	           when isParent > 0 then
	            'true'
	           else
	            'false'
	         end "isParent"
	    from (select t.department,
	                 t.parent_dept,
	                 t.department_desc,
	                 (select count(1)
	                    from code_department t1
	                   where t1.parent_dept = #deptId#) isParent
	            from code_department t
	           where t.department=#deptId#
	           order by ord)
	</select>


<select id="selectDeptChildren" resultClass="java.util.HashMap">
		select department "id",
	         parent_dept "pId",
	         department_desc "name",
	         case
	           when isParent > 0 then
	            'true'
	           else
	            'false'
	         end "isParent"
	    from (select t.department,
	                 t.parent_dept,
	                 t.department_desc,
	                 (select count(1)
	                    from code_department t1
	                   where t1.parent_dept = t.department) isParent
	            from code_department t
	           where t.parent_dept = #id#
	           order by ord)
	</select>

   <select id="selectDeptDesc" resultClass="java.util.HashMap">
		  select  SYS_CONNECT_BY_PATH(t.DEPARTMENT_DESC, '|') "namepath", t.DEPARTMENT_DESC "name", t.DEPARTMENT
		    from code_department t
		   where t.DEPARTMENT=#deptId#
		   start with t.DEPARTMENT = #loginDept#
		 connect by prior t.DEPARTMENT = t.PARENT_DEPT
	</select>
	
	<select id="selectDeptDescAll" resultClass="java.util.HashMap">
		  select t.DEPARTMENT "id",
		         t.PARENT_DEPT "pid",
		         t.DEPARTMENT_DESC "name",
		         SYS_CONNECT_BY_PATH(t.DEPARTMENT_DESC, '|') "namepath"     
		    from code_department t
		   where t.bss_depart=#saleId#
		   start with t.DEPARTMENT = #loginDept#
		 connect by prior t.DEPARTMENT = t.PARENT_DEPT
	</select>
	
	<select id="selectCustDispatch" resultClass="java.util.HashMap">
		select t.login_id_old "loginIdOld",
	       t.gz           "gz",
	       t.dkh          "dkh",
	       t.zx           "zx",
	       t.xy           "xy"
	  	from PURE_USER_ASSIGN t
	  	where t.login_id_old = #loginInOld#
		
	</select>


</sqlMap>
