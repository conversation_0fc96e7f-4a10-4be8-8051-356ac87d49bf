<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="market.grid.CostCityImp">


    <!-- 根据orgId获取节点信息  -->
    <select id="queryGradeByOrgId" resultClass="com.bonc.product.market.vo.OrgInfoVo">
        select ID "id", NAME "name", PARENT_ID "pid", GRADE "grade" from PURE_GS.ORG_ORGANIZATION_MOD t
        where t.ID = '$orgId$' and rownum = 1
    </select>

    <!-- 组织结构列表(显示当前登录用户saleId及所有下级节点) -->
    <select id="queryOrgs" resultClass="java.util.HashMap" remapResults="true">
        WITH RECURSIVE_ORG (ID, NAME, PARENT_ID, GRADE, ORD) AS (
        <!--锚成员：查询根节点，ID 为 '$orgId$'-->
        SELECT t.ID,
        t.NAME,
        t.PARENT_ID,
        t.GRADE,
        t.ORD
        FROM PURE_GS.ORG_ORGANIZATION_MOD t
        WHERE t.ID = '$orgId$'
        UNION ALL
        <!--递归成员：查询子节点-->
        SELECT c.ID,
        c.NAME,
        c.PARENT_ID,
        c.GRADE,
        c.ORD
        FROM PURE_GS.ORG_ORGANIZATION_MOD c
        JOIN RECURSIVE_ORG parent ON c.PARENT_ID = parent.ID)
        SELECT *
        FROM RECURSIVE_ORG
        where GRADE <![CDATA[ <= ]]> 2
        order by GRADE, ORD
    </select>

    <!-- 获取动态表头 -->
    <select id="getDynamicColumns" resultClass="java.util.HashMap">
        SELECT ROWNUM AS "INDEX", t.*
        FROM (
            select ROLL_ID, KPI_CODE, KPI_NAME
            from G_GA.DIM_M_CUS_COST_CITY_INPUT
            order by ROLL_ID
        ) t
    </select>

    <!-- 获取数据列表信息 -->
    <select id="queryList" resultClass="java.util.HashMap">
        SELECT *
        FROM (
        <!--明确选择需要参与分组的列-->
        <!--在Oracle的PIVOT语法中，所有未参与旋转的列都会自动成为分组条件-->
            SELECT
                MONTH_ID,
                AREA_ID,
                AREA_NAME,
                CITY_ID,
                CITY_NAME,
                IMP_USER,
                TO_CHAR(IMP_DATE, 'YYYY-MM-DD HH24:MI:SS') AS IMP_DATE,
                KPI_CODE,
                KPI_VALUE
            FROM G_GA.DM_M_CUS_COST_CITY_INPUT
            WHERE MONTH_ID = '$monthId$'
            <!-- 选中的是地市，只查询地市 -->
            <isEqual property="grade" compareValue="2">
                and AREA_ID = '$orgId$'
            </isEqual>
        )
        PIVOT (
        MAX(KPI_VALUE)
        FOR KPI_CODE IN (
            $sql$
        )
        )
        ORDER BY AREA_ID, CITY_ID
    </select>

    <!-- 获取模板数据列表 -->
    <select id="queryTemplateData" resultClass="java.util.HashMap">
        select t.AREA_ID, t.AREA_NAME, t.CITY_ID, t.CITY_NAME
        from G_GA.DIM_ORG_ORGANIZATION_TWO t
        where 1=1
        <!-- 选中的是地市，只查询地市 -->
        <isEqual property="grade" compareValue="2">
            and t.AREA_ID = '$orgId$'
        </isEqual>
        order by t.AREA_ID, t.CITY_ID
    </select>


    <!-- 批量插入 -->
    <insert id="insertDataBatch" parameterClass="list">
        insert all
        <iterate conjunction="">
            into G_GA.DM_M_CUS_COST_CITY_INPUT
            (
            MONTH_ID, PROVINCECODE, PROV_NAME, AREA_ID, AREA_NAME, CITY_ID, CITY_NAME, KPI_CODE, KPI_VALUE,
            IMP_DATE, IMP_USER
            )
            values
            (
            #rows[].MONTH_ID#, #rows[].PROVINCECODE#, #rows[].PROV_NAME#, #rows[].AREA_ID#, #rows[].AREA_NAME#,
            #rows[].CITY_ID#, #rows[].CITY_NAME#, #rows[].KPI_CODE#, #rows[].KPI_VALUE#,
            TO_DATE(#rows[].IMP_DATE#, 'YYYY-MM-DD HH24:MI:SS'), #rows[].IMP_USER#
            )
        </iterate>
        　　<!--下面这句必须加，不然会提示找不到SELECT-->
        　　select * from dual
    </insert>


    <!-- 根据ID更新数据 -->
    <!--<update id="updateDataById">
        update G_GA.G_GA_JCYW_INPUT_M
        <dynamic prepend="set">
            <isNotEmpty property="oweFee">
                OWE_FEE = #oweFee#
            </isNotEmpty>
            <isNotEmpty property="debtFeePre">
                DEBT_FEE_PRE = #debtFeePre#
            </isNotEmpty>
            <isNotEmpty property="debtFee">
                DEBT_FEE = #debtFee#
            </isNotEmpty>
        </dynamic>
        where ID = #id#
    </update>-->

    <!-- 根据账期删除数据 -->
    <delete id="deleteDataByMonthId">
        delete from G_GA.DM_M_CUS_COST_CITY_INPUT
        where MONTH_ID = '$monthId$'
    </delete>

</sqlMap>