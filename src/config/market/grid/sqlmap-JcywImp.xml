<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="market.grid.JcywImp">


    <!-- 根据orgId获取节点信息  -->
    <select id="queryGradeByOrgId" resultClass="com.bonc.product.market.vo.OrgInfoVo">
        select ID "id", NAME "name", PARENT_ID "pid", GRADE "grade" from PURE_GS.ORG_ORGANIZATION_MOD t
        where t.ID = '$orgId$' and rownum = 1
    </select>

    <!-- 组织结构列表(显示当前登录用户saleId及所有下级节点) -->
    <select id="queryOrgs" resultClass="java.util.HashMap" remapResults="true">
        WITH RECURSIVE_ORG (ID, NAME, PARENT_ID, GRADE, ORD) AS (
        <!--锚成员：查询根节点，ID 为 '$orgId$'-->
        SELECT t.ID,
        t.NAME,
        t.PARENT_ID,
        t.GRADE,
        t.ORD
        FROM PURE_GS.ORG_ORGANIZATION_MOD t
        WHERE t.ID = '$orgId$'
        UNION ALL
        <!--递归成员：查询子节点-->
        SELECT c.ID,
        c.NAME,
        c.PARENT_ID,
        c.GRADE,
        c.ORD
        FROM PURE_GS.ORG_ORGANIZATION_MOD c
        JOIN RECURSIVE_ORG parent ON c.PARENT_ID = parent.ID)
        SELECT *
        FROM RECURSIVE_ORG
        where GRADE <![CDATA[ <= ]]> 2
        order by GRADE, ORD
    </select>


    <!-- 获取数据列表信息 -->
    <select id="queryList" resultClass="com.bonc.product.market.easyexcel.model.JcywImp">
        select
        b.ID            "id",
        b.ACCT_MONTH    "monthId",
        a.ORD_ID        "index",
        a.AREA_ID       "areaNo",
        a.AREA_NAME     "areaName",
        a.MARKET_ID     "marketCode",
        a.MARKET_NAME   "marketName",
        a.GRID_ID       "gridId",
        a.GRID_NAME     "gridName",
        a.CC_CODE       "ccCode",
        a.CC_NAME       "ccDesc",
        b.Y_JCZY_INCOME "yyJczyIncome",
        b.Y_JCQT_INCOME "yyJcqtIncome",
        b.Y_JCICT_COST  "yyJcictCost",
        b.Y_JCQT_COST   "yyJcqtCost",
        b.Y_JCJT_BAD    "yyJcjtBad",
        b.JCZY_INCOME   "jczyIncome",
        b.JCQT_INCOME   "jcqtIncome",
        b.JCICT_COST    "jcictCost",
        b.JCQT_COST     "jcqtCost",
        b.JCJT_BAD      "jcjtBad",
        b.L_JCZY_INCOME "llJczyIncome",
        b.L_JCQT_INCOME "llJcqtIncome",
        b.L_JCICT_COST  "llJcictCost",
        b.L_JCQT_COST   "llJcqtCost",
        b.L_JCJT_BAD    "llJcjtBad",
        b.INSERT_DATE   "insertDate",
        b.OPERATOR      "operator"

        from PURE_GS.VIEW_COST_CONFIGURE_RELATION a
        left join (select * from G_GA.G_GA_JCYW_INPUT_M where ACCT_MONTH = '$monthId$') b
        on a.CC_CODE = b.CC_CODE
        where 1=1
        <!-- 选中的是地市，只查询地市 -->
        <isEqual property="grade" compareValue="2">
            and a.AREA_ID = '$orgId$'
        </isEqual>
        order by a.ord_id, b.INSERT_DATE desc nulls last
    </select>

    <!-- 获取模板数据列表 -->
    <select id="queryTemplateData" resultClass="com.bonc.product.market.easyexcel.model.JcywImp">
        select
        a.ORD_ID        "index",
        a.AREA_ID       "areaNo",
        a.AREA_NAME     "areaName",
        a.MARKET_ID     "marketCode",
        a.MARKET_NAME   "marketName",
        a.GRID_ID       "gridId",
        a.GRID_NAME     "gridName",
        a.CC_CODE       "ccCode",
        a.CC_NAME       "ccDesc"
        from PURE_GS.VIEW_COST_CONFIGURE_RELATION a
        where 1=1
        <!-- 选中的是地市，只查询地市 -->
        <isEqual property="grade" compareValue="2">
            and a.AREA_ID = '$orgId$'
        </isEqual>
        order by a.ord_id
    </select>


    <!-- 批量插入 -->
    <insert id="insertDataBatch" parameterClass="list">
        insert all
        <iterate conjunction="">
            into G_GA.G_GA_JCYW_INPUT_M
            (
            ACCT_MONTH, ROLL_NUMBER, AREA_NO, AREA_NAME, MARKET_CODE, MARKET_NAME, CC_CODE, CC_DESC,
            Y_JCZY_INCOME, Y_JCQT_INCOME, Y_JCICT_COST, Y_JCQT_COST, Y_JCJT_BAD,
            JCZY_INCOME, JCQT_INCOME, JCICT_COST, JCQT_COST, JCJT_BAD,
            L_JCZY_INCOME, L_JCQT_INCOME, L_JCICT_COST, L_JCQT_COST, L_JCJT_BAD,
            OPERATOR
            )
            values
            (
            #rows[].monthId#, #rows[].index#, #rows[].areaNo#, #rows[].areaName#, #rows[].marketCode#, #rows[].marketName#,
            #rows[].ccCode#, #rows[].ccDesc#,
            #rows[].yyJczyIncome#, #rows[].yyJcqtIncome#, #rows[].yyJcictCost#, #rows[].yyJcqtCost#, #rows[].yyJcjtBad#,
            #rows[].jczyIncome#, #rows[].jcqtIncome#, #rows[].jcictCost#, #rows[].jcqtCost#, #rows[].jcjtBad#,
            #rows[].llJczyIncome#, #rows[].llJcqtIncome#, #rows[].llJcictCost#, #rows[].llJcqtCost#, #rows[].llJcjtBad#,
            #rows[].operator#
            )
        </iterate>
        　　<!--下面这句必须加，不然会提示找不到SELECT-->
        　　select * from dual
    </insert>


    <!-- 根据ID更新数据 -->
    <!--<update id="updateDataById">
        update G_GA.G_GA_JCYW_INPUT_M
        <dynamic prepend="set">
            <isNotEmpty property="oweFee">
                OWE_FEE = #oweFee#
            </isNotEmpty>
            <isNotEmpty property="debtFeePre">
                DEBT_FEE_PRE = #debtFeePre#
            </isNotEmpty>
            <isNotEmpty property="debtFee">
                DEBT_FEE = #debtFee#
            </isNotEmpty>
        </dynamic>
        where ID = #id#
    </update>-->

    <!-- 根据账期删除数据 -->
    <delete id="deleteDataByMonthId">
        delete from G_GA.G_GA_JCYW_INPUT_M
        where ACCT_MONTH = '$monthId$'
    </delete>

</sqlMap>