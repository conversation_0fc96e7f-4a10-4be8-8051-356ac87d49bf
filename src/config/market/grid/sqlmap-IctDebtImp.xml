<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="market.grid.IctDebtImp">


    <!-- 根据orgId获取节点信息  -->
    <select id="queryGradeByOrgId" resultClass="com.bonc.product.market.vo.OrgInfoVo">
        select ID "id", NAME "name", PARENT_ID "pid", GRADE "grade" from PURE_GS.ORG_ORGANIZATION_MOD t
        where t.ID = '$orgId$' and rownum = 1
    </select>

    <!-- 组织结构列表(显示当前登录用户saleId及所有下级节点) -->
    <select id="queryOrgs" resultClass="java.util.HashMap" remapResults="true">
        WITH RECURSIVE_ORG (ID, NAME, PARENT_ID, GRADE, ORD) AS (
        <!--锚成员：查询根节点，ID 为 '$orgId$'-->
        SELECT t.ID,
        t.NAME,
        t.PARENT_ID,
        t.GRADE,
        t.ORD
        FROM PURE_GS.ORG_ORGANIZATION_MOD t
        WHERE t.ID = '$orgId$'
        UNION ALL
        <!--递归成员：查询子节点-->
        SELECT c.ID,
        c.NAME,
        c.PARENT_ID,
        c.GRADE,
        c.ORD
        FROM PURE_GS.ORG_ORGANIZATION_MOD c
        JOIN RECURSIVE_ORG parent ON c.PARENT_ID = parent.ID)
        SELECT *
        FROM RECURSIVE_ORG
        where GRADE <![CDATA[ <= ]]> 2
        order by GRADE, ORD
    </select>


    <!-- 获取数据列表信息 -->
    <select id="queryUserList" resultClass="com.bonc.product.market.easyexcel.model.IctDebtImp">
        select *
        from g_ga.DM_M_ICT_DEBT_IMP t
        WHERE t.MONTH_ID = '$monthId$'
        <!-- 选中的是地市，只查询地市 -->
        <isEqual property="grade" compareValue="2">
            and t.AREA_NAME = '$orgName$'
        </isEqual>
        order by t.IMP_DATE desc nulls last
    </select>

    <!-- 批量插入 -->
    <insert id="insertDataBatch" parameterClass="list">
        insert all
        <iterate conjunction="">
            into g_ga.DM_M_ICT_DEBT_IMP
            (
            PROV_DESC, AREA_NAME, MONTH_ID, CYCLE_MONTH,
            PROJECT_CODE, PROJECT_NAME, PROJECT_MANAGER, CON_CODE, CON_NAME,
            TK_CODE, TK_KH_CODE, TK_KH_NAME, SVC_TYPE, OWE_FEE,
            DEBT_FEE_PRE, DEBT_FEE, SP_FINISH_DATE, COMPANY_NAME, SALE_DEPT_CODE,
            SALE_DEPT_NAME, SALE_MANAGER_CODE, SALE_MANAGER_NAME, SALE_MANAGER_PHONE,
            PRODUCT_LINE, PRODUCT_LINE_NAME, GUANLIANFANG, IMP_USER
            )
            values
            (
            #rows[].PROV_DESC#, #rows[].AREA_NAME#, #rows[].MONTH_ID#, #rows[].CYCLE_MONTH#,
            #rows[].PROJECT_CODE#, #rows[].PROJECT_NAME#, #rows[].PROJECT_MANAGER#, #rows[].CON_CODE#,
            #rows[].CON_NAME#,
            #rows[].TK_CODE#, #rows[].TK_KH_CODE#, #rows[].TK_KH_NAME#, #rows[].SVC_TYPE#, #rows[].OWE_FEE#,
            #rows[].DEBT_FEE_PRE#, #rows[].DEBT_FEE#, #rows[].SP_FINISH_DATE#, #rows[].COMPANY_NAME#,
            #rows[].SALE_DEPT_CODE#,
            #rows[].SALE_DEPT_NAME#, #rows[].SALE_MANAGER_CODE#, #rows[].SALE_MANAGER_NAME#,
            #rows[].SALE_MANAGER_PHONE#,
            #rows[].PRODUCT_LINE#, #rows[].PRODUCT_LINE_NAME#, #rows[].GUANLIANFANG#, #rows[].IMP_USER#
            )
        </iterate>
        　　<!--下面这句必须加，不然会提示找不到SELECT-->
        　　select * from dual
    </insert>


    <!-- 根据ID更新数据 -->
    <update id="updateDataById">
        update g_ga.DM_M_ICT_DEBT_IMP
        <dynamic prepend="set">
            <isNotEmpty property="oweFee">
                OWE_FEE = #oweFee#
            </isNotEmpty>
            <isNotEmpty property="debtFeePre">
                DEBT_FEE_PRE = #debtFeePre#
            </isNotEmpty>
            <isNotEmpty property="debtFee">
                DEBT_FEE = #debtFee#
            </isNotEmpty>
        </dynamic>
        where ID = #id#
    </update>

    <!-- 根据账期删除数据 -->
    <delete id="deleteDataByMonthId">
        delete from g_ga.DM_M_ICT_DEBT_IMP
        where MONTH_ID = '$monthId$'
    </delete>

</sqlMap>