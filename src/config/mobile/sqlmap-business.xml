<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="business">
<!-- 库存查询 -->
<select id="queryStock"  resultClass="java.lang.String"> 	   
      select  mobile_number  "quantity"  from  jike.inf_terminal_library  
      where 1=1
      <isNotEmpty  property="brand">
         and mobile_brand = '$brand$'
      </isNotEmpty>
      <isNotEmpty  property="model">
     	 and mobile_type = '$model$'
      </isNotEmpty>
      <isNotEmpty  property="memory">
     	 and memory='$memory$'
      </isNotEmpty>
      <isNotEmpty  property="cards">
      	and simple_double_cards = '$cards$'
      </isNotEmpty>
      <isNotEmpty  property="color">
     	 and color='$color$'
      </isNotEmpty>
</select>
<select id="listTerminalStock"  resultClass="java.util.HashMap"> 	   
      select  t.*,a.AREA_ID_DESC_PROV from  jike.inf_terminal_library  t,dim.dim_area_no a  
      where 1=1
      and t.city = a.area_no
      <isNotEqual property="areaNo" compareValue="-1">
      	 and CITY = '$areaNo$'
      </isNotEqual>
      <isNotEmpty  property="cityNo">
      	<isNotEqual property="cityNo" compareValue="018">
         and CITY = '$cityNo$'
         </isNotEqual>
      </isNotEmpty>
      <isNotEmpty  property="mobileType">
     	 and MOBILE_TYPE = '$mobileType$'
      </isNotEmpty>
      <isNotEmpty  property="mobileBrand">
     	 and MOBILE_BRAND='$mobileBrand$'
      </isNotEmpty>
      order by CITY,MOBILE_NUMBER desc
</select>
<select id="getTerminalStock" resultClass="java.util.HashMap">
select  * from  jike.inf_terminal_library where STOCK_ID = '$stockId$'
</select>
<insert id="insertStock">
	insert into jike.inf_terminal_library(
	STOCK_ID,CITY,MOBILE_MODEL,MOBILE_BRAND,
	MOBILE_TYPE,SIMPLE_DOUBLE_CARDS,MEMORY,
	COLOR,MOBILE_NUMBER,CONTRACT_PRICE
	) values(
	jike.seq_terminal_library.nextval,'$CITY$','$MOBILE_MODEL$','$MOBILE_BRAND$',
	'$MOBILE_TYPE$','$SIMPLE_DOUBLE_CARDS$','$MEMORY$',
	'$COLOR$','$MOBILE_NUMBER$','$CONTRACT_PRICE$'
	)
</insert>
<update id="updateStock">
	update jike.inf_terminal_library set CITY = '$CITY$',
	MOBILE_MODEL = '$MOBILE_MODEL$',MOBILE_BRAND = '$MOBILE_BRAND$',
	MOBILE_TYPE = '$MOBILE_TYPE$',SIMPLE_DOUBLE_CARDS = '$SIMPLE_DOUBLE_CARDS$',
	MEMORY = '$MEMORY$',COLOR='$COLOR$',MOBILE_NUMBER='$MOBILE_NUMBER$',CONTRACT_PRICE = '$CONTRACT_PRICE$'
	where STOCK_ID = '$STOCK_ID$'
</update>
<delete id="deleteStock">
delete from jike.inf_terminal_library where STOCK_ID = '$value$'
</delete>
<delete id="deleteStockByCity">
delete from jike.inf_terminal_library where CITY = '$value$'
</delete>

</sqlMap>
