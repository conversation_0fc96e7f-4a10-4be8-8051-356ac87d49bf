<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="plan">

<!-- 套餐种类 -->
<select id="getLtPlanType"  resultClass="java.util.HashMap"> 	   
     select distinct t.product_name from jike.inf_std_products t 
     where t.is_compare ='0' and t.sp_id='0'
 </select>
<select id="getDxPlanType"  resultClass="java.util.HashMap"> 	   
     select distinct t.product_name from jike.inf_std_products t 
     where t.is_compare ='0' and t.sp_id='2'
</select>
<select id="getYdPlanType"  resultClass="java.util.HashMap"> 	   
     select distinct t.product_name from jike.inf_std_products t 
     where t.is_compare ='0' and t.sp_id='1'
</select>
<select id="getPlans"  resultClass="java.util.HashMap"> 	   
     SELECT t.PLAN_NAME "plans_name",t.pid "plans_code"
     from jike.inf_std_products  t where product_name='$product_name$'
</select>
<!-- 对比详情列表 -->
<select id="getPlansDetail" resultClass="java.util.HashMap">
	select plan_name     "plans_name",
       t.pid     "plans_code",
       t.contain_calls  "contain_calls",
       t.contain_flows  "contain_flows",
       t.contain_sms    "contain_sms",
       t.contain_answer "contain_answer",
       t.outstrip_calls "outstrip_calls",
       t.outstrip_flows "outstrip_flows",
       t.outstrip_video "outstrip_video",
       t.give_m_number  "give_m_number",
       t.give_t_number  "give_t_number",
       t.give_video     "give_video",
       t.give_sp     "give_added",
       t.contain_mms "contain_mms",
       t.contain_wifi "contain_wifi"
  from jike.inf_std_products t where t.pid in('$id1$','$id2$')
</select>
<insert id="insertEvaluate">
	insert into jike.inf_product_evaluate(staff_id,show_type,id,evaluate_level,oper_time) values(
		'$staff_id$','$show_type$','$id$','$level$',sysdate
	)
</insert>
<select id="getOrgRank" resultClass="string">
	select t.orgrank  from (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL))t ,PURE.pure_user p where t.id=p.dept_code and p.login_id='$staff_id$'
</select>
<!-- 公告查询 -->
<select id="getMsg" resultClass="java.util.HashMap">
 select t.msg_id "msg_id",
       t.msg_title "msg_title",
       t.msg_contect "msg_content",
       t.msg_sender "msg_sender",
       (case when p.msg_id is null then
         0
         else
           1
       end) "isread"
  from PURE.info_common_message_new t ,
  (select * from jike.inf_common_message_read o where o.staff_id='$staff_id$') p
 where t.msg_id=p.msg_id(+)
 <!-- and  instr('$orgRank$',t.msg_sender_department) > 0  -->
</select>
<select id="getMsgCount" resultClass="string">
select count(*) 
  from PURE.info_common_message_new t ,
  (select * from jike.inf_common_message_read o where o.staff_id='$staff_id$') p
 where t.msg_id=p.msg_id(+)
 and  instr('$orgRank$',t.msg_sender_department) > 0
 and p.msg_id is null
</select>
<select id="getModel" resultClass="java.util.HashMap">
	select p.resources_id  "resources_id",i.parent_id "parent_id"
    from PURE.pure_user            t,
       PURE.pure_user_role       o,
       PURE.pure_role_permission p,
       PURE.pure_resources i
 where t.user_id = o.user_id
   and i.resources_id =p.resources_id
   and o.role_id = p.role_id
   and t.login_id = '$staff_id$'
   AND ( (I.RESOURCES_ID LIKE '%MB%'
    OR I.RESOURCES_ID LIKE '%MOBILE%')
   AND I.RESOURCES_ID &lt; &gt; 'PURE_MOBILE'     )
</select>
<insert id="insertReadMsg">
	insert into jike.inf_common_message_read (msg_id,staff_id,oper_time) values(
		'$msg_id$','$staff_id$',sysdate
	)
</insert>
<select id="getPerformanceAll" resultClass="java.util.HashMap">
	select 
	   t.tele_type "tele_type",
       t.brand "brand",
       t.dev_day "dev_day",
       t.dev_month "dev_month",
       t.dev_year "dev_year",
       t.dev_accum "dev_accum",
       t.day_dist_rank "day_dist_rank",
       t.day_city_rank  "day_city_rank",
       t.day_prov_rank "day_prov_rank",
       t.month_dist_rank "month_dist_rank",
       t.month_city_rank "month_city_rank",
       t.month_prov_rank "month_prov_rank",
       t.year_dist_rank "year_dist_rank",
       t.year_city_rank "year_city_rank",
       t.year_prov_rank  "year_prov_rank"
	from jike.DCD_RPT_STAFF_DEVELOP t
 	where t.acct_day = '$acct_day$'
   	and t.staff_id = '$staff_id$'
</select>
<select id="getPerformanceMobile" resultClass="java.util.HashMap">
	select 
	   t.tele_type "tele_type",
       t.brand "brand",
       t.dev_day "dev_day",
       t.dev_month "dev_month",
       t.dev_year "dev_year",
       t.dev_accum "dev_accum",
       t.day_dist_rank "day_dist_rank",
       t.day_city_rank  "day_city_rank",
       t.day_prov_rank "day_prov_rank",
       t.month_dist_rank "month_dist_rank",
       t.month_city_rank "month_city_rank",
       t.month_prov_rank "month_prov_rank",
       t.year_dist_rank "year_dist_rank",
       t.year_city_rank "year_city_rank",
       t.year_prov_rank  "year_prov_rank"
	from jike.DCD_RPT_STAFF_DEVELOP_MOBILE t
 	where t.acct_day = '$acct_day$'
   	and t.staff_id = '$staff_id$'
</select>
<select id="getPreOrder" resultClass="java.util.HashMap">
	select t.order_id     "order_id",
       t.status       "status",
       t.bcc_order_id "bcc_order_id"
    from jike.inf_pre_order t,jike.inf_acce_order p
   where p.order_id=t.order_id 
   and p.staff_id='$staff_id$'
 	<isNotEmpty prepend="and" property="order_id">
 		t.order_id='$order_id$'
 	</isNotEmpty>
	
</select>
<select id="getReadMsg" resultClass="java.util.HashMap">
	select * from  jike.inf_common_message_read t
	where t.staff_id='$staff_id$' 
	and t.msg_id='$msg_id$'
</select>
<select id="getEvaluate" resultClass="java.util.HashMap">
	select * from jike.inf_product_evaluate t 
	where t.staff_id='$staff_id$'
	and t.show_type='$show_type$'
	and t.id='$id$'
</select>
</sqlMap>
