<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="mobile.stock">
	<select id="superSumNew" resultClass="java.util.HashMap">
		select nvl(t.chnl_id,'') t0,
  			   case when t.chnl_id is null then '合计' else max(t.chnl_desc) end name,
		       nvl(sum(t.cd_sellin_card_d ),0) t1 
		  from g_ga.DM_GMV_STORE_DS T
		 where 1=1
		 and chnl_id in (
		 SELECT DISTINCT CHANNEL_NO CHAN_ID
  FROM GBS_GI.G_GI_CHANNEL_EXT T
 WHERE T.CHANNEL_MANAGER = '$GMId$'
	<!-- 	  	  SELECT distinct a.chnl_code CHAN_ID
    			FROM PURE.CODE_CHNL_DEPART_patrol a,
    			(SELECT id from (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) start with id = (select dept_code from PURE.pure_user where login_id='$GMId$') connect BY prior id = parent_id) b 
   				where a.sale_id=b.id --> 
	          )
		 <isNotNull property="dateId">
	   	 	<isNotEqual property="dateId" compareValue="">
	          	 AND T.ACCT_DATE = '$dateId$'
	        </isNotEqual>     
		 </isNotNull>
		 <isNotNull property="type">
	   	 	<isNotEqual property="type" compareValue="">
	          	 AND T.TYPE = '$type$'
	        </isNotEqual>     
		 </isNotNull>
		 
		 <isNotNull property="channelIdDesc">
			 <isNotEqual property="channelIdDesc" compareValue="">
				  and t.chnl_desc like '%$channelIdDesc$%'
			 </isNotEqual>
		 </isNotNull>
		 
		 <!-- 
		 <isNotNull property="channelTypeNew">
			 <isEqual property="channelTypeNew" compareValue="A">签约渠道
				  and t.chnl_type in ('503','504')
			 </isEqual>
			 <isEqual property="channelTypeNew" compareValue="B">全部渠道
				  AND T.CHNL_TYPE IN
			               (SELECT t.id CHANNEL_TYPE 
                                    
			                          FROM(SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL))t
			                         START WITH t.id = 'root'
			                        CONNECT BY PRIOR t.id = t.parent_id)
			</isEqual>
		 </isNotNull>
		  -->
		 group by rollup(t.chnl_id)
		 <dynamic prepend=" order by ">
			<isNotNull property="sortCol">
				$sortCol$
				<isNotNull property="sortOrder">$sortOrder$</isNotNull>
			</isNotNull>
		</dynamic>
	</select>
	
	<!-- 库存title -->
	<select id="titleMainList" resultClass="java.util.HashMap" remapResults="true">
	  select   '1'ord ,
	  		   'name' key_, 
	           '网点名称' desc_,
	           '*s' format
	           from dual
	  union 
	  select    '2'ord ,
	  			'T1' key_, 
	           '成卡库存' desc_,
	           '*d' format
	           from dual
	</select>
	
	<select id="listNew" resultClass="java.util.HashMap">
			select
		      nvl(sum(t.cd_sellin_card_d),0) V1 
			  from g_ga.DM_GMV_STORE_DS t
			 where 1=1
			  and chnl_id in (
			  SELECT DISTINCT CHANNEL_NO CHAN_ID
  FROM GBS_GI.G_GI_CHANNEL_EXT T
 WHERE T.CHANNEL_MANAGER = '$GMId$'
	<!-- 	  	  SELECT distinct a.chnl_code CHAN_ID
    			FROM PURE.CODE_CHNL_DEPART_patrol a,
    			(SELECT id from (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) start with id = (select dept_code from PURE.pure_user where login_id='$GMId$') connect BY prior id = parent_id) b 
   				where a.sale_id=b.id --> 
	         ) 
			<isNotNull property="dateId">
		   	 	<isNotEqual property="dateId" compareValue="">
		          	 AND T.ACCT_DATE = '$dateId$'
		        </isNotEqual>     
			</isNotNull>
			 <isNotNull property="type">
		   	 	<isNotEqual property="type" compareValue="">
		          	 AND T.TYPE = '$type$'
		        </isNotEqual>     
			 </isNotNull>
			
		    <isNotNull property="channelId">
				 <isNotEqual property="channelId" compareValue="">
					  and T.chnl_id='$channelId$'
				 </isNotEqual>
			 </isNotNull>
			 <!--
			 <isNotNull property="channelTypeNew">
				 <isEqual property="channelTypeNew" compareValue="A">签约渠道
					  and t.chnl_type in ('503','504')
				 </isEqual>
				 <isEqual property="channelTypeNew" compareValue="B">全部渠道
					  AND T.CHNL_TYPE IN
				               (SELECT t.id CHANNEL_TYPE 
                                    
			                          FROM(SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL))t
			                         START WITH t.id = 'root'
			                        CONNECT BY PRIOR t.id = t.parent_id)
					
				</isEqual>
			 </isNotNull>	
			 -->
			 <!-- 
			 <isNotNull property="monthId">
		   	 	<isNotEqual property="monthId" compareValue="">
				 and t.sale_month between
				       to_char(add_months(to_date('$monthId$', 'yyyyMM'), -0), 'yyyyMM') and
				       '$monthId$'
			  </isNotEqual>     
		    </isNotNull>     
		     -->  
		     <isNotNull property="dateId">
		   	 	<isNotEqual property="dateId" compareValue="">
				 and t.acct_date between
				 	   to_char(to_date('$dateId$','yyyymmdd')-0,'yyyymmdd')
				        and '$dateId$'
			  </isNotEqual>     
		    </isNotNull> 
		    
			 group by t.acct_date
			 <dynamic prepend=" order by ">
				<isNotNull property="sortCol">
					$sortCol$
					<isNotNull property="sortOrder">$sortOrder$</isNotNull>
				</isNotNull>
			</dynamic>
	</select>
	
	<select id="datatitleList" resultClass="java.util.HashMap" remapResults="true">
		select   'v1' key_, 
	             '成卡库存' desc_,
	             '*d' format,
	             '张' formatUnit
	             from dual
	</select>
	
	<select id="titleList" resultClass="java.util.HashMap" remapResults="true">
	select   '1'ord ,
             'name' key_, 
             '指标' desc_
             from dual
    union 
    select   '2'ord,
             'format' key_, 
             '单位' desc_
             from dual
    union
    select   '3'ord,
             'value' key_, 
             '数值' desc_
             from dual
	</select>
	
	<select id="chartlistNew" resultClass="java.util.HashMap" remapResults="true">
		select t.acct_date month_,
			   <isEqual property="chartId" compareValue="v1">
		       sum(t.cd_sellin_card_d) V1
		       </isEqual>
		       
			  from g_ga.DM_GMV_STORE_DS t
			 where 1=1
			  and chnl_id in (
		  SELECT DISTINCT CHANNEL_NO CHAN_ID
  FROM GBS_GI.G_GI_CHANNEL_EXT T
 WHERE T.CHANNEL_MANAGER = '$GMId$'
	<!-- 	  	  SELECT distinct a.chnl_code CHAN_ID
    			FROM PURE.CODE_CHNL_DEPART_patrol a,
    			(SELECT id from (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) start with id = (select dept_code from PURE.pure_user where login_id='$GMId$') connect BY prior id = parent_id) b 
   				where a.sale_id=b.id --> 
	          )
	         
			<isNotNull property="dateId">
		   	 	<isNotEqual property="dateId" compareValue="">
		          	 AND T.ACCT_DATE &lt;= '$dateId$'
		        </isNotEqual>     
			</isNotNull>
			
			<isNotNull property="type">
		   	 	<isNotEqual property="type" compareValue="">
		          	 AND T.TYPE = '$type$'
		        </isNotEqual>     
			 </isNotNull>
			
			 <isNotNull property="channelId">
				 <isNotEqual property="channelId" compareValue="">
					  and T.chnl_id='$channelId$'
				 </isNotEqual>
			 </isNotNull>
			 <!-- 
		  AND T.CHNL_TYPE IN
	               (SELECT t.id CHANNEL_TYPE 
                                    
			                          FROM(SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL))t
			                         START WITH t.id = 'root'
			                        CONNECT BY PRIOR t.id = t.parent_id)
		   -->
		   <!-- 
			 <isNotNull property="monthId">
		   	 	<isNotEqual property="monthId" compareValue="">
				 and t.sale_month between
				       to_char(add_months(to_date('$monthId$', 'yyyyMM'), -11), 'yyyyMM') and
				       '$monthId$'
			  </isNotEqual>     
		    </isNotNull>  
		     -->     
		      <isNotNull property="dateId">
		   	 	<isNotEqual property="dateId" compareValue="">
				 and t.acct_date >
				 	   to_char(to_date('$dateId$','yyyymmdd')-30,'yyyymmdd')
			  </isNotEqual>
		    </isNotNull> 
			 group by t.acct_date
			 order by t.acct_date
	</select>
</sqlMap>
