<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="mobile.services">
	<!-- 获取集团用户编码序列-->
	<select id="getGroupUserSeq" resultClass="java.lang.String">
		select seq_groupuser.nextval from dual
	</select>
	<!-- 获取订单保存用户编码序列 --> 
	<select id="getOrderUserSeq" resultClass="java.lang.String">
		select seq_orderuser.nextval from dual
	</select>
	
	<insert id="insertGroupCustomer">
		insert into  jike.inf_grop_customer 
		values (
		       '$cardType$',
		       '$cust_photo_path$',
		       '$customerName$',
		       '$certificateNo$',
		       '$premises$',
		       '$organizationLegal$',
		       '$lpCertificateAddress$',
		       '$organizationAddress$',
		       '$operator_photo_path$',
		       '$name$',
		       '$tel$',
		       '$cardType_1$',
		       '$cardNo$',
		       '$address$',
		       '$card_photo_path$',
		       '$mailingAddress$',
		       '$postalCode$',
		       '$tel_1$'
		       
		)
		
	</insert>
	
	<!-- 获取已占用号码列表 -->
	<select id="getOccpiedNumberList" resultClass="java.lang.String">
		select SERIAL_NUMBER from jike.INF_OCCUPIED_NUMBER 
		where 1 = 1
		<isNotEmpty property = "staff_id" prepend="and"> 
		staff_id = '$staff_id$'
		</isNotEmpty>
	</select>
	
	<!-- 号码池 -->
	<select id="getNumberPool" resultClass="java.lang.String">
		select SERIAL_NUMBER from jike.INF_NUMBER_POOL 
		where 1  = 1
		<isNotEmpty property = "staff_id"> 
		and staff_id = '$staff_id$' 
		</isNotEmpty>
		<isNotEmpty property = "nbr_suffix">
			and substr(SERIAL_NUMBER, 8, 4) = '$nbr_suffix$'
		</isNotEmpty>
		<isNotEmpty property = "is_3g"> 
		and is_3g = '$is_3g$' 
		</isNotEmpty>
		and IS_OCCUPIED = '0'
	</select>
	
	<!-- 查询号码是否占用 -->
	<select id="isOccpeidNumber" resultClass="java.lang.String">
		select t.SERIAL_NUMBER from jike.INF_NUMBER_POOL t 
		where 1 = 1
		<isNotEmpty property = "staff_id"> 
		and t.STAFF_ID = '$staff_id$'
		</isNotEmpty>
		<isNotEmpty property = "nbr"> 
		and t.SERIAL_NUMBER = '$nbr$'
		</isNotEmpty>
		and t.IS_OCCUPIED = '1'
	</select>
	
	<!-- 占用号码 更新号码池-->
	<update id="updateNumberPool">
		update jike.INF_NUMBER_POOL  set IS_OCCUPIED = '1'
		where staff_id = '$staff_id$' and SERIAL_NUMBER = '$nbr$'
	</update>
	
	<!-- 添加占用号码 -->
	<insert id="addOccpiedNumber">
		insert into jike.INF_OCCUPIED_NUMBER values('$staff_id$', '$cust_id$', '$nbr$')
	</insert>
	
	<!-- 查询维系任务列表 -->
	<select id="getMainTaskList" resultClass="java.util.HashMap">
		select t.task_id          "task_id",
       t.task_type        "task_type",
       t.task_name        "task_name",
       t.task_create_time "task_create_time",
       t.task_end_time    "task_end_time"
  	   from jike.inf_maintain_task t
  	   where  1 = 1
  	   <isNotEmpty property = "staff_id"> 
  	   and t.staff_id = '$staff_id$'
  	   </isNotEmpty>
	</select>
	
	<!-- 查询维系任务详请 -->
	<select id="getMainTaskDetail" resultClass="java.util.HashMap">
		select t.task_id       "task_id",
       t.task_name     "task_name",
       t.task_end_time "task_end_time",
       t.task_detail   "task_detail",
       t.device_number "device_number"
  		from jike.inf_maintain_task t
  		where 1 = 1 
  		<isNotEmpty property = "task_id" prepend="and"> 
  		t.task_id = '$task_id$'
  		</isNotEmpty>
	</select>
	
	<!-- 插入维系反馈 -->
	<insert id="insertTaskFeedback">
		insert into jike.maintain_task_feekback
		values('$task_id$', '$staff_id$', '$task_done$', '$task_feedback$')
		
	</insert>
	
	<!-- 查询未完成任务-->
	<select id="getUnfinishedTask" resultClass="java.util.HashMap">
		select t.task_id from jike.inf_maintain_task t
		where t.STAFF_ID = '$staff_id$'
		MINUS
		select distinct t.task_id from  jike.inf_maintain_task t, jike.maintain_task_feekback t1
		where t.task_id = t1.task_id
		and t.STAFF_ID = '$staff_id$'
	</select>
	
	
	<!-- 获取品牌列表 -->
	<select id="getTerminalBrandList" resultClass="java.lang.String">
		select distinct t.brand from jike.INF_SELL_TERMINAL_DEVICE t
	</select>
	<!-- 获取型号列表 -->
	<select id="getTerminalModelList" resultClass="java.lang.String">
		select distinct t.TERMINAL_MODEL from jike.INF_SELL_TERMINAL_DEVICE t
		where t.brand = '$terminal$'
	</select>
	<!-- 获取内存列表 -->
	<select id="getTerminalMemoryList" resultClass="java.lang.String">
		select distinct t.TERMINAL_MEMORY from jike.INF_SELL_TERMINAL_DEVICE t
		where t.brand = '$terminal$'
	</select>
	<!-- 获取单/双卡类型列表 -->
	<select id="getTerminalCardList" resultClass="java.lang.String">
		select distinct t.CARDS from jike.INF_SELL_TERMINAL_DEVICE t
		where t.brand = '$terminal$'
	</select>
	<!-- 查询颜色-->
	<select id="getTerminalColorList" resultClass="java.lang.String">
		select distinct t.color from jike.INF_SELL_TERMINAL_DEVICE t
    		where t.brand = '$terminal$'
	</select>
	
	
	
	<!-- 产品查询标准产品 -->
	<select id="getStandardForProductQuery" resultClass="java.util.HashMap">
		select t.product_id "id",
	       t.product_name "name",
	       t.icon "icon",
	       t.picture "picture",
	       t.attachment "attachment",
	       decode(t.popular, 1, 'true', 0, 'false') "popular",
		   decode(t.favorate, 1, 'true', 0, 'false') "favorate"
	  from jike.INF_STANDARD_PRODUCT t
	  where 1 = 1 
		<isNotEmpty property="staff_id" prepend="and">
	  	t.STAFF_ID = '$staff_id$' 
		</isNotEmpty>
		<isNotEmpty property="net_type" prepend="and">
	  	t.net_type = '$net_type$' 
		</isNotEmpty>
		<isNotEmpty property="tele_type_lv1" prepend="and">
	  	t.DEVICE_TYPE = '$tele_type_lv1$' 
		</isNotEmpty>
		<isNotEmpty property="tele_type_lv2" prepend="and">
	  	t.DEVICE_VARIETIES = '$tele_type_lv2$' 
		</isNotEmpty>
		<isNotEmpty property="tele_type_lv3" prepend="and">
	  	t.BRAND = '$tele_type_lv3$' 
		</isNotEmpty>
	</select>
	
	<!-- 产品查询.终端设备 -->
	<select id="getTerminalForProductQuery" resultClass="java.util.HashMap">
		select t.product_id "id",
		       t.product_name "name",
		       t.icon "icon",
		       t.picture "picture",
		       t.attachment "attachment",
		       decode(t.popular, 1, 'true', 0, 'false') "popular",
		       decode(t.favorate, 1, 'true', 0, 'false') "favorate"
		  from jike.INF_STANDARD_PRODUCT t
		  where  1 = 1
			<isNotEmpty property="staff_id" prepend="and">
		  		t.STAFF_ID = '$staff_id$' 
			</isNotEmpty>
			<isNotEmpty property="brand" prepend="and">
			  	t.BRAND = '$brand$' 
			</isNotEmpty>
			<isNotEmpty property="model" prepend="and">
			  	t.PRODUCT_MODEL = '$model$' 
			</isNotEmpty>
			<isNotEmpty property="memory" prepend="and">
			  	t.PRODUCT_MEMORY = '$memory$' 
			</isNotEmpty>
			<isNotEmpty property="cards" prepend="and">
			  	t.CARDS = '$cards$' 
			</isNotEmpty>
	</select>
	
	<!-- 销售管理.产品筛选.标准产品.产品大类-->
	<select id="getProductClass" resultClass="java.lang.String">
		select distinct t.NET_TYPE_NAME from jike.INF_STD_PRODUCTS t
	</select>
	<!-- 销售管理.产品筛选.标准产品.设备大类-->
	<select id="getDeviceType" resultClass="java.lang.String">
		select distinct t.TELE_TYPE_NAME from jike.INF_STD_PRODUCTS t where t.NET_TYPE_NAME = '$net_type$' and t.TELE_TYPE_NAME is not null
	</select>
	<!-- 销售管理.产品筛选.标准产品.业务大类-->
	<select id="getTeleType" resultClass="java.lang.String">
		select distinct t.TELE_SUBTYPE_NAME
		  from jike.INF_STD_PRODUCTS t
			 where t.NET_TYPE_NAME = '$net_type$'
			   and TELE_TYPE_NAME = '$type1$'
			   and t.TELE_SUBTYPE_NAME is not null
	</select>
	<!-- 销售管理.产品筛选.标准产品.品牌大类-->
	<select id="getBrandType" resultClass="java.lang.String">
		select distinct t.BRAND_NAME
		  from jike.INF_STD_PRODUCTS t
			 where t.NET_TYPE_NAME = '$net_type$'
			   and t.TELE_TYPE_NAME = '$type1$'
         and t.TELE_SUBTYPE_NAME = '$typelv2$'
			   and t.BRAND_NAME is not null
	</select>
	
	
	<!-- 终端对比. 终端列表 -->
	<select id="contrastTerminalList" resultClass="java.util.HashMap">
		select t.product_id "id", t.product_name "name", t.icon "icon" from jike.INF_STANDARD_PRODUCT t 
		where 1 = 1
		<isNotEmpty property="brand" prepend="and">
		  	t.BRAND = '$brand$' 
		</isNotEmpty>
		<isNotEmpty property="model" prepend="and">
		  	t.PRODUCT_MODEL = '$model$' 
		</isNotEmpty>
		<isNotEmpty property="memory" prepend="and">
		  	t.PRODUCT_MEMORY = '$memory$' 
		</isNotEmpty>
		<isNotEmpty property="cards" prepend="and">
		  	t.CARDS = '$cards$' 
		</isNotEmpty>
	</select>
	
	<!-- 终端对比. 终端详情 -->
	<select id="contrastTerminalDetail" resultClass="java.util.HashMap">
		select t.product_id "id",
	       t.product_name "name",
	       t.brand "mobile_brand",
	       t.product_model "mobile_model",
	       t.cards "simple_double_cards",
	       t.cpu "cpu",
	       t.sim_type "sim_type",
	       t.resolution "resolution",
	       t.screen_size "screen_size",
	       t.camera "camera",
	       t.os "os",
	       t.rom "rom"
	  from jike.INF_STANDARD_PRODUCT t
	  where 1 = 1
	  <isNotEmpty property="id1">
	  		<isNotEmpty property="id2" prepend="and">
	  			t.product_id in ('$id1$', '$id2$')
	  		</isNotEmpty>
	  </isNotEmpty>
	</select>
	
	
	<insert id="insertAcceOrder">
		insert into  jike.inf_acce_order 
		values (
		   '$group_cert_type$',
		   '$group_cert_image_path$',
		   '$group_cust_name$'     ,
		   '$group_cert_id$'       ,
		   '$place_business$'     ,
		   '$legal_person$'        ,
		   '$legal_demicile$'      ,
		   '$org_addr$'            ,
		   '$oper_image_path$'   ,
		   '$oper_name$'           ,
		   '$oper_phone$'          ,
		   '$oper_cert_type$'      ,
		   '$oper_cert_id$'       ,
		   '$oper_cert_addr$'      ,
		   '$busi_card_image_path$' ,
		   '$post_addr$'          ,
		   '$zip_code$'           ,
		   '$group_contact_phone$'  ,
		   '$order_type$'        ,
		   '$cust_id$',
		   '$cust_name$'           ,
		   '$install_address$'    ,
		   '$contact$'             ,
		   '$contact_phone$'       ,
		   '$is_group_busi$'       ,
		   '$cert_id$'            ,
		   '$cert_image_path$'    ,
		   '$gsv_id$'             ,
		   '$busi_class$'         ,
		   '$busi_type$'          ,
		   '$bus_child_class$'    ,
		   '$busi_sub_class$'      ,
		   '$discnt_code$'         ,
		   '$serial_number$'       ,
		   '$terminal$'            ,
		   '$contract_fee$'       ,
		   '$oper_fee$'             ,
		   '$pay_acct$'            ,
		   '$fee_image_path$'     ,
		   '$sign_image_path$'     ,
		   '$rece_name$'            ,
		   '$rece_phoe$'           ,
		   '$rece_addr$'          ,
		   '$staff_id$',
		   '$remarks$',
		   '$product_type$',
		   '$product_id$',
		   '$order_id$'           
		)
		
		
	</insert>
	
	<!-- Socket 查询关系-->
	<select id="getRelationForSocket" resultClass="java.lang.String">
		SELECT t.relation_type_name FROM DIM.DIM_RELATION_TYPE_CODE t where t.relation_type_code = '$groupId$'
	</select>
	
	<!-- Socket 查询服务-->
	<select id="getServiceForSocket" resultClass="java.lang.String">
		SELECT t.service_name FROM dim.DIM_SERVICE T where t.service_id = '$serviceId$'
	</select>
	
	<!-- Socket 查询账户类型-->
	<select id="getPayModeForSocket" resultClass="java.lang.String">
		select t.pay_mode from dim.dim_pay_fee_mode_code t where t.pay_mode_code = '$payModeCode$' 
	</select>
	
	<!-- 查询客户经理信息-->
	<select id="getManagerInfo" resultClass="java.util.HashMap">
		select t.user_name, t.telephone from pure_user t where t.login_id = '$staff_id$'
	</select>
	
</sqlMap>