<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="mobile.security">

    <select id="login" resultClass="java.util.HashMap">
    	select a.user_id,a.login_id,a.user_name,a.password,a.mobile,a.position,a.mobile_state 
    	  from PURE.pure_user a
    	  WHERE a.login_id = '$username$' and a.state = 1 and rownum=1
    </select>
    
    <select id="roleModule" resultClass="java.util.HashMap">
    	select t.module_id "moduleId",t.module_name "moduleName" from PURE.sc_mobile_module t order by t.ord
    </select>
    
     <!-- 验证sessionkey -->
    <select id="checkSessionKey" resultClass="java.lang.String">
	     SELECT count(T.LOGIN_ID) count from PURE.SC_MOBILE_LOGIN_LOG T,
	     PURE.pure_user M 
	     WHERE T.LOGIN_ID = M.LOGIN_ID
	     AND M.LOGIN_ID = '$userName$'
	     AND T.SESSION_ID = '$sessionKey$'
     </select>
     
      <!-- 写入访问日志日志 -->
     <insert id="saveLog">
	     INSERT INTO PURE.SC_MOBILE_OPERATE_LOG
		  (LOGIN_ID, OPER_URL, OPER_TIME, MOUDEL_ID, OPER_PARAM)
		  (SELECT LOGIN_ID,'$url$',to_char(SYSDATE ,'yyyy-mm-dd hh24:mi:ss'),'','$params$' FROM PURE.pure_user WHERE login_ID = '$userName$'
		  )
     </insert>
     
    <delete id="deleteOldLogin">
		delete from PURE.SC_mobile_LOGIN_log where login_id = '$username$'
	</delete>
	<update id="loginout">
		update PURE.SC_mobile_login_log set is_active = '1',LOGOUT_TIME = to_char(SYSDATE,'yyyy-mm-dd hh24:mi:ss') where session_id = '$sessionKey$'
	</update>
	
     <insert id="saveLogin">
    	insert into PURE.SC_mobile_LOGIN_log (LOGIN_ID,SESSION_ID,LOGIN_TIME,DEVICE_ID,PHONE_NO,OPRATOR_TYPE,CITY_NO,DEPT,ACTIVE_TIME, is_active)
    	VALUES('$loginId$',
    	'$sessionKey$',
    	to_char(SYSDATE,'yyyy-mm-dd hh24:mi:ss'),
    	'$udid$',
    	'$phonenumber$',
    	'$opratorType$',
    	'$cityNo$',
    	'$dept$',
    	to_char(SYSDATE,'yyyy-mm-dd hh24:mi:ss'),
    	'0'
    	)
    </insert>
    <select id="getLoginBySessionKey" resultClass="java.util.HashMap">
    	select * from PURE.sc_mobile_login_log where session_id = '$value$'
    </select>
    
    <update id="activeLogin">
    	update PURE.sc_mobile_login_log set ACTIVE_TIME = to_char(SYSDATE,'yyyy-mm-dd hh24:mi:ss') where session_id = '$value$'
    </update>
    
    <select id="checkVersion"  resultClass="java.util.HashMap">
	    SELECT version_id,update_url,version_detail 
		  FROM PURE.SC_MOBILE_VERSION
		  WHERE OS_TYPE = '$ostype$'
		  AND IS_USED = '1' 
		  AND ROWNUM = 1
     </select>
     
     <select id="getVersionList"  resultClass="java.util.HashMap">
	    SELECT version_id mobile_version_no,nvl(version_detail,'暂无记录') mobile_version_details
     	  FROM PURE.SC_MOBILE_VERSION 
		  WHERE OS_TYPE = '$ostype$'
		  order by update_date
     </select>
     
     <select id="userList" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
		SELECT t.staff_id,
		T.LOGIN_NAME,
		T.GENDER,
		T.MOBILE_LOGIN,
	 	A.AREA_ID_DESC_PROV
    	FROM VASS.SC_LOGIN_USER T, DIM.DIM_AREA_NO A
		WHERE T.CITY_NO  = A.AREA_NO  (+)
		<isNotEmpty prepend="AND" property="AREA_NO">
			<isNotEqual property="AREA_NO" compareValue="018">
			T.CITY_NO = '$AREA_NO$'
			</isNotEqual>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="STAFF_ID">
			T.STAFF_ID LIKE '%$STAFF_ID$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="LOGIN_NAME">
			T.LOGIN_NAME LIKE '%$LOGIN_NAME$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="MOBILE_LOGIN">
			T.MOBILE_LOGIN=$MOBILE_LOGIN$
		</isNotEmpty>
	</select>
	<select id="userListNew" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
		SELECT t.LOGIN_ID "STAFF_ID",
		T.USER_NAME "LOGIN_NAME",
		T.SEX "GENDER",
		T.MOBILE_STATE "MOBILE_LOGIN",
	 	NVL(A.AREA_ID_DESC_PROV,'全省') "AREA_ID_DESC_PROV"
    	FROM PURE_USER T, dim.dim_area_no A
		WHERE T.AREA_NO  = A.AREA_NO  (+)
		<isNotEmpty prepend="AND" property="AREA_NO">
			<isNotEqual property="AREA_NO" compareValue="018">
			T.AREA_NO = '$AREA_NO$'
			</isNotEqual>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="STAFF_ID">
			T.LOGIN_ID LIKE '%$STAFF_ID$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="LOGIN_NAME">
			T.USER_NAME LIKE '%$LOGIN_NAME$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="MOBILE_LOGIN">
			T.MOBILE_STATE=$MOBILE_LOGIN$
		</isNotEmpty>
	</select>
	<select id="listLoginLog" resultClass="java.util.HashMap" >
		SELECT t.LOGIN_ID "STAFF_ID",
    T.USER_NAME "LOGIN_NAME",
    T.SEX "GENDER",
    T.MOBILE_STATE "MOBILE_LOGIN",
     NVL(A.AREA_ID_DESC_PROV,'全省') "AREA_ID_DESC_PROV",
     ll.login_time,ll.active_time,ll.logout_time
      FROM SC_mobile_LOGIN_log ll,PURE_USER T, dim.dim_area_no A
		WHERE T.AREA_NO  = A.AREA_NO  (+)
         and ll.login_id = t.login_id
    <isNotEmpty prepend="AND" property="AREA_NO">
			<isNotEqual property="AREA_NO" compareValue="018">
			T.AREA_NO = '$AREA_NO$'
			</isNotEqual>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="STAFF_ID">
			T.LOGIN_ID LIKE '%$STAFF_ID$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="LOGIN_NAME">
			T.USER_NAME LIKE '%$LOGIN_NAME$%'
		</isNotEmpty>
		order by ll.active_time desc,ll.login_time desc
	</select>
	
	<update id="changeMobileState1">
		UPDATE VASS.SC_LOGIN_USER SET MOBILE_LOGIN =1 WHERE STAFF_ID IN ($STAFF_ID$)
	</update>
	<update id="changeMobileState0">
		UPDATE VASS.SC_LOGIN_USER SET MOBILE_LOGIN =0 WHERE STAFF_ID IN ($STAFF_ID$)
	</update>
	<update id="changeMobileState1New">
		UPDATE PURE_USER SET MOBILE_STATE =1 WHERE LOGIN_ID IN ($STAFF_ID$)
	</update>
	<update id="changeMobileState0New">
		UPDATE PURE_USER SET MOBILE_STATE =0 WHERE LOGIN_ID IN ($STAFF_ID$)
	</update>
	<select id="selectArea" resultClass="java.util.HashMap">
		SELECT T.AREA_NO as "vkey", T.AREA_ID_DESC_PROV as "vdesc"
		  FROM (SELECT AREA_ID,
		               AREA_ID_PROV,
		               AREA_ID_DESC_PROV,
		               AREA_NO,
		               LAN_ID,
		               REGION_ID,
		               AREA_OLD,
		               MNEMONIC,
		               TO_NUMBER(DECODE(ORD, '19', '0', ORD)) ORD
		          FROM DIM.DIM_AREA_NO) T
		 WHERE 1 = 1
		<isNotEmpty prepend="and" property="areaNo">
			<isNotEqual property="areaNo" compareValue="-1">
			<isNotEqual property="areaNo" compareValue="018">
		   T.AREA_NO = '$areaNo$'
		   	</isNotEqual>
		   	</isNotEqual>
		</isNotEmpty>
		 ORDER BY T.ORD ASC
	</select>
</sqlMap>
