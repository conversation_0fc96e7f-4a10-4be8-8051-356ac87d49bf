<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="product">
<!-- 产品类型查询 -->
<select id="getProType"  resultClass="java.util.HashMap"> 	   
      select distinct  product_type  "prod_type"   from  jike.inf_industry_application  
</select>
<!-- 行业类型查询 -->
<select id="getInsType"  resultClass="java.lang.String"> 	   
      SELECT  distinct industry_type  "industry_type"  from  jike.inf_industry_application 
      where  product_type = '$prod_type$'
      and  industry_type  is not null
</select>
<select id="getInsType1"  resultClass="java.lang.String"> 	   
      SELECT  distinct industry_type  "industry_type"  from  jike.inf_industry_application 
      where  product_type = '$prod_type$'
      and  industry_type  is not null
      and  cust_visible = '0'
</select>
<!-- 行业查询（解决方案） -->
<select id="querySolutionforIns"  resultClass="java.lang.String"> 	   
      select  distinct industry  "vertical"
      FROM jike.inf_solution
</select>
<select id="querySolutionforIns1"  resultClass="java.lang.String"> 	   
      select  distinct industry  "vertical"
      FROM jike.inf_solution
       where IS_SHOW='0'
</select>
<!-- 行业查询（案例库） -->
<select id="queryCaselibforIns"  resultClass="java.lang.String"> 	   
      select  distinct industry  "vertical"
      FROM jike.inf_market_case_library
</select>
<select id="queryCaselibforIns1"  resultClass="java.lang.String"> 	   
      select  distinct industry  "vertical"
      FROM jike.inf_market_case_library
      where IS_SHOW='0'
</select>
<!-- 后台标准产品查询 -->
<select id="queryStandardProductList"  resultClass="java.util.HashMap"> 	   
      select * from jike.inf_std_products t where 1=1
      and t.STANDARD_TYPE = '$productType$'
      <isNotNull property="TELE_TYPE_NAME">
		   <isNotEqual property="TELE_TYPE_NAME" prepend="and"  compareValue="">
	   	   		trim(t.TELE_TYPE_NAME) = '$TELE_TYPE_NAME$'
	   	   </isNotEqual>
  	   </isNotNull> 
      <isNotNull property="TELE_SUBTYPE_NAME">
		   <isNotEqual property="TELE_SUBTYPE_NAME" prepend="and"  compareValue="">
	   	   		trim(t.TELE_SUBTYPE_NAME) = '$TELE_SUBTYPE_NAME$'
	   	   </isNotEqual>
  	   </isNotNull> 
      <isNotNull property="BRAND_NAME">
		   <isNotEqual property="BRAND_NAME" prepend="and"  compareValue="">
	   	   		trim(t.BRAND_NAME) = '$BRAND_NAME$'
	   	   </isNotEqual>
  	   </isNotNull> 
      <isNotNull property="PRODUCT_NAME">
		   <isNotEqual property="PRODUCT_NAME" prepend="and"  compareValue="">
	   	   		trim(t.PRODUCT_NAME) like '%$PRODUCT_NAME$%'
	   	   </isNotEqual>
  	   </isNotNull> 
      <isNotNull property="PLAN_NAME">
		   <isNotEqual property="PLAN_NAME" prepend="and"  compareValue="">
	   	   		trim(t.PLAN_NAME) = '$PLAN_NAME$'
	   	   </isNotEqual>
  	   </isNotNull> 
      <isNotNull property="PLAN_TYPE">
		   <isNotEqual property="PLAN_TYPE" prepend="and"  compareValue="">
	   	   		trim(t.PLAN_TYPE) = '$PLAN_TYPE$'
	   	   </isNotEqual>
  	   </isNotNull> 
  	   
  	   order by TO_NUMBER(t.PID) desc
</select>

<select id="getStandardProductById" resultClass="java.util.HashMap">
	select * from jike.inf_std_products where pid = '$value$'
</select>
<select id="getResourceByLinkId" resultClass="java.util.HashMap">
	select * from jike.inf_resource_library where LINK_ID = '$value$' order by IS_IMAGE
</select>
<select id="getResourceByRId" resultClass="java.util.HashMap">
	select * from jike.inf_resource_library where RID = '$value$'
</select>

<select id="getNextProductId" resultClass="java.lang.Object">
	SELECT jike.SEQ_PRODUCT_ID.NEXTVAL FROM DUAL
</select>

<insert id="insertProduct-2g">
insert into jike.inf_std_products (
	PID,CUST_VISABLE,TELE_TYPE_NAME,
	BRAND_NAME,BRAND_CODE,PRODUCT_NAME,PRODUCT_CODE,
	PLAN_NAME,PLAN_ID,PLAN_PRICE,PLAN_NOTES,
	CONTAIN_CALLS,CONTAIN_CALLS_CITY,
	CONTAIN_CALLS_LONG,CONTAIN_FLOWS_PROV,
	CONTAIN_FLOWS,CONTAIN_ANSWER,
	CONTAIN_SMS,CONTAIN_MMS,OUTSTRIP_CALLS,
	OUTSTRIP_FLOWS,OUTSTRIP_CALLS_CITY,OUTSTRIP_CALLS_LONG,
	OUTSTRIP_SMS,OUTSTRIP_MMS,OUTSTRIP_VIDEO,ICON,ICON_THUMB,
	STANDARD_TYPE,NET_TYPE_ID,NET_TYPE_NAME
)
values (
	'$PID$','$CUST_VISABLE$','$TELE_TYPE_NAME$',
	'$BRAND_NAME$','$BRAND_CODE$','$PRODUCT_NAME$','$PRODUCT_CODE$',
	'$PLAN_NAME$','$PLAN_ID$','$PLAN_PRICE$','$PLAN_NOTES$',
	'$CONTAIN_CALLS$','$CONTAIN_CALLS_CITY$',
	'$CONTAIN_CALLS_LONG$','$CONTAIN_FLOWS_PROV$',
	'$CONTAIN_FLOWS$','$CONTAIN_ANSWER$',
	'$CONTAIN_SMS$','$CONTAIN_MMS$','$OUTSTRIP_CALLS$',
	'$OUTSTRIP_FLOWS$','$OUTSTRIP_CALLS_CITY$','$OUTSTRIP_CALLS_LONG$',
	'$OUTSTRIP_SMS$','$OUTSTRIP_MMS$','$OUTSTRIP_VIDEO$','$ICON$','$ICON_THUMB$',
	'2g','0','移网'
)
</insert>
<update id="updateProduct-2g">
update jike.inf_std_products SET 
	CUST_VISABLE = '$CUST_VISABLE$',
	TELE_TYPE_NAME = '$TELE_TYPE_NAME$',
	BRAND_NAME = '$BRAND_NAME$',BRAND_CODE = '$BRAND_CODE$',
	PRODUCT_NAME = '$PRODUCT_NAME$',PRODUCT_CODE = '$PRODUCT_CODE$',
	PLAN_NAME = '$PLAN_NAME$',PLAN_ID = '$PLAN_ID$',PLAN_PRICE = '$PLAN_PRICE$',
	PLAN_NOTES = '$PLAN_NOTES$',
	CONTAIN_CALLS = '$CONTAIN_CALLS$',CONTAIN_CALLS_CITY = '$CONTAIN_CALLS_CITY$',
	CONTAIN_CALLS_LONG = '$CONTAIN_CALLS_LONG$',CONTAIN_FLOWS_PROV = '$CONTAIN_FLOWS_PROV$',
	CONTAIN_FLOWS = '$CONTAIN_FLOWS$',CONTAIN_ANSWER = '$CONTAIN_ANSWER$',
	CONTAIN_SMS = '$CONTAIN_SMS$',CONTAIN_MMS = '$CONTAIN_MMS$',OUTSTRIP_CALLS = '$OUTSTRIP_CALLS$',
	OUTSTRIP_FLOWS = '$OUTSTRIP_FLOWS$',OUTSTRIP_CALLS_CITY = '$OUTSTRIP_CALLS_CITY$',
	OUTSTRIP_CALLS_LONG = '$OUTSTRIP_CALLS_LONG$',OUTSTRIP_SMS = '$OUTSTRIP_SMS$',
	OUTSTRIP_MMS = '$OUTSTRIP_MMS$',OUTSTRIP_VIDEO = '$OUTSTRIP_VIDEO$'
	<isNotNull property="ICON">
		   <isNotEqual property="ICON" compareValue="">
	   	   		,ICON = '$ICON$'
	   	   </isNotEqual>
  	   </isNotNull> 
  	   
  	   <isNotNull property="ICON_THUMB">
		   <isNotEqual property="ICON_THUMB" compareValue="">
	   	   		,ICON_THUMB = '$ICON_THUMB$'
	   	   </isNotEqual>
  	   </isNotNull> 
	WHERE PID = '$PID$'
</update>

<insert id="insertProduct-3g4g">
insert into jike.inf_std_products (
	PID,CUST_VISABLE,TELE_TYPE_NAME,TELE_SUBTYPE_NAME,
	BRAND_NAME,BRAND_CODE,PRODUCT_NAME,PRODUCT_CODE,
	PLAN_NAME,PLAN_ID,PLAN_PRICE,PLAN_TYPE,PLAN_NOTES,
	CONTAIN_CALLS,CONTAIN_FLOWS,
	CONTAIN_SMS,CONTAIN_MMS,CONTAIN_ANSWER,
	OUTSTRIP_CALLS,OUTSTRIP_FLOWS,
	OUTSTRIP_SMS,OUTSTRIP_MMS,
	OUTSTRIP_VIDEO,GIVE_M_NUMBER,GIVE_T_NUMBER,GIVE_VIDEO,GIVE_SP,
	ICON,ICON_THUMB,
	STANDARD_TYPE,NET_TYPE_ID,NET_TYPE_NAME
)
values (
	'$PID$','$CUST_VISABLE$','$TELE_TYPE_NAME$','$TELE_SUBTYPE_NAME$',
	'$BRAND_NAME$','$BRAND_CODE$','$PRODUCT_NAME$','$PRODUCT_CODE$',
	'$PLAN_NAME$','$PLAN_ID$','$PLAN_PRICE$','$PLAN_TYPE$','$PLAN_NOTES$',
	'$CONTAIN_CALLS$','$CONTAIN_FLOWS$',
	'$CONTAIN_SMS$','$CONTAIN_MMS$','$CONTAIN_ANSWER$',
	'$OUTSTRIP_CALLS$','$OUTSTRIP_FLOWS$',
	'$OUTSTRIP_SMS$','$OUTSTRIP_MMS$',
	'$OUTSTRIP_VIDEO$','$GIVE_M_NUMBER$','$GIVE_T_NUMBER$','$GIVE_VIDEO$','$GIVE_SP$',
	'$ICON$','$ICON_THUMB$',
	'3g4g','0','移网'
)
</insert>
<update id="updateProduct-3g4g">
update jike.inf_std_products SET 
	CUST_VISABLE = '$CUST_VISABLE$',
	TELE_TYPE_NAME = '$TELE_TYPE_NAME$',
	TELE_SUBTYPE_NAME = '$TELE_SUBTYPE_NAME$',
	BRAND_NAME = '$BRAND_NAME$',BRAND_CODE = '$BRAND_CODE$',
	PRODUCT_NAME = '$PRODUCT_NAME$',PRODUCT_CODE = '$PRODUCT_CODE$',
	PLAN_NAME = '$PLAN_NAME$',PLAN_ID = '$PLAN_ID$',PLAN_PRICE = '$PLAN_PRICE$',
	PLAN_NOTES = '$PLAN_NOTES$',PLAN_TYPE = '$PLAN_TYPE$',
	CONTAIN_CALLS = '$CONTAIN_CALLS$',CONTAIN_FLOWS = '$CONTAIN_FLOWS$',
	CONTAIN_SMS = '$CONTAIN_SMS$',CONTAIN_MMS = '$CONTAIN_MMS$',
	CONTAIN_ANSWER = '$CONTAIN_ANSWER$',OUTSTRIP_CALLS = '$OUTSTRIP_CALLS$',
	OUTSTRIP_FLOWS = '$OUTSTRIP_FLOWS$',OUTSTRIP_SMS = '$OUTSTRIP_SMS$',OUTSTRIP_MMS = '$OUTSTRIP_MMS$',
	OUTSTRIP_VIDEO = '$OUTSTRIP_VIDEO$',
	GIVE_M_NUMBER = '$GIVE_M_NUMBER$',GIVE_T_NUMBER = '$GIVE_T_NUMBER$',GIVE_VIDEO = '$GIVE_VIDEO$',GIVE_SP = '$GIVE_SP$'
	<isNotNull property="ICON">
		   <isNotEqual property="ICON" compareValue="">
	   	   		,ICON = '$ICON$'
	   	   </isNotEqual>
  	   </isNotNull> 
  	   
  	   <isNotNull property="ICON_THUMB">
		   <isNotEqual property="ICON_THUMB" compareValue="">
	   	   		,ICON_THUMB = '$ICON_THUMB$'
	   	   </isNotEqual>
  	   </isNotNull> 
	WHERE PID = '$PID$'
</update>
<insert id="insertProduct-rh">
insert into jike.inf_std_products (
	PID,CUST_VISABLE,TELE_TYPE_NAME,
	PRODUCT_NAME,PRODUCT_CODE,
	PLAN_NAME,PLAN_ID,PLAN_PRICE,PLAN_TYPE,PLAN_NOTES,
	AVAIL_COMM_FEE_NO3G,AVAIL_COMM_FEE_3G,
	MAX_TERM_2G,MIN_TERM_3G,
	CONTAIN_CALLS,
	OUTSTRIP_CALLS,OUTSTRIP_CALLS_CITY,
	OUTSTRIP_CALLS_LONG,
	ICON,ICON_THUMB,
	STANDARD_TYPE,NET_TYPE_ID,NET_TYPE_NAME
)
values (
	'$PID$','$CUST_VISABLE$','$TELE_TYPE_NAME$',
	'$PRODUCT_NAME$','$PRODUCT_CODE$',
	'$PLAN_NAME$','$PLAN_ID$','$PLAN_PRICE$','$PLAN_TYPE$','$PLAN_NOTES$',
	'$AVAIL_COMM_FEE_NO3G$','$AVAIL_COMM_FEE_3G$',
	'$MAX_TERM_2G$','$MIN_TERM_3G$',
	'$CONTAIN_CALLS$',
	'$OUTSTRIP_CALLS$','$OUTSTRIP_CALLS_CITY$',
	'$OUTSTRIP_CALLS_LONG$',
	'$ICON$','$ICON_THUMB$',
	'rh','2','融合'
)
</insert>
<update id="updateProduct-rh">
update jike.inf_std_products SET 
	CUST_VISABLE = '$CUST_VISABLE$',
	TELE_TYPE_NAME = '$TELE_TYPE_NAME$',
	PRODUCT_NAME = '$PRODUCT_NAME$',PRODUCT_CODE = '$PRODUCT_CODE$',
	PLAN_NAME = '$PLAN_NAME$',PLAN_ID = '$PLAN_ID$',PLAN_PRICE = '$PLAN_PRICE$',
	PLAN_TYPE = '$PLAN_TYPE$',PLAN_NOTES = '$PLAN_NOTES$',
	AVAIL_COMM_FEE_NO3G = '$AVAIL_COMM_FEE_NO3G$',MIN_TERM_3G = '$MIN_TERM_3G$',
	CONTAIN_CALLS = '$CONTAIN_CALLS$',CONTAIN_FLOWS = '$CONTAIN_FLOWS$',
	CONTAIN_SMS = '$CONTAIN_SMS$',OUTSTRIP_CALLS = '$OUTSTRIP_CALLS$',
	OUTSTRIP_CALLS_CITY = '$OUTSTRIP_CALLS_CITY$',OUTSTRIP_CALLS_LONG = '$OUTSTRIP_CALLS_LONG$'
	<isNotNull property="ICON">
		   <isNotEqual property="ICON" compareValue="">
	   	   		,ICON = '$ICON$'
	   	   </isNotEqual>
  	   </isNotNull> 
  	   
  	   <isNotNull property="ICON_THUMB">
		   <isNotEqual property="ICON_THUMB" compareValue="">
	   	   		,ICON_THUMB = '$ICON_THUMB$'
	   	   </isNotEqual>
  	   </isNotNull> 
	WHERE PID = '$PID$'
</update>
<insert id="insertProduct-cnc">
insert into jike.inf_std_products (
	PID,CUST_VISABLE,TELE_TYPE_NAME,
	PRODUCT_NAME,PRODUCT_CODE,
	PLAN_NAME,PLAN_ID,PLAN_PRICE,PLAN_NOTES,
	ICON,ICON_THUMB,
	STANDARD_TYPE,NET_TYPE_ID,NET_TYPE_NAME
)
values (
	'$PID$','$CUST_VISABLE$','$TELE_TYPE_NAME$',
	'$PRODUCT_NAME$','$PRODUCT_CODE$',
	'$PLAN_NAME$','$PLAN_ID$','$PLAN_PRICE$','$PLAN_NOTES$',
	'$ICON$','$ICON_THUMB$',
	'cnc','1','固网'
)
</insert>
<update id="updateProduct-cnc">
update jike.inf_std_products SET 
	CUST_VISABLE = '$CUST_VISABLE$',
	TELE_TYPE_NAME = '$TELE_TYPE_NAME$',
	PRODUCT_NAME = '$PRODUCT_NAME$',PRODUCT_CODE = '$PRODUCT_CODE$',
	PLAN_NAME = '$PLAN_NAME$',PLAN_ID = '$PLAN_ID$',PLAN_PRICE = '$PLAN_PRICE$',
	PLAN_TYPE = '$PLAN_TYPE$',PLAN_NOTES = '$PLAN_NOTES$',
	AVAIL_COMM_FEE_NO3G = '$AVAIL_COMM_FEE_NO3G$',MIN_TERM_3G = '$MIN_TERM_3G$',
	CONTAIN_CALLS = '$CONTAIN_CALLS$',CONTAIN_FLOWS = '$CONTAIN_FLOWS$',
	CONTAIN_SMS = '$CONTAIN_SMS$',OUTSTRIP_CALLS = '$OUTSTRIP_CALLS$',
	OUTSTRIP_CALLS_CITY = '$OUTSTRIP_CALLS_CITY$',OUTSTRIP_CALLS_LONG = '$OUTSTRIP_CALLS_LONG$'
	<isNotNull property="ICON">
		   <isNotEqual property="ICON" compareValue="">
	   	   		,ICON = '$ICON$'
	   	   </isNotEqual>
  	   </isNotNull> 
  	   
  	   <isNotNull property="ICON_THUMB">
		   <isNotEqual property="ICON_THUMB" compareValue="">
	   	   		,ICON_THUMB = '$ICON_THUMB$'
	   	   </isNotEqual>
  	   </isNotNull> 
	WHERE PID = '$PID$'
</update>
<delete id="deleteProduct">
	delete jike.inf_std_products where pid = '$value$'
</delete>

<delete id="deleteResourceByLinkId">
	delete jike.inf_resource_library where LINK_ID = '$value$'
</delete>
<delete id="deleteResourceByRId">
	delete jike.inf_resource_library where RID = '$value$'
</delete>
<update id="updateResource">
	update jike.inf_resource_library SET VIEW_FLAG = '$VIEW_FLAG$' where RID = '$RID$'
</update>

<insert id="insertResource">
insert into jike.inf_resource_library (
RID,URL_PATH, REAL_PATH, IMAGE_THUMB_PATH, 
IMAGE_THUMB_REAL_PATH, VIEW_FLAG, LINK_ID,
 CREATE_DATE, IS_IMAGE,ORIGINAL_FILENAME)
values ( jike.SEQ_RESOURCE_ID.NEXTVAL,'$URL_PATH$', '$REAL_PATH$', '$IMAGE_THUMB_PATH$',
 '$IMAGE_THUMB_REAL_PATH$', '$VIEW_FLAG$', '$LINK_ID$', sysdate, '$IS_IMAGE$','$ORIGINAL_FILENAME$')
</insert>


<!-- 收藏产品id查询（行业应用） -->
<select id="getProId"  resultClass="java.lang.String"> 	   
      select  id  "id"  from  jike.inf_product_collection  where STAFF_ID='$staff_id$' and  show_type='2'
</select>
<!-- 收藏产品id查询（解决方案-->
<select id="getProId1"  resultClass="java.lang.String"> 	   
      select  id  "id"  from  jike.inf_product_collection  where STAFF_ID='$staff_id$' and  show_type='3'
</select>
<!-- 收藏产品id查询（案例库） -->
<select id="getProId2"  resultClass="java.lang.String"> 	   
      select  id  "id"  from  jike.inf_product_collection  where STAFF_ID='$staff_id$' and  show_type='4'
</select>
<!-- 收藏产品id查询（标准产品） -->
<select id="getProId3"  resultClass="java.lang.String"> 	   
      select  id  "id"  from  jike.inf_product_collection  where STAFF_ID='$staff_id$' and  show_type='1'
</select>
<!-- 受欢迎产品id查询（行业应用/解决方案/案例库） -->
<select id="getPopularId"  resultClass="java.lang.String"> 	   
      select  id  "id"  from  jike.inf_products_popular  where show_type='2'
</select>
<!-- 产品id查询（行业应用） -->
<select id="getProInfo"  resultClass="java.util.HashMap"> 	   
      select  t.id   "id",
              t.PRODUCT_NAME  "name",
              t.ICON  "icon",
              t2.URL_PATH   "attachment" ,
              t1.URL_PATH   "picture"
      from  jike.inf_industry_application t, (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2
      where 1=1
       and t.id= t1.link_id(+)
       and t.id = t2.link_id(+)
      <isNotEmpty  property="vertical">
      and INDUSTRY_TYPE = '$vertical$'
      </isNotEmpty>
        <isNotEmpty  property="prod_type">
      and PRODUCT_TYPE = '$prod_type$'
      </isNotEmpty>
</select> 
<select id="getProInfo1"  resultClass="java.util.HashMap"> 	   
      select  t.id   "id",
              t.PRODUCT_NAME  "name",
              t.ICON  "icon",
              t2.URL_PATH   "attachment" ,
              t1.URL_PATH   "picture"
      from  jike.inf_industry_application t, (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2
      where 1=1
       and t.id= t1.link_id(+)
       and t.id = t2.link_id(+)
       and t.cust_visible = '0'
      <isNotEmpty  property="vertical">
      and INDUSTRY_TYPE = '$vertical$'
      </isNotEmpty>
        <isNotEmpty  property="prod_type">
      and PRODUCT_TYPE = '$prod_type$'
      </isNotEmpty>
</select> 
<!-- 产品id查询（解决方案） -->
<select id="getSolutionInf"  resultClass="java.util.HashMap"> 	   
      select  t.ID   "id",
              t.CASE_NAME   "name",
              t.ICON   "icon",
              t2.URL_PATH   "attachment" ,
              t1.URL_PATH   "picture"
      from  jike.inf_solution t,  (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2
      where 1=1
      and t.id= t1.link_id(+)
      and t.id=t2.link_id(+)
      <isNotEmpty  property="vertical">
      and t.INDUSTRY = '$vertical$'
      </isNotEmpty>
</select>
<select id="getSolutionInf1"  resultClass="java.util.HashMap"> 	   
      select  t.ID   "id",
              t.CASE_NAME   "name",
              t.ICON   "icon",
              t2.URL_PATH   "attachment" ,
              t1.URL_PATH   "picture"
      from  jike.inf_solution t,  (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2
      where 1=1
      and t.id= t1.link_id(+)
      and t.id=t2.link_id(+)
      and t.is_show = '0'
      <isNotEmpty  property="vertical">
      and t.INDUSTRY = '$vertical$'
      </isNotEmpty>
</select>
<!-- 产品id查询（案例库） -->
<select id="getShowCaseInfo"  resultClass="java.util.HashMap"> 	   
      select  t.ID  "id",
              t.CASE_NAME  "name",
              t.ICON  "icon",
              t2.URL_PATH   "attachment" ,
              t1.URL_PATH   "picture"
      from  jike.inf_market_case_library  t, (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2
      where 1=1
      and t.id= t1.link_id(+)
      and t.id=t2.link_id(+)
      <isNotEmpty  property="vertical">
      and INDUSTRY = '$vertical$'
      </isNotEmpty>
</select>
<select id="getShowCaseInfo1"  resultClass="java.util.HashMap"> 	   
      select  t.ID  "id",
              t.CASE_NAME  "name",
              t.ICON  "icon",
              t2.URL_PATH   "attachment" ,
              t1.URL_PATH   "picture"
      from  jike.inf_market_case_library  t, (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2
      where 1=1
      and t.id= t1.link_id(+)
      and t.id=t2.link_id(+)
      and t.is_show = '0'
      <isNotEmpty  property="vertical">
      and INDUSTRY = '$vertical$'
      </isNotEmpty>
</select>
<!-- 插入收藏产品信息 -->
<insert  id="insertProductId">
insert  into jike.inf_product_collection(   staff_id, 
											show_type,
											col_date, 
											id
									)values(
									        '$staff_id$',
									        '$show_type$',
									         sysdate,
									         '$id$'
									       <!-- <isNotEmpty  property="staff_id">
											'$staff_id$',
											</isNotEmpty>
											<isEmpty  property="staff_id">
											 '',
											</isEmpty>
											 <isNotEmpty  property="show_type">
											'$show_type$',
											</isNotEmpty>
											<isEmpty  property="show_type">
											 '',
											</isEmpty>
											sysdate,
											 <isNotEmpty  property="id">
											'$id$'
											</isNotEmpty>
											<isEmpty  property="id">
											  ''
											</isEmpty>--> 
                                            )
</insert>
<!-- 删除收藏产品 -->
<delete  id="deleteCollectionProduct">
delete from  jike.inf_product_collection 
where 1=1
<isNotEmpty property="staff_id">
   and staff_id = '$staff_id$'
</isNotEmpty>
<isNotEmpty property="show_type">
    and  show_type = '$show_type$'
</isNotEmpty> 
<isNotEmpty property="id">
  and  id='$id$'
</isNotEmpty>
</delete>
<!-- show_type查询 -->
<select id="queryShowType"  resultClass="java.lang.String">
	select distinct show_type  "show_type"  from jike.inf_product_collection   where staff_id = '$staff_id$'
</select>
<!-- 产品id查询（输入staff_id,show_type）-->
<select id="getSHCId"  resultClass="java.lang.String">
	select id  "id"  from jike.inf_product_collection   where staff_id = '$staff_id$'  and show_type='$show_type$'
</select>
<!-- 产品id查询（输入staff_id,show_type）-->
<select id="getHYId"  resultClass="java.lang.String">
	select id  "id"  from jike.inf_products_popular   where show_type='$show_type$'
</select>
<!-- 标准产品查询-->
<select id="getStandProdInfo"  resultClass="java.util.HashMap"> 	   
      select distinct t.PID  "id",
              t.PRODUCT_NAME  "name",
              t.ICON  "icon",
              t2.URL_PATH   "attachment" ,
              t1.URL_PATH   "picture"
      from  jike.inf_std_products  t,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2,jike.inf_product_collection t3
      where 1=1
      and t.pid= t1.link_id(+)
      and t.pid = t2.link_id(+)
      and t.pid = t3.id 
      and t3.staff_id = '$staff_id$'  
      and t3.show_type='$show_type$'
</select>
<!-- 行业应用产品查询-->
<select id="getInsAppsInfo"  resultClass="java.util.HashMap"> 	   
      select distinct  t.ID  "id",
              t.PRODUCT_NAME  "name",
              t.ICON  "icon",
              t2.URL_PATH   "attachment" ,
              t1.url_path   "picture"
      from  jike.inf_industry_application  t, (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2,jike.inf_product_collection t3
      where 1=1
      and t.id= t1.link_id(+)
      and t.id=t2.link_id(+)
      and t.id = t3.id 
       and t3.staff_id = '$staff_id$'  
      and t3.show_type='$show_type$'
</select>
<!-- 解决方案产品查询-->
<select id="getSolCaseInfo"  resultClass="java.util.HashMap"> 	   
      select distinct  t.ID  "id",
              t.CASE_NAME  "name",
              t.ICON  "icon",
              t2.URL_PATH   "attachment" ,
              t1.url_PATH   "picture"
      from  jike.inf_solution  t, (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2,jike.inf_product_collection t3
      where 1=1
      and t.id= t1.link_id(+)
      and t.id=t2.link_id(+)
      and t.id = t3.id 
      and t3.staff_id = '$staff_id$'  
      and t3.show_type='$show_type$'
</select>
<!-- 案例库查询-->
<select id="getCasLibInfo"  resultClass="java.util.HashMap"> 	   
      select distinct  t.ID  "id",
              t.CASE_NAME  "name",
              t.ICON  "icon",
              t2.URL_PATH   "attachment" ,
              t1.url_PATH   "picture"
      from  jike.inf_market_case_library  t, (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2,jike.inf_product_collection t3
      where 1=1
      and t.id= t1.link_id(+)
      and t.id=t2.link_id(+)
      and t.id = t3.id 
       and t3.staff_id = '$staff_id$'  
      and t3.show_type='$show_type$'
</select>
<!-- 终端设备查询-->
<select id="getDeviceInfo"  resultClass="java.util.HashMap"> 	   
      select distinct  t.ID  "id",
              t.MOBILE_BRAND  "name",
              t.ICON  "icon",
              t2.URL_PATH   "attachment" ,
              t1.url_PATH   "picture"
      from  jike.inf_terminal_device  t, (select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2,jike.inf_product_collection t3
      where  t.id= t1.link_id(+)
      and t.id=t2.link_id(+)
       and t.id = t3.id 
        and t3.staff_id = '$staff_id$'  
      and t3.show_type='$show_type$'
</select>
<!-- 行业类型查询 -->
<select id="getHYTpye"  resultClass="java.util.HashMap"> 	   
      select  distinct PRODUCT_TYPE  "name"
      from  jike.inf_industry_application 
</select>
<!-- 产品类型查询 -->
<select id="getCPType"  resultClass="java.lang.String"> 
 select  distinct   INDUSTRY_TYPE
      from  jike.inf_industry_application 
      where  PRODUCT_TYPE = '$industry_type$'
</select>
<select id="getSCList"  resultClass="java.util.HashMap">
select 1
  from jike.inf_product_collection
  where staff_id='$staff_id$' and id='$id$' and show_type = '$show_type$'
</select>
</sqlMap>
