<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="mobile.sale">
	
    <!-- 销售统计主页-->
	<select id="listOne" resultClass="java.util.HashMap" remapResults="true">
		 with  kpi_table    As  (select /*'4' p_id,
							       '9' id,
							       '提成佣金' v0,*/
							           sum(t.cd_dev_cnt_2g) v11,
							       sum(t.cm_dev_cnt_2g) v12,
							       ROUND(DECODE(sum(t.lm_dev_cnt_2g),
							                    0,
							                    0,
							                    (sum(t.cm_dev_cnt_2g) - sum(t.lm_dev_cnt_2g)) /
							                    sum(t.lm_dev_cnt_2g)),
							             4) v13,
							             sum(t.cd_active_cnt_2g) v21,
							       sum(t.cm_active_cnt_2g) v22,
							       ROUND(DECODE(sum(t.lm_active_cnt_2g),
							                    0,
							                    0,
							                    (sum(t.cm_active_cnt_2g) - sum(t.lm_active_cnt_2g)) /
							                    sum(t.lm_active_cnt_2g)),
							             4) v23,
							               sum(t.cd_dev_cnt_3g) v31,
							       sum(t.cm_dev_cnt_3g) v32,
							       ROUND(DECODE(sum(t.lm_dev_cnt_3g),
							                    0,
							                    0,
							                    (sum(t.cm_dev_cnt_3g) - sum(t.lm_dev_cnt_3g)) /
							                    sum(t.lm_dev_cnt_3g)),
							             4) v33,
							               sum(t.cd_dev_cnt_3ghy) v41,
							       sum(t.cm_dev_cnt_3ghy) v42,
							       ROUND(DECODE(sum(t.lm_dev_cnt_3ghy),
							                    0,
							                    0,
							                    (sum(t.cm_dev_cnt_3ghy) - sum(t.lm_dev_cnt_3ghy)) /
							                    sum(t.lm_dev_cnt_3ghy)),
							             4) v43,
							              sum(t.cd_dev_cnt_wirless) v51,
							       sum(t.cm_dev_cnt_wirless) v52,
							       ROUND(DECODE(sum(t.lm_dev_cnt_wirless),
							                    0,
							                    0,
							                    (sum(t.cm_dev_cnt_wirless) - sum(t.lm_dev_cnt_wirless)) /
							                    sum(t.lm_dev_cnt_wirless)),
							             4) v53,
							                sum(t.cd_dev_cnt_adsl) v61,
							       sum(t.cm_dev_cnt_adsl) v62,
							       ROUND(DECODE(sum(t.lm_dev_cnt_adsl),
							                    0,
							                    0,
							                    (sum(t.cm_dev_cnt_adsl) - sum(t.lm_dev_cnt_adsl)) /
							                    sum(t.lm_dev_cnt_adsl)),
							             4) v63,
							              sum(t.cd_dev_cnt_pstn) v71,
							       sum(t.cm_dev_cnt_pstn) v72,
							       ROUND(DECODE(sum(t.lm_dev_cnt_pstn),
							                    0,
							                    0,
							                    (sum(t.cm_dev_cnt_pstn) - sum(t.lm_dev_cnt_pstn)) /
							                    sum(t.lm_dev_cnt_pstn)),
							             4) v73,
							              sum(t.cd_pay_fee) v81,
							       sum(t.cm_pay_fee) v82,
							       ROUND(DECODE(sum(t.lm_pay_fee),
							                    0,
							                    0,
							                    (sum(t.cm_pay_fee) - sum(t.lm_pay_fee)) /
							                    sum(t.lm_pay_fee)),
							             4) v83
							             <!--  
							             ,
							       sum(t.royalty_comm_fee) v91,
							       sum(t.cm_royalty_comm_fee) v92,
							       ROUND(DECODE(sum(t.lm_royalty_comm_fee),
							                    0,
							                    0,
							                    (sum(t.cm_royalty_comm_fee) - sum(t.lm_royalty_comm_fee)) /
							                    sum(t.lm_royalty_comm_fee)),
							             4) v93
							             -->
							             
							  from g_ga.DM_D_CHNL_GM_DEVS t
							 where t.acct_date = '$dateId$'
							   and t.acct_month = substr('$dateId$',1,6)
							   and t.day = substr('$dateId$',7,8)
							 <isNotNull property="channelId">
								<isNotEqual property="channelId" compareValue="">
							   and t.cell_id = '$channelId$'
							   </isNotEqual>
							   <isEqual property="channelId" compareValue="">
								 and t.cell_id in (
								 SELECT DISTINCT CHANNEL_NO CHAN_ID
  FROM GBS_GI.G_GI_CHANNEL_EXT T
 WHERE T.CHANNEL_MANAGER = '$GMId$'
	<!-- 	  	  SELECT distinct a.chnl_code CHAN_ID
    			FROM PURE.CODE_CHNL_DEPART_patrol a,
    			(SELECT id from (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) start with id = (select dept_code from PURE.pure_user where login_id='$GMId$') connect BY prior id = parent_id) b 
   				where a.sale_id=b.id --> 
	         )
						    	</isEqual>
						     </isNotNull> )
							                        select 
							                        '1' p_id,
							                        '1' id,
							                        '销售' v0,
							                        nvl(v11,0) v1,
							                        nvl(v12,0) v2,
							                        nvl(v13,0)  v3                              
							                        from kpi_table
							                        union all
							                         select 
							                         '1' p_id,
							       '2' id,
							       '离网' v0,
							                        nvl(v21,0),
							                        nvl(v22,0),
							                        nvl(v23,0)                               
							                        from kpi_table
							                          union all
							                       
							                         select 
							                       '2' p_id,
							       '3' id,
							       '销售' v0,
							                        nvl(v31,0),
							                        nvl(v32,0),
							                        nvl(v33,0)                               
							                        from kpi_table
							                          union all
							                         select 
							                        '2' p_id,
							       '4' id,
							       '合约' v0,
							                        nvl(v41,0),
							                        nvl(v42,0),
							                        nvl(v43,0)                               
							                        from kpi_table
							                          union all
							                         select 
							                        '2' p_id,
							       '5' id,
							       '上网卡' v0,
							                        nvl(v51,0),
							                        nvl(v52,0),
							                        nvl(v53,0)                               
							                        from kpi_table
							                          union all
							                         select 
							                      '3' p_id,
							       '6' id,
							       '宽带装机' v0,
							                        nvl(v61,0),
							                        nvl(v62,0),
							                        nvl(v63,0)                               
							                        from kpi_table
							                          union all
							                         select 
							                     '3' p_id,
							       '7' id,
							       '固话装机' v0,
							                        nvl(v71,0),
							                        nvl(v72,0),
							                        nvl(v73,0)                               
							                        from kpi_table
							                          union all
							                         select 
							                         '3' p_id,
							       '8' id,
							       '缴费金额' v0,
							                        nvl(v81,0),
							                        nvl(v82,0),
							                        nvl(v83,0)                               
							                        from kpi_table
							                        
	</select>
	
	<select id="getTitles" resultClass="java.util.HashMap">
		select 'v0' key_, '监控指标' desc_, '*s' format , '1' id
		  from dual
		union all
		select 'v1' key_, '当日值' desc_, '*d' format , '2' id
		  from dual
		union all
		select 'v2' key_, '月累计' desc_, '*d' format , '3' id
		  from dual
		union all
		select 'v3' key_, '月环比' desc_, '*d' format , '4' id
		  from dual
	</select>
	
	<!-- 销售统计二级-->
	<select id="listTwo" resultClass="java.util.HashMap" remapResults="true">
	 select t.cell_id id,
			t.cell_desc desc_,
			<isEqual property="kpiCode" compareValue="1">
		       nvl(sum(t.cd_dev_cnt_2g),0) v1,
		       nvl(sum(t.cm_dev_cnt_2g),0) v2,
		       nvl(ROUND(DECODE(sum(t.lm_dev_cnt_2g),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_2g) - sum(t.lm_dev_cnt_2g)) /
		                    sum(t.lm_dev_cnt_2g)),
		             4),0) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="2">
		       nvl(sum(t.cd_active_cnt_2g),0) v1,
		       nvl(sum(t.cm_active_cnt_2g),0) v2,
		       nvl(ROUND(DECODE(sum(t.lm_active_cnt_2g),
		                    0,
		                    0,
		                    (sum(t.cm_active_cnt_2g) - sum(t.lm_active_cnt_2g)) /
		                    sum(t.lm_active_cnt_2g)),
		             4),0) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="3">
		       nvl(sum(t.cd_dev_cnt_3g),0) v1,
		       nvl(sum(t.cm_dev_cnt_3g),0) v2,
		       nvl(ROUND(DECODE(sum(t.lm_dev_cnt_3g),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_3g) - sum(t.lm_dev_cnt_3g)) /
		                    sum(t.lm_dev_cnt_3g)),
		             4),0) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="4">
		       nvl(sum(t.cd_dev_cnt_3ghy),0) v1,
		       nvl(sum(t.cm_dev_cnt_3ghy),0) v2,
		       nvl(ROUND(DECODE(sum(t.lm_dev_cnt_3ghy),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_3ghy) - sum(t.lm_dev_cnt_3ghy)) /
		                    sum(t.lm_dev_cnt_3ghy)),
		             4),0) v3
		     </isEqual>
		     <isEqual property="kpiCode" compareValue="5">
		       nvl(sum(t.cd_dev_cnt_wirless),0) v1,
		       nvl(sum(t.cm_dev_cnt_wirless),0) v2,
		       nvl(ROUND(DECODE(sum(t.lm_dev_cnt_wirless),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_wirless) - sum(t.lm_dev_cnt_wirless)) /
		                    sum(t.lm_dev_cnt_wirless)),
		             4),0) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="6">
		       nvl(sum(t.cd_dev_cnt_adsl),0) v1,
		       nvl(sum(t.cm_dev_cnt_adsl),0) v2,
		       nvl(ROUND(DECODE(sum(t.lm_dev_cnt_adsl),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_adsl) - sum(t.lm_dev_cnt_adsl)) /
		                    sum(t.lm_dev_cnt_adsl)),
		             4),0) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="7">
		       nvl(sum(t.cd_dev_cnt_pstn),0) v1,
		       nvl(sum(t.cm_dev_cnt_pstn),0) v2,
		       nvl(ROUND(DECODE(sum(t.lm_dev_cnt_pstn),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_pstn) - sum(t.lm_dev_cnt_pstn)) /
		                    sum(t.lm_dev_cnt_pstn)),
		             4),0) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="8">
		       nvl(sum(t.cd_pay_fee),0) v1,
		       nvl(sum(t.cm_pay_fee),0) v2,
		       nvl(ROUND(DECODE(sum(t.lm_pay_fee),
		                    0,
		                    0,
		                    (sum(t.cm_pay_fee) - sum(t.lm_pay_fee)) /
		                    sum(t.lm_pay_fee)),
		             4),0) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="9">
		       nvl(sum(t.royalty_comm_fee),0) v1,
		       nvl(sum(t.cm_royalty_comm_fee),0) v2,
		       nvl(ROUND(DECODE(sum(t.lm_royalty_comm_fee),
		                    0,
		                    0,
		                    (sum(t.cm_royalty_comm_fee) - sum(t.lm_royalty_comm_fee)) /
		                    sum(t.lm_royalty_comm_fee)),
		             4),0) v3
			</isEqual>
			  from g_ga.DM_D_CHNL_GM_DEVS t
			 where t.acct_date = '$dateId$'
			   and t.acct_month = substr('$dateId$',1,6)
			   and t.day = substr('$dateId$',7,8)
			   <isNotNull property="channelId">
								<isNotEqual property="channelId" compareValue="">
							   and t.cell_id = '$channelId$'
							   </isNotEqual>
							   <isEqual property="channelId" compareValue="">
							    and t.cell_id in (
								 SELECT DISTINCT CHANNEL_NO CHAN_ID
  FROM GBS_GI.G_GI_CHANNEL_EXT T
 WHERE T.CHANNEL_MANAGER = '$GMId$'
	<!-- 	  	  SELECT distinct a.chnl_code CHAN_ID
    			FROM PURE.CODE_CHNL_DEPART_patrol a,
    			(SELECT id from (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) start with id = (select dept_code from PURE.pure_user where login_id='$GMId$') connect BY prior id = parent_id) b 
   				where a.sale_id=b.id --> 
	          )
						    	</isEqual>
						     </isNotNull> 
			   group by t.cell_id,t.cell_desc
			   order by v1
	</select>
	
	<select id="getTitlesTwo" resultClass="java.util.HashMap">
		select 'desc_' key_, '网点名称' desc_, '*s' format , '1' id
		  from dual
		union all
		select 'v1' key_, '当日值' desc_, '*d' format , '2' id
		  from dual
		union all
		select 'v2' key_, '月累计' desc_, '*d' format , '3' id
		  from dual
		union all
		select 'v3' key_, '月环比' desc_, '*d' format , '4' id
		  from dual
	</select>
	
	<!-- 销售统计二级趋势图-->
	<select id="listThree" resultClass="java.util.HashMap">
	 select t.acct_date date_,
			<isEqual property="kpiCode" compareValue="1">
		       sum(t.cd_dev_cnt_2g) v1,
		       sum(t.cm_dev_cnt_2g) v2,
		       ROUND(DECODE(sum(t.lm_dev_cnt_2g),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_2g) - sum(t.lm_dev_cnt_2g)) /
		                    sum(t.lm_dev_cnt_2g)),
		             4) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="2">
		       sum(t.cd_active_cnt_2g) v1,
		       sum(t.cm_active_cnt_2g) v2,
		       ROUND(DECODE(sum(t.lm_active_cnt_2g),
		                    0,
		                    0,
		                    (sum(t.cm_active_cnt_2g) - sum(t.lm_active_cnt_2g)) /
		                    sum(t.lm_active_cnt_2g)),
		             4) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="3">
		       sum(t.cd_dev_cnt_3g) v1,
		       sum(t.cm_dev_cnt_3g) v2,
		       ROUND(DECODE(sum(t.lm_dev_cnt_3g),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_3g) - sum(t.lm_dev_cnt_3g)) /
		                    sum(t.lm_dev_cnt_3g)),
		             4) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="4">
		       sum(t.cd_dev_cnt_3ghy) v1,
		       sum(t.cm_dev_cnt_3ghy) v2,
		       ROUND(DECODE(sum(t.lm_dev_cnt_3ghy),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_3ghy) - sum(t.lm_dev_cnt_3ghy)) /
		                    sum(t.lm_dev_cnt_3ghy)),
		             4) v3
		     </isEqual>
		     <isEqual property="kpiCode" compareValue="5">
		       sum(t.cd_dev_cnt_wirless) v1,
		       sum(t.cm_dev_cnt_wirless) v2,
		       ROUND(DECODE(sum(t.lm_dev_cnt_wirless),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_wirless) - sum(t.lm_dev_cnt_wirless)) /
		                    sum(t.lm_dev_cnt_wirless)),
		             4) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="6">
		       sum(t.cd_dev_cnt_adsl) v1,
		       sum(t.cm_dev_cnt_adsl) v2,
		       ROUND(DECODE(sum(t.lm_dev_cnt_adsl),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_adsl) - sum(t.lm_dev_cnt_adsl)) /
		                    sum(t.lm_dev_cnt_adsl)),
		             4) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="7">
		       sum(t.cd_dev_cnt_pstn) v1,
		       sum(t.cm_dev_cnt_pstn) v2,
		       ROUND(DECODE(sum(t.lm_dev_cnt_pstn),
		                    0,
		                    0,
		                    (sum(t.cm_dev_cnt_pstn) - sum(t.lm_dev_cnt_pstn)) /
		                    sum(t.lm_dev_cnt_pstn)),
		             4) v3
		    </isEqual>
		    <isEqual property="kpiCode" compareValue="8">
		       sum(t.cd_pay_fee) v1,
		       sum(t.cm_pay_fee) v2,
		       ROUND(DECODE(sum(t.lm_pay_fee),
		                    0,
		                    0,
		                    (sum(t.cm_pay_fee) - sum(t.lm_pay_fee)) /
		                    sum(t.lm_pay_fee)),
		             4) v3
		    </isEqual>
		    <!-- 提成佣金不要了
		    <isEqual property="kpiCode" compareValue="9">
		       sum(t.royalty_comm_fee) v1,
		       sum(t.cm_royalty_comm_fee) v2,
		       ROUND(DECODE(sum(t.lm_royalty_comm_fee),
		                    0,
		                    0,
		                    (sum(t.cm_royalty_comm_fee) - sum(t.lm_royalty_comm_fee)) /
		                    sum(t.lm_royalty_comm_fee)),
		             4) v3
			</isEqual>
			 -->
			 
			  from g_ga.DM_D_CHNL_GM_DEVS t
			 where t.acct_date &lt;= '$dateId$'
			   and t.acct_date >
			       to_char(to_date('$dateId$', 'YYYYMMDD') - 30, 'YYYYMMDD')
			   and t.acct_month in
			       (to_char(add_months(to_date(substr('$dateId$',1,6), 'YYYYMM'), -1), 'YYYYMM'),
			       substr('$dateId$',1,6))
			   <isNotNull property="channelId">
								<isNotEqual property="channelId" compareValue="">
							   and t.cell_id = '$channelId$'
							   </isNotEqual>
							   <isEqual property="channelId" compareValue="">
							 	and t.cell_id in (
								  SELECT DISTINCT CHANNEL_NO CHAN_ID
  FROM GBS_GI.G_GI_CHANNEL_EXT T
 WHERE T.CHANNEL_MANAGER = '$GMId$'
	<!-- 	  	  SELECT distinct a.chnl_code CHAN_ID
    			FROM PURE.CODE_CHNL_DEPART_patrol a,
    			(SELECT id from (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) start with id = (select dept_code from PURE.pure_user where login_id='$GMId$') connect BY prior id = parent_id) b 
   				where a.sale_id=b.id --> 
	         )
						    	</isEqual>
						     </isNotNull> 
			    group by t.acct_date
 				order by t.acct_date desc
	</select>

	<select id="getTitlesThree" resultClass="java.util.HashMap">
		select 'date_' key_, '日期' desc_, '*s' format , '1' id
		  from dual
		union all
		select 'v1' key_, '当日值' desc_, '*d' format , '2' id
		  from dual
		union all
		select 'v2' key_, '月累计' desc_, '*d' format , '3' id
		  from dual
		union all
		select 'v3' key_, '月环比' desc_, '*d' format , '4' id
		  from dual
	</select>
</sqlMap>
