<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="marketing">
	<select id="getMarketingList" resultClass="java.util.HashMap" remapResults="true">
		select t.*,a.AREA_ID_DESC_PROV from 
		<isEqual property="type" compareValue="0" prepend=" ">
			jike.inf_market_case_library t 
		</isEqual>
		<isEqual property="type" compareValue="1" prepend=" ">
			jike.inf_solution t
		</isEqual>
		,dim.dim_area_no a 
		where t.is_show='0' and t.city = a.area_no
		<isNotEmpty property="caseName" prepend="and">
			t.case_name like '%'||'$caseName$'||'%'
		</isNotEmpty>
		<isNotEmpty property="areaNo">
			<isEqual property="areaNo" compareValue="-1">
				<isNotEmpty property="city">
					and t.city='$city$'
				</isNotEmpty>
			</isEqual>
			<isNotEqual property="areaNo" compareValue="-1">
				and (t.city='$areaNo$' or t.city = '018')
			</isNotEqual>
		</isNotEmpty>
	</select>
	<select id="getDiShi" resultClass="java.util.HashMap">
		select t.area_no "key",t.area_id_desc_prov "value" from dim.dim_area_no t order by "key" 
	</select>
	<insert id="insertMarketingCase">
		insert into 
			<isEqual property="type" compareValue="0" prepend=" ">
				jike.inf_market_case_library t 
			</isEqual>
			<isEqual property="type" compareValue="1" prepend=" ">
				jike.inf_solution t
			</isEqual>
			 (
				city, 
				industry, 
				case_name, 
				icon, 
				is_show, 
				id, 
				icon_thumb
		) values(
				'$city$',
				'$industry$',
				'$caseName$',
				'$ICON$',
				'$isShow$',
				'$id$',
				'$ICON_THUMB$'
		)
	</insert>
	<delete id="deleteMarketingCase" >
		delete from 
		<isEqual property="type" compareValue="0" prepend=" ">
			jike.inf_market_case_library t 
		</isEqual>
		<isEqual property="type" compareValue="1" prepend=" ">
			jike.inf_solution t
		</isEqual>
		where t.id='$id$'
	</delete>
	<delete id="deleteResource">
		delete from jike.inf_resource_library t
		where t.link_id='$id$'
	</delete>
	<select id="getMarketingCase" resultClass="java.util.HashMap" remapResults="true">
		select t.*,a.AREA_ID_DESC_PROV from 
		<isEqual property="type" compareValue="0" prepend=" ">
			jike.inf_market_case_library t 
		</isEqual>
		<isEqual property="type" compareValue="1" prepend=" ">
			jike.inf_solution t
		</isEqual>
		,dim.dim_area_no a 
		where t.id='$id$' and t.city = a.area_no
	</select>
	<select id="getAttachmentList" resultClass="java.util.HashMap">
		select * from jike.inf_resource_library t where t.link_id='$id$'
	</select>
	<update id="updateMarketingCase">
		update 
		<isEqual property="type" compareValue="0" prepend=" ">
			jike.inf_market_case_library t 
		</isEqual>
		<isEqual property="type" compareValue="1" prepend=" ">
			jike.inf_solution t
		</isEqual>
				set
				city='$city$', 
				industry='$industry$', 
				<isNotEmpty property="ICON">
					icon='$ICON$', 
				</isNotEmpty>
				is_show='$isShow$', 
				<isNotEmpty property="ICON_THUMB">
					icon_thumb='$ICON_THUMB$',
				</isNotEmpty>
				case_name='$caseName$'
				where id='$id$'
				
	</update>
</sqlMap>
