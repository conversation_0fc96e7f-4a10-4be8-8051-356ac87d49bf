<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="mobile.xundian">

	<select id="getMyMobileChannelNew" resultClass="java.util.HashMap">
		SELECT t.CHANNEL_NO CHAN_ID,t.CHANNEL_NO_DESC NAME,
		nvl(c.CONTACTS_ADDR,'') ADDRESS,
		nvl(c.CHANNEL_CONTACTS,'')
		AGENT_CUSTOMER_DESC,nvl(c.CONTACTS_PHONE1,'') TELEPHONE,
		nvl(d.longitude,0) LOG,
		nvl(d.latitude,0) LAT
		FROM
		(select * from DMCODE.G_DMCODE_CHANNEL_NO T WHERE T.STATE != '1' ) t,PURE.CODE_CHNL_DEPART_ALL a,
		(SELECT id from PURE.org_organization_mod
		start with id = (select dept_code from PURE.pure_user where login_id='$username$') connect BY prior id = parent_id) b,
		(select * from
		g_gi.g_gi_channel_ext ) c,g_ga.dm_chnl_gi_info d
		where a.chnl_code=t.channel_no(+) and a.sale_id=b.id
		and a.chnl_code=c.channel_no
		and t.channel_no=d.chnl_id(+)
		<isNotNull property="channelIdDesc">
			<isNotEqual property="channelIdDesc" compareValue="">
				and t.CHANNEL_NO_DESC like '%$channelIdDesc$%'
			</isNotEqual>
		</isNotNull>
	</select>

	<select id="getMyMobileChannelNews" resultClass="java.util.HashMap">
		select distinct T.CHANNEL_NO CHAN_ID,T.CHANNEL_NO_DESC CHAN_NAME,
		T.CHANNEL_NO_DESC AGENT_CUSTOMER_DESC,
		w.CONTACTS_ADDR CHAN_ADDR,
		w.CONTACTS_PHONE1 TELEPHONE,
		nvl(d.longitude,0) X,
		nvl(d.latitude,0) Y
		from g_dmcode.G_DMCODE_CHANNEL_NO T,g_gi.g_gi_channel_ext w,
		g_ga.dm_chnl_gi_info d
		where T.channel_no='$channelId$'
		and w.CHANNEL_NO(+)=t.CHANNEL_NO
		and t.CHANNEL_NO = d.chnl_id(+)
	</select>

	<select id="getMyMobileChannel1Title" resultClass="java.util.HashMap" remapResults="true">
		select 'NAME' key_,
		'网点名称' desc_,
		'*s' format
		from dual
		union
		select 'ADDRESS' key_,
		'地址' desc_,
		'*s' format
		from dual
		union
		select 'LOG' key_,
		'经度' desc_,
		'*d' format
		from dual
		union
		select 'LAT' key_,
		'纬度' desc_,
		'*d' format
		from dual
		union
		select 'AGENT_CUSTOMER_DESC' key_,
		'代理商' desc_,
		'*s' format
		from dual
		union
		select 'TELEPHONE' key_,
		'代理商电话' desc_,
		'*s' format
		from dual
	</select>

	<select id="myXunDianTaskList" resultClass="java.util.HashMap">
		select a.CHAN_ID,
		nvl(b.score,0) score,
		'0' recommend,
		nvl(b.finsh,0) finsh, nvl(b.unfinsh,0) unfinsh
		from
		(select b.chnl_id,
		sum(decode(b.score,'null',null,b.score)) / count(0) score,
		sum(decode(b.visit_stutas, 1, 1, 0)) finsh,
		sum(decode(b.visit_stutas, 0, 1, 0)) unfinsh
		from g_ga.dm_visit_info_self_d b
		where substr(b.acct_date,1,6) =to_char(sysdate,'yyyymm')
		group by chnl_id) b,
		(
		SELECT distinct a.chnl_code CHAN_ID
		FROM CODE_CHNL_DEPART_ALL a,
		(SELECT id from PURE.org_organization_mod start with id = (select dept_code from PURE.pure_user where login_id='$params.username$') connect BY prior id
		= parent_id) b
		where a.sale_id=b.id
		)a where a.CHAN_ID=b.chnl_id(+)
	</select>

	<!-- 获得我的网点任务以及情况列表 -->
	<select id="myXunDianTaskListTitle" resultClass="java.util.HashMap" remapResults="true">
		select 'chnl_id' key_,
		'网点编码' desc_,
		'*s' format
		from dual
		union
		select 'score' key_,
		'综合评分' desc_,
		'*d' format
		from dual
		union
		select 'recommend' key_,
		'推荐指数' desc_,
		'*d' format
		from dual
		union
		select 'finsh' key_,
		'已完成' desc_,
		'*s' format
		from dual
		union
		select 'unfinsh' key_,
		'未完成' desc_,
		'*d' format
		from dual
	</select>
	<!-- 获取我的模板列表 -->
	<select id="myTemplateList" resultClass="java.util.HashMap">
		select LOGIN_ID,
		MODEL_NAME,
		MODEL_ID,
		MODEL_DETAIL,
		CREAT_DATE
		from g_ga.dm_visit_user_model where login_id='$username$'
	</select>
	<!-- 获取试题列表 -->
	<select id="templateList" resultClass="java.util.HashMap">
		select content_id id_,
		content_desc desc_,
		'*'||content_type format,
		LISTAGG(anser_content, '~') WITHIN
		GROUP(
		ORDER BY content_id) AS anser_contents
		from (select t.content_id,
		t.content_desc,
		t.content_status,
		t.content_type,
		c.anser_code || '_' || c.anser_content anser_content
		from g_ga.dm_visit_content_model t,
		g_ga.dm_visit_content_rel c
		where t.content_id = c.content_id
		and content_status='1'
		order by t.shunxu,anser_code asc)
		group by content_id,
		content_desc,
		content_status,
		content_type
		order by content_type asc,content_id asc
	</select>

	<insert id="creatMyTemplate">
		insert into g_ga.DM_VISIT_USER_MODEL
		(login_id, model_name, model_id, model_detail, creat_date)
		VALUES('$username$',
		'$modelname$',
		'$uuid$',
		'$template$',
		SYSDATE
		)
	</insert>

	<insert id="createTask">
		insert into g_ga.dm_visit_info_self_d
		(acct_date, visit_id, chnl_id, chnl_name, area_id, area_desc, city_id, city_desc, chnl_chief_id, chnl_chief_desc, chnl_chief_phone, location_desc,
		template_id, visit_name, visit_stutas, visit_date, score, chnl_type, chnl_lvl, location_size, gm_id, gm_name, new_grid_id, cm_sales_card,
		cm_pay_times, cm_pay_fee, new_grid_name)
		select
		acct_date,'$uuid$',chnl_id,chnl_name,area_id,area_desc,city_id,city_desc,chnl_chief_id,chnl_chief_desc,chnl_chief_phone,'$locationDesc$','$moduleId$','$visitnamestr$',1,sysdate,'$score$',chnl_type,chnl_lvl,'',gm_id,gm_name,new_grid_id,cm_sales_card,cm_pay_times,cm_pay_fee,new_grid_name
		from
		g_ga.dm_visit_info_d_cvs t where t.acct_date='$dateId$' and t.chnl_id='$channelId$'
	</insert>

	<insert id="answer">
		insert into g_ga.dm_visit_self_detail_d
		(visit_id,content_id,anser_code)
		values
		('$uuidStr$','$id$','$value$')
	</insert>

	<update id="updatePhonePath">
		update g_ga.dm_visit_self_detail_d
		set anser_content='$savePath$'
		where visit_id='$uuidStr$' and content_id='$uploadFileCode$'
	</update>

	<!-- 任务巡店首页列表 -->
	<select id="dataList" resultClass="java.util.HashMap">
		select nvl(t.chnl_id,'') chnl_id,
		case when t.chnl_id is null then '合计' else max(t.chnl_name) end chnl_name,
		count((case
		when t.visit_stutas = 1 then
		t.visit_id
		end)) tasknumber,
		count((case
		when t.visit_stutas = 0 then
		t.visit_id
		end)) unfinished
		from g_ga.DM_VISIT_INFO_D t
		where 1=1
		<isNotNull property="GMId">
			<isNotEqual property="GMId" compareValue="">
				and T.chnl_id
				in (
				SELECT distinct a.chnl_code CHAN_ID
				FROM PURE.CODE_CHNL_DEPART_all a,
				(SELECT id from PURE.org_organization_mod start with id = (select dept_code from PURE.pure_user where login_id='$GMId$') connect BY prior id =
				parent_id) b
				where a.sale_id=b.id
				)
				and t.gm_id='$GMId$'
			</isNotEqual>
		</isNotNull>
		<isNotEmpty property="channelIdDesc">
			and t.chnl_name like '%$channelIdDesc$%'
		</isNotEmpty>
		<isNotEmpty property="visitName">
			and t.visit_name like '%$visitName$%'
		</isNotEmpty>
		<isNotNull property="dateId">
			<isNotEqual property="dateId" compareValue="">
				and t.start_date between substr('$dateId$',1,6)||'01' and '$dateId$'
			</isNotEqual>
		</isNotNull>
		group by rollup(t.chnl_id)
		<dynamic prepend=" order by ">
			nvl2(chnl_id,0,1) desc
			<isNotNull property="sortCol">
				,$sortCol$
				<isNotNull property="sortOrder">$sortOrder$</isNotNull>
			</isNotNull>
			<isNotNull property="sortCol">
				,chnl_id
			</isNotNull>
		</dynamic>
	</select>

	<!-- 查看和寻访 -->
	<select id="selectView" resultClass="java.util.HashMap">
		select t.visit_id,
		t.chnl_id,
		t.chnl_name,
		t.visit_date,
		t.acct_date,
		t.start_date,
		t.end_date,
		t.visit_name,
		t.template_id
		from g_ga.DM_VISIT_INFO_D t
		where 1=1
		<isNotNull property="GMId">
			<isNotEqual property="GMId" compareValue="">
				and T.chnl_id
				in (
				SELECT distinct a.chnl_code CHAN_ID
				FROM PURE.CODE_CHNL_DEPART_ALL a,
				(SELECT id from PURE.org_organization_mod start with id = (select dept_code from PURE.pure_user where login_id='$GMId$') connect BY prior id =
				parent_id) b
				where a.sale_id=b.id
				)
				and t.gm_id='$GMId$'
			</isNotEqual>
		</isNotNull>
		<isNotNull property="dateId">
			<isNotEqual property="dateId" compareValue="">
				and t.start_date between substr('$dateId$',1,6)||'01' and '$dateId$'
			</isNotEqual>
		</isNotNull>
		<isNotNull property="channelId">
			<isNotEqual property="channelId" compareValue="">
				and t.chnl_id = '$channelId$'
			</isNotEqual>
		</isNotNull>
		<isNotNull property="viewType">
			<isNotEqual property="viewType" compareValue="">
				and t.visit_stutas = '$viewType$'
			</isNotEqual>
		</isNotNull>
	</select>

	<!-- 根据visitId获取用户channelId -->
	<select id="getChannelId" resultClass="java.lang.String">
		select T.CHNL_ID from g_ga.DM_VISIT_INFO_D t where t.visit_id='$visitId$'
	</select>


	<select id="getLocation" resultClass="java.util.HashMap">
		select t.longitude x, t.latitude y
		from g_ga.dm_chnl_gi_info t
		where t.chnl_id = '$channelId$'
		and t.longitude between 100 and 300
	</select>

	<!-- 查看或者寻访的明细 -->
	<select id="selectViewDetail" resultClass="java.util.HashMap">
		select *
		from g_ga.DM_VISIT_INFO_D t
		where t.visit_id = '$visitId$'
	</select>

	<insert id="cjLocation">
		insert into g_ga.DM_CHNL_GI_INFO
		(chnl_id,longitude,latitude,login_id,update_time)
		VALUES('$channelId$',
		'$longitude$',
		'$latitude$',
		'$GMId$',
		SYSDATE
		)
	</insert>

	<delete id="deleteLocation">
		delete from g_ga.DM_CHNL_GI_INFO where chnl_id='$channelId$'
	</delete>


	<!-- 查询参数切片 月 -->
	<sql id="XunDian_query_parameters">
		<isNotNull property="yueId">
			and t.acct_month = '$yueId$'
		</isNotNull>
		<isNotNull property="dateId">
			and t.visit_date like to_date('$dateId$','yyyy/mm/dd')
		</isNotNull>
		<isNotEmpty property="channelDesc">
			and t.chnl_name like '%$channelDesc$%'
		</isNotEmpty>
		<isNotNull property="channelzrrDesc">
			<isNotEqual property="channelzrrDesc" compareValue="">
				and t.gm_name like '%$channelzrrDesc$%'
			</isNotEqual>
		</isNotNull>
		<isNotEmpty property="visitNameDesc">
			and t.visit_name like '%$visitNameDesc$%'
		</isNotEmpty>
		<isNotNull property="templateId">
			<isNotEqual property="templateId" compareValue="-1">
				and t.template_id = '$templateId$'
			</isNotEqual>
		</isNotNull>
		<isNotNull property="channel">
			<isNotEqual property="channel" compareValue="">
				and t.chnl_id like '%$channel$%'
			</isNotEqual>
		</isNotNull>
		<isNotNull property="gridid">
			<isNotEqual property="gridid" compareValue="">
				and t.new_grid_id = '$gridid$'
			</isNotEqual>
		</isNotNull>
		<isNotNull property="GMid">
			<isNotEqual property="GMid" compareValue="">
				and t.gm_id = '$GMid$'
			</isNotEqual>
		</isNotNull>

	</sql>

	<!-- 查询参数切片 -->
	<sql id="XunDian_query_open1_parameters">
		<isNotNull property="RatingType">
			<isEqual property="RatingType" compareValue="VC">
				and 1=1
			</isEqual>
			<isEqual property="RatingType" compareValue="VA">
				and to_char(VISIT_DATE,'yyyymmdd') between start_date and end_date
			</isEqual>
			<isEqual property="RatingType" compareValue="VN">
				and to_char(VISIT_DATE, 'yyyymmdd') &gt; end_date
			</isEqual>
			<isEqual property="RatingType" compareValue="T1">
				and adv_door in ( 'B','D','F')
			</isEqual>
			<isEqual property="RatingType" compareValue="T2">
				and adv_paste = 'C'
			</isEqual>
			<isEqual property="RatingType" compareValue="T3">
				and adv_air = 'C'
			</isEqual>
			<isEqual property="ratingType" compareValue="T4">
				and adv_know = 'C'
			</isEqual>
			<isEqual property="RatingType" compareValue="T5">
				and sys_op = 'C'
			</isEqual>
			<isEqual property="RatingType" compareValue="T6">
				and sale_minded = 'C'
			</isEqual>
			<isEqual property="RatingType" compareValue="T7">
				and card_store = 'A'
			</isEqual>
			<isEqual property="RatingType" compareValue="T8">
				and adv_content = 'B'
			</isEqual>
			<isEqual property="RatingType" compareValue="T9">
				and location_size &gt; 100
			</isEqual>
		</isNotNull>
	</sql>

	<select id="sellAreaInfo" resultClass="java.util.HashMap">
		select /*+ordered*/ t.id as "vkey",
		t.name as "vdesc",
		t.GRADE as "level"
		from PURE.ORG_ORGANIZATION_MOD t
		where t.id = '$value$'
	</select>

	<!-- 渠道类型 -->
	<sql id="selectItem_channelType">
		(SELECT SUBSTR(PATH, 1, INSTR(PATH, '-') - 1) P_CHANNEL_TYPE,
		SUB_NODE CHANNEL_TYPE FROM (SELECT
		SUBSTR(SYS_CONNECT_BY_PATH(PARENT_CHNL_KIND_ID, '-'), 2) || '-' PATH,
		C.PARENT_CHNL_KIND_ID SUB_NODE FROM g_dmcode.g_dmcode_channel_type C
		CONNECT BY
		C.P_PARENT_CHNL_KIND_ID = PRIOR C.PARENT_CHNL_KIND_ID START WITH C.P_PARENT_CHNL_KIND_ID =
		'$channelType$' union all select
		'$channelType$' || '-' path,
		'$channelType$' sub_node from dual))
	</sql>

	<select id="xundian_select_openSum" resultClass="java.util.HashMap">
		SELECT /*+ordered*/ t.gm_id V0,
		t.gm_name V0_DESC,
		t.new_grid_id VWG,
		t.new_grid_name VW_DESC,
		nvl(count(distinct visit_id),'0') VS,
		nvl(sum(case when visit_stutas = '1' then 1 end),'0') VA,
		nvl(sum(case when visit_stutas = '1' and to_char(visit_date, 'yyyymmdd') &gt; end_date then 1 end),'0') VW,
		nvl(sum(case when visit_stutas = '1' and adv_door in ( 'B','D','F') then 1 end),'0') T1,
		nvl(sum(case when visit_stutas = '1' and adv_paste = 'C' then 1 end),'0') T2,
		nvl(sum(case when visit_stutas = '1' and adv_air = 'C' then 1 end),'0') T3,
		nvl(sum(case when visit_stutas = '1' and adv_know = 'C' then 1 end),'0') T4,
		nvl(sum(case when visit_stutas = '1' and sys_op = 'C' then 1 end),'0') T5,
		nvl(sum(case when visit_stutas = '1' and sale_minded = 'C' then 1 end),'0') T6,
		nvl(sum(case when visit_stutas = '1' and card_store = 'A' then 1 end),'0') T7,
		nvl(sum(case when visit_stutas = '1' and adv_content = 'B' then 1 end),'0') T8,
		nvl(sum(case when visit_stutas = '1' and location_size &gt; 100 then 1 end),'0') T9,
		round(nvl(DECODE(count(distinct visit_id),
		0,
		0,
		SUM(score) / count(distinct visit_id)),'0'),1) VP
		FROM (select * from g_ga.DM_VISIT_INFO_D t WHERE 1=1
		<include refid="XunDian_query_parameters" />
		) T,
		(SELECT SUBSTR(PATH,
		1,
		INSTR(PATH, '-') - 1) P_SELL_AREA,
		GRID_ID
		FROM (SELECT SUBSTR(SYS_CONNECT_BY_PATH(ID,
		'-'),
		2) || '-' PATH,
		T.ID GRID_ID
		FROM (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) T
		CONNECT BY T.PARENT_ID = PRIOR
		T.ID
		START WITH CASE
		WHEN EXISTS
		(SELECT *
		FROM PURE.org_organization_mod T1
		WHERE T1.PARENT_ID =
		'$sellArea$') THEN
		T.PARENT_ID
		ELSE
		T.ID
		END = '$sellArea$')
		UNION 
		SELECT
		'$sellArea$' P_SELL_AREA, '$sellArea$' GRID_ID
		FROM DUAL) yx,

		<isNotEqual property="channelType" compareValue="root">
			(
			<include refid="selectItem_channelType" />
			) qd,
		</isNotEqual>

		PURE.org_organization_mod b
		where t.new_grid_id = yx.grid_id
		<isNotEqual property="channelType" compareValue="root">
			and t.chnl_type = qd.channel_type
		</isNotEqual>

		and yx.p_sell_area = b.id

		<include refid="XunDian_query_open1_parameters" />

		group by t.gm_id, t.gm_name ,t.new_grid_name,t.new_grid_id
		<dynamic prepend=" order by ">
			<isNotNull property="sortCol">
				$sortCol$
				<isNotNull property="sortOrder">$sortOrder$</isNotNull>
			</isNotNull>
		</dynamic>
	</select>

	<select id="xundian_select_gonggao" resultClass="java.util.HashMap">
		SELECT /*+ordered*/
		nvl(count(distinct visit_id),'0')VS,
		nvl(count(distinct case when visit_stutas = '1' then visit_id end),'0')VC,
		nvl(count(distinct case when visit_stutas = '1' and to_char(VISIT_DATE,'yyyymmdd') between start_date and end_date then visit_id end),'0') VA,
		nvl(count(distinct case when visit_stutas = '1' and to_char(VISIT_DATE, 'yyyymmdd') &gt; end_date then visit_id end),'0') VN,
		nvl(count(distinct case when visit_stutas = '1' and location_size &gt; 100 then visit_id end),'0') T9
		FROM g_ga.DM_VISIT_INFO_D T,
		(SELECT SUBSTR(PATH,
		1,
		INSTR(PATH, '-') - 1) P_SELL_AREA,
		GRID_ID
		FROM (SELECT SUBSTR(SYS_CONNECT_BY_PATH(ID,
		'-'),
		2) || '-' PATH,
		T.ID GRID_ID
		FROM (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) T
		CONNECT BY T.PARENT_ID = PRIOR
		T.ID
		START WITH CASE
		WHEN EXISTS
		(SELECT *
		FROM PURE.org_organization_mod T1
		WHERE T1.PARENT_ID =
		'$sellArea$') THEN
		T.PARENT_ID
		ELSE
		T.ID
		END = '$sellArea$')
		UNION 
		SELECT
		'$sellArea$' P_SELL_AREA, '$sellArea$' GRID_ID
		FROM DUAL) yx,

		<isNotEqual property="channelType" compareValue="root">
			(
			<include refid="selectItem_channelType" />
			) qd,
		</isNotEqual>

		(SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) b
		where t.new_grid_id(+) = yx.grid_id

		<isNotEqual property="channelType" compareValue="root">
			and t.chnl_type = qd.channel_type
		</isNotEqual>

		and yx.p_sell_area = b.id

		<isNotNull property="monthId">
			and t.acct_month = '$monthId$'
		</isNotNull>
		union all
		SELECT /*+ordered*/
		nvl(count(distinct visit_id),'0')VS,
		nvl(count(distinct case when visit_stutas = '1' then visit_id end),'0')VC,
		nvl(count(distinct case when visit_stutas = '1' and to_char(VISIT_DATE,'yyyymmdd') between start_date and end_date then visit_id end),'0') VA,
		nvl(count(distinct case when visit_stutas = '1' and to_char(VISIT_DATE, 'yyyymmdd') &gt; end_date then visit_id end),'0') VN,
		nvl(count(distinct case when visit_stutas = '1' and location_size &gt; 100 then visit_id end),'0') T9
		FROM g_ga.DM_VISIT_INFO_D T,
		(SELECT SUBSTR(PATH,
		1,
		INSTR(PATH, '-') - 1) P_SELL_AREA,
		GRID_ID
		FROM (SELECT SUBSTR(SYS_CONNECT_BY_PATH(ID,
		'-'),
		2) || '-' PATH,
		T.ID GRID_ID
		FROM PURE.org_organization_mod T
		CONNECT BY T.PARENT_ID = PRIOR
		T.ID
		START WITH CASE
		WHEN EXISTS
		(SELECT *
		FROM PURE.org_organization_mod T1
		WHERE T1.PARENT_ID =
		'$sellArea$') THEN
		T.PARENT_ID
		ELSE
		T.ID
		END = '$sellArea$')
		UNION ALL
		SELECT
		'$sellArea$' P_SELL_AREA, '$sellArea$' GRID_ID
		FROM DUAL) yx,

		<isNotEqual property="channelType" compareValue="root">
			(
			<include refid="selectItem_channelType" />
			) qd,
		</isNotEqual>

		PURE.org_organization_mod b
		where t.new_grid_id(+) = yx.grid_id
		<isNotEqual property="channelType" compareValue="root">
			and t.chnl_type = qd.channel_type
		</isNotEqual>

		and yx.p_sell_area = b.id
		and to_date(to_char(t.visit_date,'yyyy-mm-dd'),'yyyy-mm-dd' )
		= to_date(to_char(sysdate-1, 'yyyy-mm-dd'), 'yyyy-mm-dd')
	</select>

	<select id="xundian_select_open1" resultClass="java.util.HashMap">
		SELECT /*+ordered*/ visit_id VID,
		new_grid_name V0,
		t.chnl_id V1,
		chnl_name V2,
		gm_name V3 ,
		nvl(score,0) V4,
		start_date V5,
		end_date V6,
		TO_CHAR(VISIT_DATE, 'mm-dd hh24:mi') V7,
		s.longitude||','||s.latitude xt,
		t.location_desc xd,
		location_size pc,
		case when visit_stutas = '1' and to_char(VISIT_DATE, 'yyyymmdd') &gt; end_date then 1 end V8,
		case when visit_stutas = '1' and to_char(VISIT_DATE, 'yyyymmdd') &gt; end_date then '否' else '是' end V8_1,
		case when visit_stutas = '1' and adv_door in ( 'B','D','F') then 1 end T1,
		case when visit_stutas = '1' and adv_paste = 'C' then 1 end T2,
		case when visit_stutas = '1' and adv_air = 'C' then 1 end T3,
		case when visit_stutas = '1' and adv_know = 'C' then 1 end T4,
		case when visit_stutas = '1' and sys_op = 'C' then 1 end T5,
		case when visit_stutas = '1' and sale_minded = 'C' then 1 end T6,
		case when visit_stutas = '1' and card_store = 'A' then 1 end T7,
		case when visit_stutas = '1' and adv_content = 'B' then 1 end T8,
		case when visit_stutas = '1' and location_size &gt; 100 then 1 end T9
		FROM (select * from g_ga.DM_VISIT_INFO_D t WHERE 1=1
		<include refid="XunDian_query_parameters" />
		) T,
		(SELECT SUBSTR(PATH,
		1,
		INSTR(PATH, '-') - 1) P_SELL_AREA,
		GRID_ID
		FROM (SELECT SUBSTR(SYS_CONNECT_BY_PATH(ID,
		'-'),
		2) || '-' PATH,
		T.ID GRID_ID
		FROM PURE.org_organization_mod T
		CONNECT BY T.PARENT_ID = PRIOR
		T.ID
		START WITH CASE
		WHEN EXISTS
		(SELECT *
		FROM PURE.org_organization_mod T1
		WHERE T1.PARENT_ID =
		'$sellArea$') THEN
		T.PARENT_ID
		ELSE
		T.ID
		END = '$sellArea$')
		UNION 
		SELECT
		'$sellArea$' P_SELL_AREA, '$sellArea$' GRID_ID
		FROM DUAL) yx,

		<isNotEqual property="channelType" compareValue="root">
			(
			<include refid="selectItem_channelType" />
			) qd,
		</isNotEqual>

		PURE.org_organization_mod b, g_ga.dm_chnl_gi_info s
		where t.new_grid_id(+) = yx.grid_id

		<isNotEqual property="channelType" compareValue="root">
			and t.chnl_type = qd.channel_type
		</isNotEqual>

		and yx.p_sell_area = b.id
		and t.chnl_id = s.chnl_id(+)
		and visit_stutas = '1'

		<include refid="XunDian_query_open1_parameters" />
		<dynamic prepend=" order by ">
			<isNotNull property="sortCol">
				$sortCol$
				<isNotNull property="sortOrder">$sortOrder$</isNotNull>
			</isNotNull>
		</dynamic>
	</select>

	<select id="sumList" resultClass="java.util.HashMap">
		SELECT /*+ordered*/t.GM_NAME V0,t.GM_ID V1
		FROM (SELECT /*+ordered*/*
		FROM g_ga.dm_visit_info_d T
		<isNotEqual property="channelType" compareValue="root">
			,(
			<include refid="selectItem_channelType" />
			) C
		</isNotEqual>
		WHERE 1=1
		<isNotEqual property="channelType" compareValue="root">
			and T.chnl_type = C.CHANNEL_TYPE
		</isNotEqual>

		and t.location_desc is not null
		AND T.visit_date like to_date('$dateId$','yyyy-mm-dd')
		<isNotNull property="channelNet">
			AND T.chnl_name like '%$channelNet$%'
		</isNotNull>
		<isNotNull property="gmId">
			AND T.GM_ID = '$gmId$'
		</isNotNull>
		and t.visit_stutas = '1') T,
		(SELECT /*+ordered*/SUBSTR(PATH, 1, INSTR(PATH, '-') - 1) P_GRID_ID,
		GRID_ID
		FROM (SELECT /*+ordered*/SUBSTR(SYS_CONNECT_BY_PATH(ID, '-'), 2) || '-' PATH,
		T.ID GRID_ID
		FROM PURE.org_organization_mod T
		CONNECT BY T.PARENT_ID = PRIOR T.ID
		START WITH T.PARENT_ID = '$gridId$')
		UNION ALL
		SELECT /*+ordered*/'$gridId$' P_GRID_ID, '$gridId$' GRID_ID FROM DUAL) S,
		PURE.org_organization_mod P
		WHERE T.NEW_GRID_ID = S.GRID_ID
		AND S.P_GRID_ID = P.ID
		group by GM_NAME,GM_ID
	</select>

	<select id="mapdateList" resultClass="java.util.HashMap">
		SELECT /*+ordered*/ t.GM_NAME V0,t.GM_ID V1
		FROM (SELECT /*+ordered*/*
		FROM g_ga.dm_visit_info_d T
		<isNotEqual property="channelType" compareValue="root">
			,(
			<include refid="selectItem_channelType" />
			) C
		</isNotEqual>
		WHERE 1=1
		<isNotEqual property="channelType" compareValue="root">
			and T.chnl_type = C.CHANNEL_TYPE
		</isNotEqual>

		and t.location_desc is not null
		<isNotNull property="dateBeginId">
			AND to_char(T.visit_date, 'yyyymmdd') <![CDATA[  >= ]]>
			'$dateBeginId$'
		</isNotNull>
		<isNotNull property="dateEndId">
			AND to_char(T.visit_date, 'yyyymmdd') <![CDATA[  <= ]]>
			'$dateEndId$'
		</isNotNull>

		<isNotNull property="channelNet">
			AND T.chnl_name like '%$channelNet$%'
		</isNotNull>
		<isNotNull property="gmId">
			AND T.GM_ID = '$gmId$'
		</isNotNull>
		and t.visit_stutas = '1') T,
		(SELECT /*+ordered*/SUBSTR(PATH, 1, INSTR(PATH, '-') - 1) P_GRID_ID,
		GRID_ID
		FROM (SELECT /*+ordered*/SUBSTR(SYS_CONNECT_BY_PATH(ID, '-'), 2) || '-' PATH,
		T.ID GRID_ID
		FROM PURE.org_organization_mod T
		CONNECT BY T.PARENT_ID = PRIOR T.ID
		START WITH T.PARENT_ID = '$gridId$')
		UNION ALL
		SELECT /*+ordered*/'$gridId$' P_GRID_ID, '$gridId$' GRID_ID FROM DUAL) S,
		PURE.org_organization_mod P
		WHERE T.NEW_GRID_ID = S.GRID_ID
		AND S.P_GRID_ID = P.ID
		group by GM_NAME,GM_ID
	</select>

	<select id="visitList" resultClass="java.util.HashMap">
		SELECT /*+ordered*/K.*, ROWNUM V4
		FROM (SELECT /*+ordered*/M.V0, M.V1, M.V2 ,M.V3,M.V5,M.V6,M.V7,M.V8
		from (SELECT /*+ordered*/t.chnl_name V0, t.chnl_id V1, t.location_desc V2,TO_CHAR(t.visit_date,'mm-dd hh24:mi') V3,
		case
		when t.adv_door = 'A' then
		'联通门头良好'
		when t.adv_door = 'B' then
		'联通门头已受损'
		when t.adv_door = 'C' then
		'沃门头良好'
		when t.adv_door = 'D' then
		'沃门头已受损'
		when t.adv_door = 'E' then
		'联合门头良好'
		when t.adv_door = 'F' then
		'联合门头已受损'
		when t.adv_door = 'G' then
		'非联通门头良好'
		when t.adv_door = 'H' then
		'非联通门头已受损'
		end V5,
		t.score V6,
		t.visit_id V7,
		t.acct_month V8
		FROM g_ga.dm_visit_info_d T
		where t.location_desc is not null
		<isNotNull property="gmId">
			and T.GM_ID = '$gmId$'
		</isNotNull>
		and t.visit_stutas = '1'
		AND T.visit_date like to_date('$dateId$','yyyy-mm-dd')
		group by t.chnl_name, t.chnl_id, t.location_desc,t.visit_date,t.adv_door,t.score,t.visit_id,t.acct_month) m
		order by M.V3) K
	</select>

	<select id="visitDateList" resultClass="java.util.HashMap">
		SELECT /*+ordered*/K.*, ROWNUM V4
		FROM (SELECT /*+ordered*/M.V0, M.V1, M.V2 ,M.V3,M.V5,M.V6,M.V7,M.V8
		from (SELECT /*+ordered*/t.chnl_name V0, t.chnl_id V1, t.location_desc V2,TO_CHAR(t.visit_date,'mm-dd hh24:mi') V3,
		case
		when t.adv_door = 'A' then
		'联通门头良好'
		when t.adv_door = 'B' then
		'联通门头已受损'
		when t.adv_door = 'C' then
		'沃门头良好'
		when t.adv_door = 'D' then
		'沃门头已受损'
		when t.adv_door = 'E' then
		'联合门头良好'
		when t.adv_door = 'F' then
		'联合门头已受损'
		when t.adv_door = 'G' then
		'非联通门头良好'
		when t.adv_door = 'H' then
		'非联通门头已受损'
		end V5,
		t.score V6,
		t.visit_id V7,
		t.acct_month V8
		FROM g_ga.dm_visit_info_d T
		where t.location_desc is not null
		<isNotNull property="gmId">
			and T.GM_ID = '$gmId$'
		</isNotNull>
		and t.visit_stutas = '1'
		<isNotNull property="dateBeginId">
			AND to_char(T.visit_date, 'yyyymmdd') <![CDATA[  >= ]]>
			'$dateBeginId$'
		</isNotNull>
		<isNotNull property="dateEndId">
			AND to_char(T.visit_date, 'yyyymmdd') <![CDATA[  <= ]]>
			'$dateEndId$'
		</isNotNull>
		group by t.chnl_name, t.chnl_id, t.location_desc, t.visit_date,t.adv_door,t.score,t.visit_id,t.acct_month) m
		order by M.V3) K
	</select>

	<select id="getXundianDetail" resultClass="java.util.HashMap">
		SELECT /*+ordered*/h.longitude x, h.latitude y,T.* from g_ga.DM_VISIT_INFO_D T, g_ga.dm_chnl_gi_info H
		Where t.acct_month='$monthId$' and t.VISIT_ID ='$visitId$'
		<isNotNull property="chiefId">
			<isNotEqual property="chiefId" compareValue="-1">
				and t.CHNL_CHIEF_id='$chiefId$'
			</isNotEqual>
		</isNotNull>
		AND H.chnl_id(+) = T.chnl_id
	</select>

	<select id="channelInfo" resultClass="java.util.HashMap">
		select /*+ordered*/ t.channel_id as "channel_id",
		d.channel_no_desc as "channel_no_desc",
		t.channel_type as "channel_type",
		b.channel_type_name as "channel_type_name",
		t.channel_level as "channel_level",
		c.channel_lvl_desc as "channel_lvl_desc",
		t.mark_area as "mark_area",
		t.zone_id as "zone_id",
		a.sell_area_desc as "department_desc",
		regist_money as "regist_money",
		t.link_man as "link_man",
		t.link_phone as "link_phone",
		channel_address as "channel_address",
		case when length(channel_address)>11 then substr(channel_address,0,11)|| '...' else channel_address end as "channel_address1",
		bank as "bank",
		case when length(bank)>11 then substr(bank,0,11)|| '...' else bank end as "bank1",
		account_name as "account_name",
		account_number as "account_number",
		margin as "margin",
		mortgage as "mortgage",
		contract_name as "contract_name",
		case when length(contract_name)>11 then substr(contract_name,0,11)|| '...' else contract_name end as "contract_name1",
		contract_no as "contract_no",
		contract_status as "contract_status",
		contract_kind as "contract_kind",
		SUBSTR(t.create_date, 1, 4) || '年' || SUBSTR(t.create_date, 5, 2) || '月' ||
		SUBSTR(t.create_date, 7, 2) || '日' as "create_date",
		SUBSTR(effect_date, 1, 4) || '年' || SUBSTR(effect_date, 5, 2) || '月' ||
		SUBSTR(effect_date, 7, 2) || '日' as "effect_date",
		SUBSTR(invalid_date, 1, 4) || '年' || SUBSTR(invalid_date, 5, 2) || '月' ||
		SUBSTR(invalid_date, 7, 2) || '日' as "invalid_date",
		e.description as "area_desc",
		f.description as "city_desc",
		t.chief_man as "chief_man",
		g.life_cycle_desc as "life_cycle_desc",
		h.sell_area_desc as "zone_desc",
		c.channel_lvl_desc as "channel_lvl_desc",
		SUBSTR(operate_date , 1, 4) || '年' || SUBSTR(operate_date , 5, 2) || '月' ||
		SUBSTR(operate_date , 7, 2) || '日' as "operate_date ",
		d.agent_desc as "agent_desc",
		d.agent_id as "agent_id"
		from dm.DM_CHNL_V_CHANNEL_INFO_D t,
		dss.code_sell_area a,
		dss.code_channel_type b,
		dmcode.dmcode_channel_lvl c,
		dmcode.dmcode_channel d,
		dmcode.dmcode_area e,
		dmcode.dmcode_city f,
		dmcode.dmcode_channel_life_cycle g,
		dss.code_sell_area h
		where t.channel_id = '$channelId$'
		and t.mark_area = a.sell_area(+)
		and t.channel_type = b.channel_type(+)
		and t.channel_level = c.channel_lvl(+)
		and t.channel_id = d.channel_no(+)
		and t.area_no = e.area_no(+)
		and t.city_no = f.city_no(+)
		and t.life_cycle = g.life_cycle(+)
		and t.zone_id=h.sell_area(+)
	</select>


	<select id="xundian_select_open2" resultClass="java.util.HashMap">
		SELECT /*+ordered*/ new_grid_name V0,
		chnl_id V1,
		chnl_name V2,
		gm_name V3,
		count(visit_id) V4,
		nvl(round(sum(score)/count(visit_id),2),0) V5
		FROM (select * from g_ga.DM_VISIT_INFO_D t WHERE 1=1
		<include refid="XunDian_query_parameters" />
		) T,
		(SELECT SUBSTR(PATH,
		1,
		INSTR(PATH, '-') - 1) P_SELL_AREA,
		GRID_ID
		FROM (SELECT SUBSTR(SYS_CONNECT_BY_PATH(ID,
		'-'),
		2) || '-' PATH,
		T.ID GRID_ID
		FROM PURE.org_organization_mod T
		CONNECT BY T.PARENT_ID = PRIOR
		T.ID
		START WITH CASE
		WHEN EXISTS
		(SELECT *
		FROM PURE.org_organization_mod T1
		WHERE T1.PARENT_ID =
		'$sellArea$') THEN
		T.PARENT_ID
		ELSE
		T.ID
		END = '$sellArea$')
		UNION ALL
		SELECT
		'$sellArea$' P_SELL_AREA, '$sellArea$' GRID_ID
		FROM DUAL) yx,

		<isNotEqual property="channelType" compareValue="root">
			(
			<include refid="selectItem_channelType" />
			) qd,
		</isNotEqual>

		PURE.org_organization_mod b
		where t.new_grid_id(+) = yx.grid_id

		<isNotEqual property="channelType" compareValue="root">
			and t.chnl_type = qd.channel_type
		</isNotEqual>

		and yx.p_sell_area = b.id
		and visit_stutas = '1'

		<include refid="XunDian_query_open1_parameters" />
		group by chnl_id,city_desc,chnl_name,gm_name,new_grid_name
		<dynamic prepend=" order by ">
			<isNotNull property="sortCol">
				$sortCol$
				<isNotNull property="sortOrder">$sortOrder$</isNotNull>
			</isNotNull>
		</dynamic>
	</select>
</sqlMap>
