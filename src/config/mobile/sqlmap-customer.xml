<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="customer">
<!-- 标准地址查询 -->
<select id="getStandardAddress"  resultClass="java.util.HashMap"> 	   
      select * from (  SELECT STANDARD_ADDRESS_ID6  "address_id",
             SITUATED  "address"
      FROM   G_GA.DM_ADDRESS_DETAIL_DAY 
      WHERE  SITUATED like '%$address$%') where rownum <![CDATA[< ]]> 21
</select>
<!-- 端口资源查询 -->
<select id="getResourcesByAddress"  resultClass="java.util.HashMap"> 	   
        select * from (     SELECT FTTH_PORT_SUMNUM   "ftth_total",
             FTTH_PORT_EMPNUM   "ftth_remain",
             AD_PORT_SUMNUM     "adsl_total", 
             AD_PORT_EMPNUM     "adsl_remain",
             FTTH_PORT_SUMNUM + AD_PORT_SUMNUM   "total_count",
             FTTH_PORT_EMPNUM + AD_PORT_EMPNUM    "remain_count"
      FROM g_ga.DM_addr_resource_detail_w
      WHERE STANDARD_ADDRESS_ID6='$address_id$'
      order by acct_month desc,day_id  desc) where rownum <![CDATA[< ]]> 2
</select>
<!-- 异网信息查询 -->
<select id="getYWInfo"  resultClass="java.util.HashMap"> 	   
         SELECT  cust_name    "cust_name",
                 tele_type    "tele_type", 
                 sp_info      "sp_info", 
                 dinner       "dinner", 
                 stop_date    "stop_date",
                 start_date   "start_date",
                 cust_id     "cust_id",
                 floor(((stop_date-sysdate)/30)+1)  "month" ,
                 remarks   "remarks",
				 id      "id"
      FROM jike.inf_diffnet
      WHERE 1=1
      and  staff_id='$loginId$'
      and to_char(remind_time,'yyyyMMdd') >= to_char(sysdate,'yyyyMMdd')
      <isNotEmpty  property='keyword'>
           and CUST_NAME like  '%$keyword$%'
      </isNotEmpty>
      order by remind_time desc
</select>

<!-- 异网信息列表展示 -->
<select id="getYWList"  resultClass="java.util.HashMap"> 	   
          select tele_type    "tele_type", 
                 stop_date    "stop_date",
                 count(stop_date)  "num" 
      FROM jike.inf_diffnet
      WHERE 1=1
      group  by tele_type,stop_date
</select>

<select id="getYWInfoNo"  resultClass="java.lang.String"> 	   
            SELECT count(id) 
      FROM jike.inf_diffnet
      WHERE 1=1
      and to_char(REMIND_TIME,'yyyy-MM-dd') >= to_char(sysdate,'yyyy-MM-dd')
      and <![CDATA[
      to_char(REMIND_TIME,'yyyy-MM-dd') <= to_char(last_day(sysdate),'yyyy-MM-dd')]]>
      and  staff_id='$loginId$'
</select>
<!-- 异网信息新增 -->
<insert  id="createYWInfo">
<selectKey keyProperty="different_seq" resultClass="string" type="pre">
			select jike.different_seq.nextval as value from dual
</selectKey>
insert into  jike.inf_diffnet (
                              start_date,
                              stop_date,  
							  cust_name, 
							  create_time, 
							  tele_type, 
						      staff_id, 
						      dinner, 
							  sp_info, 
							  remind_time, 
							  in_net, 
							  latlon,
							  cust_id,
							  remarks,
							  id)
                       values(
                                 <isNotEmpty  property='start_date'>
                                  to_date('$start_date$','yyyy-MM-dd'),
                                </isNotEmpty>
                                <isEmpty  property='start_date'>
						          '',
						        </isEmpty>
						         <isNotEmpty  property='stop_date'>
                                  to_date('$stop_date$','yyyy-MM-dd'),
                                </isNotEmpty>
                                <isEmpty  property='stop_date'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='cust_name'>
                                  '$cust_name$',
                                </isNotEmpty>
                                <isEmpty  property='cust_name'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='create_time'>
                                  to_date('$create_time$','yyyy-MM-dd'),
                                </isNotEmpty>
                                <isEmpty  property='create_time'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='tele_type'>
                                  '$tele_type$',
                                </isNotEmpty>
                                <isEmpty  property='tele_type'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='staff_id'>
                                  '$staff_id$',
                                </isNotEmpty>
                                <isEmpty  property='staff_id'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='dinner'>
                                  '$dinner$',
                                </isNotEmpty>
                                <isEmpty  property='dinner'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='sp_info'>
                                  '$sp_info$',
                                </isNotEmpty>
                                <isEmpty  property='sp_info'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='remind_time'>
                                  to_date('$remind_time$','yyyy-MM-dd'),
                                </isNotEmpty>
                                <isEmpty  property='remind_time'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='in_net'>
                                  '$in_net$',
                                </isNotEmpty>
                                <isEmpty  property='in_net'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='latlon'>
                                  '$latlon$',
                                </isNotEmpty>
                                <isEmpty  property='latlon'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='cust_id'>
                                  '$cust_id$',
                                </isNotEmpty>
                                <isEmpty  property='cust_id'>
						          '',
						        </isEmpty>
						        <isNotEmpty  property='remarks'>
                                  '$remarks$',
                                </isNotEmpty>
                                <isEmpty  property='remarks'>
						          '',
						        </isEmpty>
						        '$different_seq$'
                              )
</insert>
<!--异网信息修改-->
<update  id="editYWInfo">
update  jike.inf_diffnet  set  
						     <isNotEmpty  property='cust_name'>
							  cust_name = '$cust_name$', 
							  </isNotEmpty>
							  <isNotEmpty  property='create_time'>
							  create_time = to_date('$create_time$','yyyy-MM-dd'), 
							  </isNotEmpty>
							  <isNotEmpty  property='tele_type'>
							  tele_type = '$tele_type$', 
							  </isNotEmpty>
							  <isNotEmpty  property='staff_id'>
						      staff_id = '$staff_id$', 
						      </isNotEmpty>
						      <isNotEmpty  property='dinner'>
						       dinner = '$dinner$', 
						      </isNotEmpty>
						      <isNotEmpty  property='sp_info'>
							    sp_info = '$sp_info$', 
							  </isNotEmpty>
							  <isNotEmpty  property='remind_time'>
							    remind_time = to_date('$remind_time$','yyyy-MM-dd'), 
							  </isNotEmpty>
							  <isNotEmpty  property='in_net'>
							    in_net = '$in_net$', 
							  </isNotEmpty>
							  <isNotEmpty  property='latlon'>
							    latlon = '$latlon$',
							  </isNotEmpty>
							   <isNotEmpty  property='remarks'>
							    remarks = '$remarks$',
							  </isNotEmpty>
							  <isNotEmpty  property='cust_id'>
								cust_id = '$cust_id$',
						      </isNotEmpty>
						      <isNotEmpty  property='id'>
								id = '$id$'
						      </isNotEmpty>
  where   id = '$id$'						  
</update>
<!-- 组织机构查询 -->
<select id="queryOrganization"  resultClass="java.util.HashMap"> 
	select id  "dept_id",
	       name  "dept_name",
	       grade  "dept_level_id",
	       parent_id  "parent_dept_id"
	from (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL))      
</select>
<!-- 用户列表查询(维护经理) -->
<select id="queryForWHUserList"  resultClass="java.util.HashMap"> 
	select staff_name  "staff_name",
	       phone_no "phone_no",
	       email  "email"
	from  PURE_CONTACTS_WHJL
	where DEPT_ID = '$dept_id$'        
</select>
<!-- 用户列表查询(客户经理) -->
<select id="queryForKHUserList"  resultClass="java.util.HashMap"> 
	select staff_name  "staff_name",
	       phone_no  "phone_no",
	       email  "email"
	from  PURE_CONTACTS_KHJL
	where DEPT_ID = '$dept_id$'        
</select>
<!-- 用户查询(维护经理) -->
<select id="queryWHUserList"  resultClass="java.util.HashMap"> 
	select staff_name  "staff_name",
	       phone_no  "phone_no",
	       email  "email"
	from  PURE_CONTACTS_WHJL
    where staff_name like '%$staff_name$%'       
</select>
<!-- 用户查询(客户经理) -->
<select id="queryKHUserList"  resultClass="java.util.HashMap"> 
	select staff_name  "staff_name",
	       phone_no  "phone_no",
	       email  "email"
	from  PURE_CONTACTS_KHJL
	where staff_name like '%$staff_name$%'        
</select>

<!-- 名单制主客户查询1 名单制客户分析数据-->
<select id="queryanaList"  resultClass="java.util.HashMap"> 
	select  dev_gcust_num  "cust_dev_count", 
          dev_guser_num  "user_dev_count", 
          dev_in_listmon  "month_in_count", 
          dev_out_listmon  "month_out_count", 
          dev_owe_fee  "owe_fee", 
          mon_income_total  "income_month", 
          mon_income_comp   "income_month_tb", 
          mon_income_hc   "income_month_hb", 
          mon_allincome_avg  "income_month_avg"
  from  jike.DCD_M_CUST_ANALYSISDATA
	where 1=1
	<isNotEmpty property='id'>
	       and  m_cust_id = '$id$'  
	</isNotEmpty>   
</select>
<!-- 名单制主客户查询1 名单制客户业务数据-->
<select id="querybusiList"  resultClass="java.util.HashMap"> 
	select t1.SVC_DESC  "tele_type",
         t.user_num   "user_count"
	from  jike.DCD_MCUST_TELE_DAY t, DMCODE.G_DMCODE_SVC_TYPE t1
  where t.tele_type = t1.SVC_TYPE
	<isNotEmpty property='id'>
	and m_cust_id = '$id$'  
	</isNotEmpty>   
</select>
</sqlMap>
