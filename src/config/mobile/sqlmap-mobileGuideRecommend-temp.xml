<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="MobileGuideRecommend">

<!-- =============根据统计用到的语句2013.3.13============= -->
<!-- 判断手机是否3G 0是1不是-->
	<select id="is3GTele" resultClass="String">
			 select  /*+INDEX(t INDEX_HS_USER_INFO_DAY)*/distinct DECODE(T.IS_3G, '30', '2', '20', '1', '0') IS3G from vaas.Hs_User_Info_Day t where t.PHONE LIKE '%'||'$tel$' ||'%'
			  <isNotNull property="areaNo">
			   <isNotEqual property="areaNo" prepend="and"  compareValue="-1">
		   	   		t.area_no = '$areaNo$'
		   	   </isNotEqual>
	   	      </isNotNull> 
	</select>
    <!-- 修改操作员信息 -->
	<update id="updatePwd" >
		UPDATE vass.sc_login_user t
		   SET t.password = '$newpwd$',
		   t.init_pwd = '$newpwd$'
		 where t.staff_id = '$oprator$'
	</update>
	<update id="updatePwd2" > 
        UPDATE  vaas.AGENT_OPERATOR_INFO t 
           SET t.OPERRATOR_PASSWORD='$newpwd$'
         WHERE t.OPERRATOR_ID = (SELECT s.LOGIN_ID FROM vass.SC_LOGIN_USER s WHERE s.STAFF_ID='$oprator$')
	</update>
	<!-- 公告信息 -->
 	<select id="getNoticeInfoMap" resultClass="java.util.HashMap">
 		<![CDATA[
	    SELECT *
	     FROM (SELECT T.*,ROWNUM RO  FROM (SELECT T.NOTICE_TITLE TITLE,
	          T.NOTICE_CONTENT CON,
	          T.NOTICE_ID TID,
            CASE
              WHEN T.Create_Date >= add_months(sysdate,-1)
                 THEN 'Red'
              ELSE 'Black'
           END FONTCOLOR
	     FROM $VAAS$HS_SYSTEM_NOTICE T
	    WHERE T.EXPIRY_DATE >= (SELECT to_date(to_char(SYSDATE,'yyyy-MM-dd'),'yyyy-MM-dd')
	       FROM DUAL)
	     ORDER BY CREATE_DATE DESC) T)
	      WHERE RO<=6  	
	      ]]>
 	</select>
 	
 	<select id="selectStaffID" resultClass="java.util.HashMap">	
	SELECT DISTINCT T1.STAFF_ID,
                T1.DEPARTMENT,
                T2.CHANNEL_TYPE,
                     T3.CHANNEL_SUBPAR_TYPE
  FROM VASS.SC_LOGIN_USER T1, DIM.DIM_CHANNEL_NO T2, DIM.DIM_CHANNEL_TYPE T3
 WHERE T1.DEPARTMENT = T2.CHANNEL_NO(+)
   AND T2.CHANNEL_TYPE = T3.CHANNEL_TYPE(+)
   AND T1.LOGIN_ID = #oprator#
	</select>
 	<!-- 插入捕获日志 -->
 	<insert id="queryLog" parameterClass="java.util.HashMap" >
       insert into vaas.HS_LOG_QUERY_TEL_TEMP (LOGIN_ID,DEPT,LOG_DATE,AREA_NO,TEL_NO,TACTICS_ID,IFYES,IFZX) 
                                  values('$userId$','$dept$',sysdate,'$areaNo$','$tel$','$id$','$ifYes$','$opratorType$')
    </insert>
    <!-- 获得服务日志信息 -->
    <select id="getUserServiceHistory"  resultClass="java.util.HashMap">
		select distinct m.SCENE_TYPE_ID,m.scene_type_name,
       n.SCENE_NAME,
       nvl(a.TIME, t.operate_time) TIME,
       a.SERVICENAME,
       nvl(a.LOGINNAME, operator_id) LOGINNAME
  from (select t.operator_id,
                        t.phone_no,
                        t.user_id,
                        t.scene_id,
                        max(t.operate_time) operate_time,
                        t.scene_type_id
          from VAAS.HS_INFO_USER_SEVER_RECORD_TEMP t
         where t.isyes = '1'
           and (t.phone_no = '$tel$' or t.user_id = '$userId$'
           <isNotNull property="shareNumber">
              <isNotEqual property="shareNumber" compareValue="">
                or t.PHONE_NO = '$shareNumber$'
              </isNotEqual>
           </isNotNull>
           
           )
           group by t.operator_id,
                  t.phone_no,
                  t.user_id,
                  t.scene_id,
                  t.scene_type_id
        ) t,
       (SELECT *
           from (select distinct SERVICENAME,
               TO_CHAR(TIME, 'YYYY-MM-DD HH24:mi') TIME,
               LOGINNAME,
               USER_ID,
               PHONE_NO,
               REPLY_MSG,
               SCENE_TYPE_ID
          FROM VAAS.HS_LOG_USER_SERVICE_HISTORY_T T
         WHERE T.USER_ID = '$userId$'
         union all 
         select distinct SERVICENAME,
               TO_CHAR(TIME, 'YYYY-MM-DD HH24:mi') TIME,
               LOGINNAME,
               USER_ID,
               PHONE_NO,
               REPLY_MSG,
               SCENE_TYPE_ID
          FROM VAAS.HS_LOG_USER_SERVICE_HISTORY_T T
            where T.PHONE_NO = '$tel$'
            <isNotNull property="shareNumber">
              <isNotEqual property="shareNumber" compareValue="">
              union all 
              select distinct SERVICENAME,
               TO_CHAR(TIME, 'YYYY-MM-DD HH24:mi') TIME,
               LOGINNAME,
               USER_ID,
               PHONE_NO,
               REPLY_MSG,
               SCENE_TYPE_ID
          	FROM VAAS.HS_LOG_USER_SERVICE_HISTORY_T T
                where T.PHONE_NO = '$shareNumber$'
              </isNotEqual>
           </isNotNull>)
       ) A,
       vaas.hs_code_plantype m,
       (select *
          from vaas.hs_info_weight_analysis
        union all
        
        select * from vaas.hs_info_weight_analysis_cnc
        
        ) n
 where t.phone_no = a.phone_no(+)
   and t.scene_type_id = a.SCENE_TYPE_ID(+)
   and t.scene_id = a.REPLY_MSG(+)
   and t.operator_id = a.LOGINNAME(+)
   and t.scene_type_id = m.scene_type_id
   and t.scene_id = n.SCENE_ID

	</select>
	<!-- 查询流量包对应的策略类型 -->
	<select id="queryPolicyType" resultClass="java.lang.String">
		 select  /*+INDEX (T INDEX_PRODUCT_INFO)*/t.scene_type_id from vaas.HS_INFO_PRODUCT_USER_INFO t 
		 where t.product_id = '$product_id$'
		   AND ROWNUM &lt; 2
	</select>
	<!-- 插入服务日志信息 -->
	<insert id="insertUserServiceHistory">
	   insert into VAAS.HS_LOG_USER_SERVICE_HISTORY_T (SERVICENAME,TIME,LOGINNAME,USER_ID,PHONE_NO,REPLY_MSG,SCENE_TYPE_ID,IFZX) 
	      VALUES
	   ('$recommendMessage$',sysdate,'$oprator$','$userId$','$tel$','$guideIds$','$SCENE_TYPE_ID$','$opratorType$')
	</insert>
	<!-- 插入服务日志信息 -->
	<insert id="insertUserServiceHistoryLLB">
	   insert into VAAS.HS_LOG_USER_SERVICE_HISTORY_T (SERVICENAME,TIME,LOGINNAME,USER_ID,PHONE_NO,REPLY_MSG,SCENE_TYPE_ID,IFZX) 
	      VALUES
	   ('$PRODUCT_NAME$',sysdate,'$oprator$','$userId$','$tel$','$SCENE_ID$','$SCENE_TYPE_ID$','$opratorType$')
	</insert>
	<!-- 通过用户号码查询初始化用户ID -->
	<select id="queryUserId" resultClass="String"> 	   
      select /*+INDEX(t INDEX_HS_USER_INFO_DAY)*/distinct T.Userid USERID from vaas.Hs_User_Info_Day t where t.PHONE = '$tel$'
	</select>
	<select id="selectUserInfo" resultClass="java.util.HashMap">
	   select /*+INDEX(t INDEX_HS_USER_INFO_DAY)*/* from vaas.Hs_User_Info_Day t where t.PHONE = '$tel$'
	       <isNotNull property="areaNo">
				   <isNotEqual property="areaNo" prepend="and"  compareValue="-1">
			   	   		t.area_no = '$areaNo$'
			   	   </isNotEqual>
		   </isNotNull> 
	</select>
	<!-- 固网: 通过用户号码查询初始化用户ID -->
	<select id="queryNetUserId" resultClass="java.util.HashMap"> 	   
      select /*+INDEX(t INDEX_HS_USER_INFO_CNC_DAY)*/ distinct T.Userid USERID, T.SHARE_NUMBER SHARE_NUMBER from vaas.Hs_User_Info_CNC_Day t where t.PHONE = '$tel$'
	</select>
	<select id="selectNetUserInfo" resultClass="java.util.HashMap">
	   select /*+INDEX(t INDEX_HS_USER_INFO_CNC_DAY)*/* from vaas.Hs_User_Info_CNC_Day t where t.PHONE = '$tel$'
	       <isNotNull property="areaNo">
				   <isNotEqual property="areaNo" prepend="and"  compareValue="-1">
			   	   		t.area_no = '$areaNo$'
			   	   </isNotEqual>
		   </isNotNull> 
	</select>
	<!-- 用户基本信息 -->
	<select id="getUserInfor" resultClass="java.util.HashMap">
	SELECT *
    FROM (
    SELECT a.*,ct.scene_type_name
  	FROM (SELECT /*+INDEX(t INDEX_FLOW_COACH_USER_HS)*/
                 *
          FROM VAAS.HS_INFO_FLOW_COACH_USER_INFO T
          WHERE T.PHONE = '$tel$'
        UNION ALL
        SELECT /*+INDEX(t INDEX_FLOW_COACH_USER_RH)*/
         *
          FROM VAAS.HS_INFO_FLOW_RH_USER_INFO T
         WHERE T.PHONE = '$tel$') a, vaas.hs_code_plantype ct where a.scene_type_id = ct.scene_type_id order by ct.coach_level ) where rownum &lt;= 4 
 	ORDER BY FIRST_LEVEL, START_DATE DESC
	</select>
	<!-- 固网：用户基本信息 -->
	<select id="getNetUserInfor" resultClass="java.util.HashMap">
		select * from (
	select a.*,ct.scene_type_name from (
		    select  /*+INDEX(t INDEX_FLOW_CNC_USER_HS)*/* from vaas.HS_INFO_FLOW_CNC_USER_INFO t where t.PHONE = '$tel$'
		 		<isNotEmpty property="shareNumber">
		 		or (t.phone = '$shareNumber$' and t.scene_type_id &lt;&gt; '10')
		 		</isNotEmpty>
		 	union all
		 	select /*+INDEX(t INDEX_FLOW_COACH_USER_RH)*/ * from vaas.hs_info_flow_rh_user_info t where t.phone = '$tel$'
		 ) a, vaas.hs_code_plantype ct where a.scene_type_id = ct.scene_type_id order by ct.coach_level ) where rownum &lt;= 4
	   	 order by first_level, start_date desc
	</select>
	<!-- 页签信息 -->
	<select id="getPageTab" resultClass="java.util.HashMap">
		select  distinct scene_type_id from(
		 select  /*+INDEX(t INDEX_FLOW_COACH_USER_HS)*/t.scene_type_id,first_level from vaas.HS_INFO_FLOW_COACH_USER_INFO t where t.PHONE LIKE '%'||'$tel$' ||'%'
	   	  order by first_level, t.start_date desc)
	</select>
	<!-- 固网：页签信息 -->
	<select id="getNetPageTab" resultClass="java.util.HashMap">
		select  distinct scene_type_id from(
		 select  /*+INDEX(t INDEX_FLOW_CNC_USER_HS)*/t.scene_type_id,first_level from vaas.HS_INFO_FLOW_CNC_USER_INFO t 
		 where t.PHONE LIKE '%'||'$tel$' ||'%'
		 <isNotEmpty property="shareNumber">
		 	or t.phone = '$shareNumber$'
		 </isNotEmpty>
	   	  order by first_level, t.start_date desc)
	</select>
	<!-- 用户流量信息 -->
	<select id="getFlowInfor" resultClass="java.util.HashMap">
		SELECT T.PACKAGE, T.MONTH, T.MONTH_L, T.MONTH_LL, T.MONTH_LLL,T.END_DATE
		  FROM (SELECT '日期' PACKAGE,
		               T.GATHER_MON MONTH,
		               TO_CHAR(ADD_MONTHS(TO_DATE(T.GATHER_MON, 'yyyymm'), -1),
		                       'yyyymm') MONTH_L,
		               TO_CHAR(ADD_MONTHS(TO_DATE(T.GATHER_MON, 'yyyymm'), -2),
		                       'yyyymm') MONTH_LL,
		               TO_CHAR(ADD_MONTHS(TO_DATE(T.GATHER_MON, 'yyyymm'), -3),
		                       'yyyymm') MONTH_LLL,
		               999999 RANK,
		               '包到期时间' END_DATE
		          FROM VAAS.HS_INFO_USER_INFO_FLOW T
		         WHERE ROWNUM = 1
		        UNION ALL
		        SELECT /*+INDEX (T INDEX_HS_INFO_FLOW_PHONE)*/ T.PACKAGE,
		               T.MONTH,
		               T.MONTH_L,
		               T.MONTH_LL,
		               T.MONTH_LLL,
		               TO_NUMBER(T.RANK),
		               T.END_DATE
		          FROM VAAS.HS_INFO_USER_INFO_FLOW T
		         WHERE T.PHONE = '$tel$') T
		 ORDER BY T.RANK DESC
	</select>
	<!-- 用户短信信息 -->
	<select id="getSMSInfor" resultClass="java.util.HashMap">
	SELECT T.PACKAGE, T.MONTH, T.MONTH_L, T.MONTH_LL, T.MONTH_LLL, T.END_DATE
		  FROM (SELECT '日期' PACKAGE,
		               T.GATHER_MON MONTH,
		               TO_CHAR(ADD_MONTHS(TO_DATE(T.GATHER_MON, 'yyyymm'), -1),
		                       'yyyymm') MONTH_L,
		               TO_CHAR(ADD_MONTHS(TO_DATE(T.GATHER_MON, 'yyyymm'), -2),
		                       'yyyymm') MONTH_LL,
		               TO_CHAR(ADD_MONTHS(TO_DATE(T.GATHER_MON, 'yyyymm'), -3),
		                       'yyyymm') MONTH_LLL,
		               999999 RANK,
		               '包到期时间' END_DATE
		          FROM VAAS.HS_INFO_USER_INFO_SMS T
		         WHERE ROWNUM = 1
		        UNION ALL
		        SELECT /*+INDEX (T INDEX_HS_INFO_SMS_PHONE)*/ T.PACKAGE,
		               T.MONTH,
		               T.MONTH_L,
		               T.MONTH_LL,
		               T.MONTH_LLL,
		               TO_NUMBER(T.RANK),
		               T.END_DATE
		          FROM VAAS.HS_INFO_USER_INFO_SMS T
		         WHERE T.PHONE = '$tel$') T
		 ORDER BY T.RANK DESC
	</select>
	<!-- 用户彩信信息 -->
	<select id="getMMSInfor" resultClass="java.util.HashMap">
	SELECT T.PACKAGE, T.MONTH, T.MONTH_L, T.MONTH_LL, T.MONTH_LLL, T.END_DATE
		  FROM (SELECT '日期' PACKAGE,
		               T.GATHER_MON MONTH,
		               TO_CHAR(ADD_MONTHS(TO_DATE(T.GATHER_MON, 'yyyymm'), -1),
		                       'yyyymm') MONTH_L,
		               TO_CHAR(ADD_MONTHS(TO_DATE(T.GATHER_MON, 'yyyymm'), -2),
		                       'yyyymm') MONTH_LL,
		               TO_CHAR(ADD_MONTHS(TO_DATE(T.GATHER_MON, 'yyyymm'), -3),
		                       'yyyymm') MONTH_LLL,
		               999999 RANK,
		               '包到期时间' END_DATE
		          FROM VAAS.HS_INFO_USER_INFO_MMS T
		         WHERE ROWNUM = 1
		        UNION ALL
		        SELECT /*+INDEX (T INDEX_HS_INFO_MMS_PHONE)*/ T.PACKAGE,
		               T.MONTH,
		               T.MONTH_L,
		               T.MONTH_LL,
		               T.MONTH_LLL,
		               TO_NUMBER(T.RANK),
		               T.END_DATE
		          FROM VAAS.HS_INFO_USER_INFO_MMS T
		         WHERE T.PHONE = '$tel$') T
		 ORDER BY T.RANK DESC
	</select>
	<!-- 用户宽带信息 -->
	<select id="selectCNCXX" resultClass="java.util.HashMap">
	    SELECT T.PHONE,
		       to_char(T.START_TIME,'yyyy-mm-dd') START_TIME,
               to_char(T.END_TIME,'yyyy-mm-dd') END_TIME,
               TRUNC(T.END_TIME - SYSDATE) AS LASTTIME,
               case ACCESS_TYPE
                when '01' then 'ASDL'
                when '02'  then '光纤'
                else  '其他' end ACCESS_TYPE,
               DISCNT_NAME,
               ACCT_FEE,
               ZK_FEE,
               SCORE_VALUE
         FROM VAAS.HS_INFO_USER_INFO_CNC t
		 where 1 = 1
		 <isNotEmpty property="shareNumber">
		 	and t.phone = '$shareNumber$'
	 	 </isNotEmpty>
	 	 <isEmpty property="shareNumber">
		 	and t.phone = '$tel$'
	 	 </isEmpty>
	</select>
	<!-- 用户固网信息 -->
	<select id="selectGWXX" resultClass="java.util.HashMap">
	    SELECT SCORE_VALUE
         FROM VAAS.HS_INFO_USER_INFO_CNC t
		 where t.phone ='$tel$'
	</select>
	<!-- 用户合约信息 -->
	<select id="selectHYXX" resultClass="java.util.HashMap">
	    SELECT /*+INDEX (T HS_INFO_USER_INFO_HY_INDEX) */ T.PHONE, '合约到期项目' HY_NAME,
               '生效日期' START_TIME,
               '到期日期' END_TIME,
               '剩余天数' LASTTIME,
           to_char(ADD_MONTHS(to_date(T.V_MONTH, 'yyyymm'), -1), 'yyyymm') || ' 收入' TOTAL_FEE,
           to_char(ADD_MONTHS(to_date(T.V_MONTH, 'yyyymm'), -2), 'yyyymm') || ' 收入' TOTAL_FEE_L,
              to_char(ADD_MONTHS(to_date(T.V_MONTH, 'yyyymm'), -3), 'yyyymm') || ' 收入' TOTAL_FEE_LL
  FROM VAAS.HS_INFO_USER_INFO_HY T
 WHERE T.PHONE = '$tel$'
 UNION ALL 
      SELECT /*+INDEX (T HS_INFO_USER_INFO_HY_INDEX) */ T.PHONE,
           T.HY_NAME,
           to_char(T.START_TIME,'yyyy-mm-dd') START_TIME,
               to_char(T.END_TIME,'yyyy-mm-dd') END_TIME,
           to_char(TRUNC(T.END_TIME - SYSDATE)) AS LASTTIME,
		       to_char(T.TOTAL_FEE),
		       to_char(T.TOTAL_FEE_L),
		       to_char(T.TOTAL_FEE_LL)
		  FROM VAAS.HS_INFO_USER_INFO_HY  t 
		 where t.phone ='$tel$'
	</select>
	<select id="selectHYXXTITLE" resultClass="java.util.HashMap">
		SELECT /*+INDEX (T HS_INFO_USER_INFO_HY_INDEX) */'合约到期项目' NAME_TITLE,
       				'生效日期' START_TIME_TITLE,
       				'到期日期' END_TIME_TITLE,
       				'剩余天数' LASTTIME_TITLE,
		       to_char(ADD_MONTHS(to_date(T.V_MONTH, 'yyyymm'), -1), 'yyyymm') || ' 收入' FEE_TITLE,
		       to_char(ADD_MONTHS(to_date(T.V_MONTH, 'yyyymm'), -2), 'yyyymm') || ' 收入' FEE_L_TITLE,
       		   to_char(ADD_MONTHS(to_date(T.V_MONTH, 'yyyymm'), -3), 'yyyymm') || ' 收入' FEE_LL_TITLE
  FROM VAAS.HS_INFO_USER_INFO_HY T
 WHERE T.PHONE = '$tel$'
	</select>
	<!-- 用户积分信息 -->
	<select id="selectJFXX" resultClass="java.util.HashMap">
	    SELECT /*+INDEX (T INDEX_HS_INFO_SCORE_PHONE)*/
	           T.PHONE,
		       T.SCORE
		  FROM VAAS.HS_INFO_USER_INFO_SCORE  t 
		 where t.phone ='$tel$'
	</select>
	<select id="getRecommendUsageType" resultClass="java.util.HashMap">
		select * from vaas.recommend_usage_type where scene_type_id = '$value$'
	</select>
	<!-- 查询推荐软件 -->
	<select id="querySoftware" resultClass="java.util.HashMap">
         select *
		  from (select /*+INDEX (T INDEX_SOFT_INFO)*/
		         t.*,
		         row_number() over(partition by t.scene_id order by t.ord asc) myorder
		          from vaas.HS_INFO_SOFTWARE_USER_INFO t
		         where t.device_number = '$tel$') b
	</select>
	<select id="queryRecommendMessage" resultClass="java.lang.String">
         select recommend_message_a
		  from (select /*+INDEX (T INDEX_SOFT_INFO)*/
		         t.*,
		         row_number() over(partition by t.scene_id order by t.ord asc) myorder
		          from vaas.HS_INFO_SOFTWARE_USER_INFO t
		         where t.device_number = '$phoneno$'
		           AND t.SCENE_ID = '$policyId$') b
		         <![CDATA[
		         where b.myorder < 4
		         ]]>
	</select>
	<!-- 流量包产品 -->
	<select id="getLiuliangbaoList" resultClass="java.util.HashMap">
	    select  /*+INDEX (T INDEX_PRODUCT_INFO)*/* from vaas.HS_INFO_PRODUCT_USER_INFO t where t.device_number='$tel$'
	</select>
	<!-- 增值包产品 -->
	<select id="getSPList" resultClass="java.util.HashMap">
	    select  /*+INDEX (T INDEX_PRODUCT_INFO_TYPE)*/* from vaas.HS_INFO_PRODUCT_USER_INFO t where t.device_number='$tel$' and t.scene_type_id = '11'
	</select>
	<!-- 短信包产品 -->
	<select id="getDuanxinbaoList" resultClass="java.util.HashMap">
	    select  /*+INDEX (T INDEX_PRODUCT_INFO_TYPE)*/* from vaas.HS_INFO_PRODUCT_USER_INFO t where t.device_number='$tel$' and t.scene_type_id = '4'
	</select>
	<!-- 彩信包产品 -->
	<select id="getCaixinbaoList" resultClass="java.util.HashMap">
	    select  /*+INDEX (T INDEX_PRODUCT_INFO_TYPE)*/* from vaas.HS_INFO_PRODUCT_USER_INFO t where t.device_number='$tel$' and t.scene_type_id = '5'
	</select>
	<select id="selectSequence" resultClass="java.lang.Object">
		SELECT UI.SEQ_PUSH_PUSH_ID.NEXTVAL FROM DUAL
	</select>
	<!-- 查询PUSH网址 -->
	<select id="selectPushUrl" resultClass="java.lang.Object">
		SELECT T.PUSH_URL  FROM UI.UI_PUSH_URL T WHERE T.ID = '1'
	</select>
	<!-- 电话营销订单号 -->
	<select id="selectMsgId" resultClass="java.lang.String">
		SELECT VAAS.SQ_MSG_ID.NEXTVAL FROM DUAL
	</select>
	<!-- 查询短信ID -->
	<select id="selectDuanxin" resultClass="java.lang.String">
		SELECT VAAS.SEQ_GUIDE_LINK.NEXTVAL FROM DUAL
	</select>
	<select id="selectPhone" resultClass="java.lang.String">
		select nvl(a.mobile_phone,'-1') PHONE from vass.sc_login_user a where a.staff_id='$value$'
	</select>
	<insert id="insertRandomNum">
	    INSERT INTO VAAS.HS_SYSTEM_RESETPASSWORD
                 (ID, LOGINID, PHONE_NO, RANDOM_NUMBER, CREATEDATE, LOSTDATE)
             VALUES
                 ('$msgId$','$value$','$tel$','$randomNum$',sysdate,sysdate+5/1440)
	</insert>
	<delete id="deleteRandomNum">
	   DELETE FROM  VAAS.HS_SYSTEM_RESETPASSWORD WHERE LOGINID = '$value$'
	</delete>
	<select id="selectRandomIfLost" resultClass="java.lang.String">
	<![CDATA[
	      SELECT CASE
		         WHEN (SYSDATE - T.LOSTDATE) < 0 THEN
		          '1'
		         ELSE
		          '-1'
		       END D
		  FROM VAAS.HS_SYSTEM_RESETPASSWORD T
		 WHERE T.LOGINID = '$loginId$'
		   AND T.RANDOM_NUMBER = '$randomNum$'
	]]>
	</select>
	<update id="resetPassword">
	update vass.sc_login_user t 
		set t.password='123456',t.init_pwd='-1'
		where t.login_id='$loginId$' 
		and t.state='1'
	</update>
	<!-- 插入短信表 -->
	<insert id="insertOrsderMsg">
		INSERT INTO DSS.SMS_QUEUE VALUES('$msgId$',  '$tel$', '$trial_info$', SYSDATE)
	</insert>
	
	<!-- 插入短信表 -->
	<insert id="insertOrsderMsgOne">
		INSERT INTO DSS.SMS_QUEUE VALUES('$msgId$', '$tel$', '$trial_info$', SYSDATE)
	</insert>
	
	<!-- 应用链接发送日志 -->
	<insert id="addLinkSMSLog">
			INSERT INTO VAAS.SMS_SEND_LOG (sms_id,operator_id,phone_no,send_content,send_time,reply_msg,sys_flag) VALUES
			 ('$msgId$', '$oprator$', '$tel$', '$trial_info$', SYSDATE,'$guideIds$','0')
	</insert>
	<insert id="insertPush" >
		INSERT INTO UI.PUSH_TACTICS_INFO_REC
		  (PUSH_ID,PUSH_METHOD,PUSH_TYPE,PUSH_URL,PUSH_MONTH,PUSH_ADDRESS_MUST,ACTIVE_NAME,START_TIME,END_TIME,PUSH_MAIN_PAGE_URL)
		 	VALUES
		  ('$push_id$',
		   '$push_method$',
		   '3',
		   '$recommendMessage$', 
		    to_char(sysdate, 'YYYY-MM-DD'),
		   '$pushAddress$',
		   'flowTutor',
		   to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd'),
		   to_date(to_char(sysdate+90,'yyyy-mm-dd'),'yyyy-mm-dd'),
		   '$shortAddress$'
		   )
	</insert>
	<!-- 短信流量表里插入短信 -->
	<insert id="insertDXandLL">
	     INSERT INTO VAAS.HS_LOG_SMS_SEND_TUTOR(SMS_ID,OPERATOR_ID,PHONE_NO,SEND_CONTENT,REPLY_MSG,SCENE_TYPE_ID,DUANXINT,EDITDATE,APP_CODE,PUSH_ID,IFZX) VALUES
	       ('$msgId$','$oprator$','$tel$','$recommendMessage$','$guideIds$','1','$msgId$',sysdate,'$SOFTWARE_ID$','$push_id$','$opratorType$')
	</insert>
	<!-- 短信流量表里插入流量 -->
	<insert id="insertDX_LL">
	     INSERT INTO VAAS.HS_LOG_SMS_SEND_TUTOR(SMS_ID,OPERATOR_ID,PHONE_NO,SEND_CONTENT,REPLY_MSG,SCENE_TYPE_ID,DUANXINT,EDITDATE,IFZX) VALUES
	       ('$msgId$','$oprator$','$tel$','$PRODUCT_NAME$','$SCENE_ID$','2','$PRODUCT_ID$',sysdate,'$opratorType$')
	</insert>
	<insert id="addRecordServer"> 
	insert into VAAS.HS_INFO_USER_SEVER_RECORD_TEMP (OPERATOR_ID,PHONE_NO,USER_ID,SCENE_ID,OPERATE_TIME,ISYES,SCENE_TYPE_ID,IFZX,MOBILE_SALE)values
		        ('$oprator$',
		         '$tel$',
		         '$userId$',
		         '$SCENE_ID$',
                 TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'),
                '$isYes$',
                '$SCENE_TYPE_ID$','$opratorType$','1')		 
	</insert>
	<insert id="addRecordServerOther"> 
	insert into VAAS.HS_INFO_USER_SEVER_RECORD_TEMP (OPERATOR_ID,PHONE_NO,USER_ID,SCENE_ID,OPERATE_TIME,ISYES,SCENE_TYPE_ID,IFZX, NOTES,MOBILE_SALE)
	select '$oprator$',
		         m.device_number,
		         '$userId$',
		         '$SCENE_ID$',
                 TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'),
                '$isYes$',
                '$SCENE_TYPE_ID$','$opratorType$', '$tel$', '1' from vaas.hs_user_info_cnc_extend_day m where m.phone = '$tel$'
		        	 
	</insert>
	<select id="getRecordServer" resultClass="java.util.HashMap">
		select * from VAAS.HS_INFO_USER_SEVER_RECORD_TEMP where PHONE_NO = '$tel$' and SCENE_ID = '$SCENE_ID$' and to_char(to_date(OPERATE_TIME,'yyyy-MM-dd hh24:mi:ss'),'yyyyMMdd') = to_char(sysdate,'yyyyMMdd')
	</select>
	<insert id="addRepulseServer"> 
	insert into VAAS.HS_INFO_USER_SEVER_RECORD_TEMP (OPERATOR_ID,PHONE_NO,USER_ID,SCENE_ID,OPERATE_TIME,ISYES,SCENE_TYPE_ID,IFZX,REPULSE_REASION,MOBILE_SALE)values
		        ('$oprator$',
		         '$tel$',
		         '$userId$',
		         '$SCENE_ID$',
                 TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'),
                '$isYes$',
                '$SCENE_TYPE_ID$','$opratorType$','$repulseReasion$','1')		 
	</insert>
	<delete id="deleteRepulseServer">
	    delete from VAAS.HS_INFO_USER_SEVER_RECORD_TEMP where PHONE_NO = '$tel$' and ISYES = '0' and SCENE_TYPE_ID = '$SCENE_TYPE_ID$' and SCENE_ID = '$SCENE_ID$'
	</delete>
	<select id="selectWanChenCL" resultClass="java.util.HashMap">
		SELECT T.SCENE_ID,
       T.ISYES,
       T. PHONE_NO,
       T.OPERATE_TIME,
       CASE
         WHEN T.isyes = '0' and
              (sysdate - 10 >=
              to_date(T.OPERATE_TIME, 'YYYY-MM-DD HH24:MI:SS')) THEN
          'true'
         ELSE
          'false'
       END OPRATE,
       T.REPULSE_REASION
  FROM VAAS.HS_INFO_USER_SEVER_RECORD_TEMP T
 WHERE 
 						<isNotNull property="shareNumber">
	                       <isNotEqual property="shareNumber" compareValue="">
	                       (
	                       </isNotEqual>
	                    </isNotNull>
 T.PHONE_NO = '$tel$'
	                    <isNotNull property="shareNumber">
	                       <isNotEqual property="shareNumber" compareValue="">
	                         or T.PHONE_NO = '$shareNumber$')
	                       </isNotEqual>
	                    </isNotNull>
	                    
   and OPERATE_TIME = (select max(OPERATE_TIME)
                         from VAAS.HS_INFO_USER_SEVER_RECORD_TEMP T1
                        where T.SCENE_ID = T1.SCENE_ID
                          AND
                          <isNotNull property="shareNumber">
	                       <isNotEqual property="shareNumber" compareValue="">
	                       (
	                        </isNotEqual>
	                    </isNotNull>
                           T1.PHONE_NO = '$tel$'
                          <isNotNull property="shareNumber">
	                       <isNotEqual property="shareNumber" compareValue="">
	                         or T1.PHONE_NO = '$shareNumber$')
	                       </isNotEqual>
	                    </isNotNull>
                          )
    and (BSS_RESPONSE is null or BSS_RESPONSE &lt;&gt; '2')
	</select>
	<!-- 展示的公告信息 -->
 	<select id="getNoticeInfo" resultClass="java.util.HashMap">
 	   SELECT T.NOTICE_TITLE TITLE,
	          T.NOTICE_CONTENT CON,
	          T.NOTICE_ID TID,
	          to_char(T.CREATE_DATE,'yyyy-MM-dd') TIME
	     FROM $VAAS$HS_SYSTEM_NOTICE T
	    WHERE T.EXPIRY_DATE &gt;= 
	    	(SELECT to_date(to_char(SYSDATE,'yyyy-MM-dd'),'yyyy-MM-dd')FROM DUAL)
	      AND T.NOTICE_ID = '$noticeId$'
 	</select>
 	<!-- 更新用户订购信息 -->
	<select id="vloprator" resultClass="java.lang.String">
		select count(1) 
		  from vass.sc_login_user t
		 where t.staff_id = '$newOprator$'
		<!--  or t.pc_alias = '$newOprator$'  -->
	</select>
	<!-- 查询流量包信息 -->
	<select id="selectFlowPack" resultClass="java.util.HashMap">
	    SELECT /*+INDEX(T HS_INFO_USER_FLOW_PACK)*/t.discnt_name,to_char(t.start_date,'yyyy-MM-dd') start_date,to_char(t.end_date,'yyyy-MM-dd') end_date FROM VAAS.HS_INFO_USER_FLOW_PACK t where t.phone = '$tel$'
	</select>
	<!-- 查询当前工号用户信息 -->
	<select id="getCurrentUserInfoByStaffId" resultClass="java.util.HashMap">
		SELECT * FROM VASS.SC_LOGIN_USER T WHERE T.STAFF_ID = #essid#
	</select>
	<select id="selectFDJL" resultClass="java.util.HashMap">
	select x.phone_no,
       x.scene_name,
       x.operate_time,
       x.isyes,
       x.scene_type_name,
       replace(x.servicename,')','')  servicename
	from (select m.phone_no,
	             m.scene_name, 
	             m.operate_time, 
	             m.isyes,
	             m.scene_type_name,
	             replace(wmsys.wm_concat(n.servicename||')'),'),','；') servicename
	  from (select distinct t.phone_no,
	                        t.operator_id,
	                        t1.scene_id,
	                        t1.scene_name,
	                        t.operate_time,
	                        case
	                          when t.isyes = '1' then
	                           '接受'
	                          when t.isyes = '0' then
	                           '不接受'
	                        end as isyes,
                            case when t.scene_type_id = '1' then '流量辅导'
                                 when t.scene_type_id = '2' then '合约到期'
                                 when t.scene_type_id = '3' then '2-3G融合'
                                 when t.scene_type_id = '4' then '短信推荐'
                                 when t.scene_type_id = '5' then '彩信推荐'
                                 when t.scene_type_id = '6' then '客户积分'
                                 when t.scene_type_id = '7' then '宽带到期'
                                 when t.scene_type_id = '8' then '宽带包月'
                                 when t.scene_type_id = '9' then '宽带小余额'
                                 when t.scene_type_id = '10' then '固网积分'
                                 when t.scene_type_id = '11' then '增值业务'
                                 when t.scene_type_id = '12' then '融合策略'
                                 when t.scene_type_id = '13' then '固网产品推荐'
                                 when t.scene_type_id = '14' then '语音推荐'
                            end as scene_type_name
	          from (select operator_id,
	                       phone_no,
	                       user_id,
	                       scene_id,
	                       to_char(to_date(operate_time, 'yyyy-MM-dd hh24:mi:ss'),'yyyy-MM-dd hh24:mi') operate_time,
	                       isyes,
	                       scene_type_id
	                  from vaas.HS_INFO_USER_SEVER_RECORD_TEMP
	                 where operator_id = '$oprator$'
	                 <dynamic>
	                    <isNotNull property="beginDay">
	                       <isNotEqual property="beginDay" compareValue="">
	                         <![CDATA[
	                         and to_date(operate_time, 'yyyy-MM-dd hh24:mi:ss') >= to_date('$beginDay$', 'yyyy-MM-dd')
	                         ]]>
	                       </isNotEqual>
	                       <isNull property="endDay">
		                       <isEqual property="beginDay" compareValue="">
		                         <![CDATA[
		                         and to_date(operate_time, 'yyyy-MM-dd hh24:mi:ss') >= (sysdate - 7)
		                         ]]>  
		                       </isEqual>
	                       </isNull>
	                    </isNotNull>
	                    <isNull property="beginDay">
	                      <isNull property="endDay">
	                       <![CDATA[
	                         and to_date(operate_time, 'yyyy-MM-dd hh24:mi:ss') >= (sysdate - 7)
	                       ]]>
	                      </isNull> 
	                    </isNull>
	                    <isNotNull property="endDay">
	                       <isNotEqual property="endDay" compareValue="">
	                         <![CDATA[
	                         and to_date(operate_time, 'yyyy-MM-dd hh24:mi:ss') < (to_date('$endDay$', 'yyyy-MM-dd')+1)
	                         ]]>
	                       </isNotEqual>
	                    </isNotNull>
	                 </dynamic>
	                 ) t,
	               vaas.hs_info_weight_analysis t1
	         where t.scene_id = t1.scene_id(+)) m,
	           VAAS.HS_LOG_USER_SERVICE_HISTORY_T n
	        where m.phone_no = n.phone_no(+)
	          and m.operator_id = n.loginname(+)
	          and m.scene_id = n.reply_msg(+)    
	     group by m.phone_no, m.scene_name, m.operate_time, m.isyes,m.scene_type_name
	 order by m.operate_time desc) x
	</select>
	<select id="selectYFDSoftID" resultClass="java.util.HashMap">
	   select distinct
		       t.reply_msg,
		       t.scene_type_id,
		       t.reply_msg,
		       case when t.scene_type_id = '1' then t.app_code else t.duanxint end app_code
		  from vaas.HS_LOG_SMS_SEND_TUTOR t
		where t.phone_no = '$tel$'
	</select>
	<!-- 用户校验 -->
	<insert id="insertUserCheckRandomNum">
	    INSERT INTO VAAS.hs_system_userinfo_checked
                 (ID, LOGINID, PHONE_NO, RANDOM_NUMBER, CREATEDATE, LOSTDATE)
             VALUES
                 ('$msgId$','$value$','$tel$','$randomNum$',sysdate,sysdate+5/1440)
	</insert>
	<delete id="deleteUserCheckRandomNum">
	   DELETE FROM  VAAS.hs_system_userinfo_checked WHERE PHONE_NO = '$tel$' and LOGINID = '$value$'
	</delete>
	<select id="selectUserRandomIfLost" resultClass="java.lang.String">
	<![CDATA[
	      SELECT CASE
		         WHEN (SYSDATE - T.LOSTDATE) < 0 THEN
		          '1'
		         ELSE
		          '-1'
		       END D
		  FROM VAAS.hs_system_userinfo_checked T
		 WHERE T.LOGINID = '$oprator$'
		   AND T.RANDOM_NUMBER = '$randomNum$'
		   and t.PHONE_NO = '$tel$'
	]]>
	</select>
	<!-- 查找MTPush流量包 -->
	<select id="selectMTPush" resultClass="java.util.HashMap">
	   select /*+INDEX (T INDEX_HS_PRODUCT_ALL)*/ * from VAAS.HS_INFO_USER_PRODUCT_ALL WHERE DEVICE_NUMBER = '$tel$'
	</select>
	<select id="checkPopup" resultClass="java.util.HashMap">
		select * from (select a.*,ct.coach_level from 
		(
		 select  /*+INDEX(t INDEX_FLOW_COACH_USER_HS)*/ t.SCENE_TYPE_ID,t.SCENE_ID  from vaas.HS_INFO_FLOW_COACH_USER_INFO t where t.PHONE = '$tel$'
		 and not exists
		 (select 1 from vaas.HS_INFO_USER_SEVER_RECORD_TEMP m 
		 where t.phone = m.phone_no and t.scene_id = m.scene_id 
		 and ( (m.isyes = '1' and (m.bss_response is null or m.bss_response &lt;&gt; '2') ) or
		      (m.isyes = '0' and (sysdate - 10 &lt; to_date(operate_time,'YYYY-MM-DD HH24:MI:SS'))))
		 )	     
		 union all
		 select  /*+INDEX(t INDEX_FLOW_CNC_USER_HS)*/ t.SCENE_TYPE_ID,t.SCENE_ID  from vaas.HS_INFO_FLOW_CNC_USER_INFO t where
		 <isNotNull property="shareNumber">
            <isNotEqual property="shareNumber" compareValue="">
            (
             </isNotEqual>
         </isNotNull>
               t.PHONE = '$tel$'
              <isNotNull property="shareNumber">
            <isNotEqual property="shareNumber" compareValue="">
              or (t.PHONE = '$shareNumber$' and t.scene_type_id &lt;&gt; '10'))
            </isNotEqual>
         </isNotNull>

		 and not exists
		  (select 1 from vaas.HS_INFO_USER_SEVER_RECORD_TEMP m 
		 where t.phone = m.phone_no and t.scene_id = m.scene_id 
		 and ( (m.isyes = '1' and (m.bss_response is null or m.bss_response &lt;&gt; '2') )  or
		      (m.isyes = '0' and (sysdate - 10 &lt; to_date(operate_time,'YYYY-MM-DD HH24:MI:SS'))))
		 )	   
		 union all
		 select  /*+INDEX(t INDEX_FLOW_RH_USER_HS)*/ t.SCENE_TYPE_ID,t.SCENE_ID  from vaas.HS_INFO_FLOW_RH_USER_INFO t where
			<isNotNull property="shareNumber">
	            <isNotEqual property="shareNumber" compareValue="">
	            (
	             </isNotEqual>
	         </isNotNull>
               t.PHONE = '$tel$'
              <isNotNull property="shareNumber">
	            <isNotEqual property="shareNumber" compareValue="">
	              or (t.PHONE = '$shareNumber$' and t.scene_type_id &lt;&gt; '10'))
	            </isNotEqual>
	         </isNotNull>
		 and not exists
		  (select 1 from vaas.HS_INFO_USER_SEVER_RECORD_TEMP m 
		 where t.phone = m.phone_no and t.scene_id = m.scene_id 
		 and ( (m.isyes = '1' and (m.bss_response is null or m.bss_response &lt;&gt; '2') ) or
		      (m.isyes = '0' and (sysdate - 10 &lt; to_date(operate_time,'YYYY-MM-DD HH24:MI:SS'))))
		 )	   
		 ) a, vaas.hs_code_plantype ct
		 where not exists(select 1 from push.redlist_info where user_svn = '$tel$') 
		 and a.scene_type_id = ct.scene_type_id order by ct.coach_level ) where rownum &lt;= 4 
	</select>
	<select id="selectCncOtherInfor" resultClass="java.util.HashMap">
		SELECT /*+INDEX(t INDEX_HS_USER_CNC_EXTEND_DAY)*/  * FROM VAAS.HS_USER_INFO_CNC_EXTEND_DAY T WHERE T.PHONE = '$tel$'
	</select>
	<select id="importUploadRecord" resultClass="java.util.HashMap">
		select RECORD_NO,
		       IMPORT_TIME,
		       COUNT(*) total,
		       sum(case
		             when is_scene = '1' then
		              1
		             else
		              0
		           end) as cnt
		
		  from VAAS.VAAS_CLIENT_IMPORT_RECORD t
		  WHERE STAFF_ID = '$oprator$'
		 GROUP BY RECORD_NO, IMPORT_TIME
		 order by IMPORT_TIME DESC
	</select>
	<insert id="insertClientImportRecord">
	    INSERT INTO VAAS.VAAS_CLIENT_IMPORT_RECORD
                 (RECORD_NO,TEL,iS_SCENE, IMPORT_TIME,STAFF_ID)
             VALUES
                 ('$recordNo$','$tel$','$isScene$',to_date('$importTime$','YYYY-MM-DD HH24:MI:SS'),'$oprator$')
	</insert>
	<select id="checkChannelNo" resultClass="java.lang.Integer">
			select count(distinct T.PHONE) cnt from vaas.Hs_User_Info_Day T,VASS.Sc_Login_User U where U.Department = t.channel_no and T.Phone = '$phoneNo$'  and U.STAFF_ID = '$oprator$'
	</select>
	<delete id="deleteImportRecord">
		DELETE FROM VAAS.VAAS_CLIENT_IMPORT_RECORD T WHERE T.RECORD_NO = '$value$'
	</delete>
	<select id="getRecordSceneDetail" resultClass="java.util.HashMap">
		select T1.PHONE,
		       t3.scene_num || '-' || t1.rn CNT,
		       T1.CI_WEIGHT_DESC,
		       T2.Scene_Type_Name,isYes
		  from (select t1.*,case S.Isyes when '1' then '已推荐' else '未推荐' end isYes,
		               ROW_NUMBER() OVER(PARTITION BY T1.Phone ORDER BY T1.first_level, t1.start_date desc) RN
		          from (SELECT /*+INDEX(t INDEX_FLOW_COACH_USER_HS)*/
		                distinct T.Phone,
		                         T.Ci_Weight_Desc,
		                         T.Scene_Type_Id,T.scene_id,
		                         first_level,
		                         start_date
		                  FROM VAAS.HS_INFO_FLOW_COACH_USER_INFO T,
		                       VAAS.VAAS_CLIENT_IMPORT_RECORD    R
		                 WHERE T.PHONE = R.Tel
		                   and R.Record_No = '$recordNo$'
		    <isNotNull property="sceneType">
	            <isNotEqual property="sceneType" compareValue="-1">
	              and T.Scene_Type_Id = '$sceneType$'
	            </isNotEqual>
	         </isNotNull>
	         <isNotNull property="sceneName">
	            <isNotEqual property="sceneName" compareValue="">
	              and T.Ci_Weight_Desc like '%$sceneName$%'
	            </isNotEqual>
	         </isNotNull>
		                union all
		                select /*+INDEX(t INDEX_FLOW_CNC_USER_HS)*/
		                distinct T.Phone,
		                         T.Ci_Weight_Desc,
		                         T.Scene_Type_Id,T.scene_id,
		                         first_level,
		                         start_date
		                  from vaas.HS_INFO_FLOW_CNC_USER_INFO t,
		                       VAAS.VAAS_CLIENT_IMPORT_RECORD  R
		                 WHERE T.PHONE = R.Tel
		                   and R.Record_No = '$recordNo$'
		                   <isNotNull property="sceneType">
					            <isNotEqual property="sceneType" compareValue="-1">
					              and T.Scene_Type_Id = '$sceneType$'
					            </isNotEqual>
					         </isNotNull>
					         <isNotNull property="sceneName">
					            <isNotEqual property="sceneName" compareValue="">
					              and T.Ci_Weight_Desc like '%$sceneName$%'
					            </isNotEqual>
					         </isNotNull>
		                union all
		                select /*+INDEX(t INDEX_FLOW_COACH_USER_RH)*/
		                distinct T.Phone,
		                         T.Ci_Weight_Desc,
		                         T.Scene_Type_Id,T.scene_id,
		                         first_level,
		                         start_date
		                  from vaas.hs_info_flow_rh_user_info t,
		                       VAAS.VAAS_CLIENT_IMPORT_RECORD R
		                 WHERE T.PHONE = R.Tel
		                   and R.Record_No = '$recordNo$'
		                   <isNotNull property="sceneType">
				            <isNotEqual property="sceneType" compareValue="-1">
				              and T.Scene_Type_Id = '$sceneType$'
				            </isNotEqual>
				         </isNotNull>
				         <isNotNull property="sceneName">
				            <isNotEqual property="sceneName" compareValue="">
				              and T.Ci_Weight_Desc like '%$sceneName$%'
				            </isNotEqual>
				         </isNotNull>
		                   
		                   ) T1,vaas.HS_INFO_USER_SEVER_RECORD_TEMP S where T1.Scene_Id = S.Scene_Id(+)
                         and T1.Phone = S.Phone_No(+) ) t1,
		       
		       (select t3.phone, count(distinct scene_id) scene_num
		          from (SELECT /*+INDEX(t INDEX_FLOW_COACH_USER_HS)*/
		                distinct T.Phone,
		                         T.Ci_Weight_Desc,
		                         T.Scene_Type_Id,
		                         T.scene_id,
		                         first_level,
		                         start_date
		                  FROM VAAS.HS_INFO_FLOW_COACH_USER_INFO T,
		                       VAAS.VAAS_CLIENT_IMPORT_RECORD    R
		                 WHERE T.PHONE = R.Tel
		                   and R.Record_No = '$recordNo$'
		                   <isNotNull property="sceneType">
				            <isNotEqual property="sceneType" compareValue="-1">
				              and T.Scene_Type_Id = '$sceneType$'
				            </isNotEqual>
				         </isNotNull>
				         <isNotNull property="sceneName">
				            <isNotEqual property="sceneName" compareValue="">
				              and T.Ci_Weight_Desc like '%$sceneName$%'
				            </isNotEqual>
				         </isNotNull>
		                union all
		                select /*+INDEX(t INDEX_FLOW_CNC_USER_HS)*/
		                distinct T.Phone,
		                         T.Ci_Weight_Desc,
		                         T.Scene_Type_Id,T.scene_id,
		                         first_level,
		                         start_date
		                  from vaas.HS_INFO_FLOW_CNC_USER_INFO t,
		                       VAAS.VAAS_CLIENT_IMPORT_RECORD  R
		                 WHERE T.PHONE = R.Tel
		                   and R.Record_No = '$recordNo$'
		                   <isNotNull property="sceneType">
					            <isNotEqual property="sceneType" compareValue="-1">
					              and T.Scene_Type_Id = '$sceneType$'
					            </isNotEqual>
					         </isNotNull>
					         <isNotNull property="sceneName">
					            <isNotEqual property="sceneName" compareValue="">
					              and T.Ci_Weight_Desc like '%$sceneName$%'
					            </isNotEqual>
					         </isNotNull>
		                union all
		                select /*+INDEX(t INDEX_FLOW_COACH_USER_RH)*/
		                distinct T.Phone,
		                         T.Ci_Weight_Desc,
		                         T.Scene_Type_Id,T.scene_id,
		                         first_level,
		                         start_date
		                  from vaas.hs_info_flow_rh_user_info t,
		                       VAAS.VAAS_CLIENT_IMPORT_RECORD R
		                 WHERE T.PHONE = R.Tel
		                   and R.Record_No = '$recordNo$'
		                   <isNotNull property="sceneType">
					            <isNotEqual property="sceneType" compareValue="-1">
					              and T.Scene_Type_Id = '$sceneType$'
					            </isNotEqual>
					         </isNotNull>
					         <isNotNull property="sceneName">
					            <isNotEqual property="sceneName" compareValue="">
					             and T.Ci_Weight_Desc like '%$sceneName$%'
					            </isNotEqual>
					         </isNotNull>
		                   ) T3
		         group by t3.phone) t3,
		       VAAS.hs_code_plantype T2
		 WHERE T1.SCENE_TYPE_ID = T2.SCENE_TYPE_ID
		   and t1.phone = t3.phone
		 order by phone, first_level, start_date desc
	</select>
	<select id="getRecordSceneDetailSocialChannel" resultClass="java.util.HashMap">
	
	select T1.PHONE,T1.CI_WEIGHT_DESC,
		       T2.Scene_Type_Name,
                       t3.cnt || '-' || ROW_NUMBER() OVER(PARTITION BY T1.Phone ORDER BY T1.first_level, t1.start_date desc) CNT,
                       isYes
                  from (SELECT /*+INDEX(t INDEX_FLOW_COACH_USER_HS)*/
			                                distinct T.Phone,
			                                 T.Ci_Weight_Desc,
			                                 T.Scene_Type_Id,
			                                 first_level,
			                                 h.channel_no,
			                                 start_date,
			                                 case S.Isyes when '1' then '已推荐' else '未推荐' end isYes,
			                                  T.Scene_Id
			                          FROM VAAS.HS_INFO_FLOW_COACH_USER_INFO T,
			                          vaas.HS_INFO_USER_SEVER_RECORD_TEMP S,
			                               VAAS.VAAS_CLIENT_IMPORT_RECORD   R,
			                               vaas.Hs_User_Info_Day             H,
			                               VASS.Sc_Login_User                U
			                         WHERE T.PHONE = R.Tel
			                         and T.Scene_Id = S.Scene_Id(+)
			                         and T.Phone = S.Phone_No(+)
                                   and R.Record_No = '$recordNo$'
                                   and T.phone = H.PHONE
                                   and U.Department = H.channel_no
                                   and U.STAFF_ID = '$oprator$'
                                   <isNotNull property="sceneType">
							            <isNotEqual property="sceneType" compareValue="-1">
							              and T.Scene_Type_Id = '$sceneType$'
							            </isNotEqual>
							         </isNotNull>
							         <isNotNull property="sceneName">
							            <isNotEqual property="sceneName" compareValue="">
							              and T.Ci_Weight_Desc like '%$sceneName$%'
							            </isNotEqual>
							         </isNotNull>
                                   ) t1,
                       (select /*+INDEX(t INDEX_FLOW_COACH_USER_HS)*/ T.Phone, count(distinct T.SCENE_ID) cnt
                          FROM VAAS.HS_INFO_FLOW_COACH_USER_INFO T,
                                       VAAS.VAAS_CLIENT_IMPORT_RECORD    R,
                                       vaas.Hs_User_Info_Day             H,
                                       VASS.Sc_Login_User                U
                                 WHERE T.PHONE = R.Tel
                                   and R.Record_No = '$recordNo$'
                                   and T.phone = H.PHONE
                                   and U.Department = H.channel_no
                                   and U.STAFF_ID = '$oprator$'
                                   <isNotNull property="sceneType">
					            <isNotEqual property="sceneType" compareValue="-1">
					              and T.Scene_Type_Id = '$sceneType$'
					            </isNotEqual>
					         </isNotNull>
					         <isNotNull property="sceneName">
					            <isNotEqual property="sceneName" compareValue="">
					              and T.Ci_Weight_Desc like '%$sceneName$%'
					            </isNotEqual>
					         </isNotNull>
                         group by T.phone) t3,VAAS.hs_code_plantype T2
                 where T1.SCENE_TYPE_ID = T2.SCENE_TYPE_ID
		   and t1.phone = t3.phone
		 order by phone, first_level, start_date desc
	</select>
	<insert id="insertBssOrderInfo">
	    INSERT INTO VAAS.BSS_ORDER_INFO
                 (SERIAL_NUMBER, ORDER_NO, PRODUCT_ID, PHONE_NO, OPRATOR, ORDER_TIME, UPDATE_TIME, ORDER_STATUS, BSS_SERIAL_NUMBER)
             VALUES
                 ('$serialNumber$','$orderNo$','$productId$','$phoneNo$','$orator$',sysdate,sysdate,'2','bssSerialNumber')
	</insert>
	<select id="selectNonUpdatedBssOrder" resultClass="java.util.HashMap">
		SELECT * FROM VAAS.BSS_ORDER_INFO T WHERE T.ORDER_STATUS = '2'
		 and T.ORDER_TIME between to_date('$start$','YYYY-MM-DD HH24:MI:SS') and to_date('$end$','YYYY-MM-DD HH24:MI:SS')
	</select>
	
	<update id="updateBssOrderStatus">
		UPDATE VAAS.BSS_ORDER_INFO T SET T.ORDER_STATUS = '$status$',UPDATE_TIME = sysdate,
		BSS_OPRATE_TIME = to_date('$bssOprateTime$','YYYYMMDDHH24MISS')
		 WHERE T.BSS_SERIAL_NUMBER = '$bssSerialNumber$'
	</update>
	
	 <select id="selectFZList" resultClass="java.util.HashMap">
        select scene_type_id "sceneTypeId", 
               scene_type_name "sceneTypeName"
          from VAAS.HS_CODE_PLANTYPE
          WHERE STATE='0'
         order by ORDER_ID  
    </select>
    
	 <select id="businessStatistics" resultClass="java.util.HashMap">
       select * from vaas.hs_promote_trade_day t,(select to_char(max(to_date(ACCT_DATE,'yyyyMMdd')),'yyyyMMdd') ACCT_DATE from vaas.hs_promote_trade_day where staff_id = '$oprator$') t1 where t.staff_id = '$oprator$' and  t.acct_date = t1.acct_date
    </select>
    
	 <select id="userStatistics" resultClass="java.util.HashMap">
        select t.ACCT_DATE,
	       PHONE_NO,
	       SCENE_ID,
	       SCENE_ID_NAME,
	       TO_CHAR(TO_DATE(PROMOTE_DATE, 'yyyyMMdd'), 'yyyy-MM-dd') PROMOTE_DATE,
	       IS_ACCEPT,
	       IS_TRADE,
	       STAFF_ID
	  from vaas.hs_promote_info_day t,
	       (select to_char(max(to_date(ACCT_DATE, 'yyyyMMdd')), 'yyyyMMdd') ACCT_DATE
	          from vaas.hs_promote_info_day
	         where staff_id = '$oprator$') t1
	 where t.staff_id = '$oprator$'
	   and t.acct_date = t1.acct_date
    </select>
    
</sqlMap>
