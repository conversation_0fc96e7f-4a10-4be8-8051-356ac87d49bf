<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="mobile.services">
	<!-- 获取集团用户编码序列-->
	<select id="getGroupUserSeq" resultClass="java.lang.String">
		select jike.seq_groupuser.nextval from dual
	</select>
	<!-- 获取订单保存用户编码序列 --> 
	<select id="getOrderUserSeq" resultClass="java.lang.String">
		select jike.seq_orderuser.nextval from dual
	</select>
	
	<insert id="insertGroupCustomer">
		insert into  jike.inf_grop_customer 
		values (
		       '$cardType$',
		       '$cust_photo_path$',
		       '$customerName$',
		       '$certificateNo$',
		       '$premises$',
		       '$organizationLegal$',
		       '$lpCertificateAddress$',
		       '$organizationAddress$',
		       '$operator_photo_path$',
		       '$name$',
		       '$tel$',
		       '$cardType_1$',
		       '$cardNo$',
		       '$address$',
		       '$card_photo_path$',
		       '$mailingAddress$',
		       '$postalCode$',
		       '$tel_1$',
		       '$createTime$'
		       
		)
		
	</insert>
	
	<!-- 获取已占用号码列表 -->
	<select id="getOccpiedNumberList" resultClass="java.lang.String">
		select SERIAL_NUMBER from jike.INF_OCCUPIED_NUMBER 
		where 1 = 1
		<isNotEmpty property = "staff_id" prepend="and"> 
		staff_id = '$staff_id$'
		</isNotEmpty>
	</select>
	
	<!-- 号码池 -->
	<select id="getNumberPool" resultClass="java.lang.String">
		select SERIAL_NUMBER from jike.INF_NUMBER_POOL  
		where 1  = 1
		<iterate prepend="and" property="poolIdList" open="(" close=")" conjunction="or"> 
			POOL_ID=#poolIdList[]# 
		</iterate>
		<isNotEmpty property = "nbr_suffix">
			and substr(SERIAL_NUMBER, 8, 4) = '$nbr_suffix$'
		</isNotEmpty>
		<isNotEmpty property = "is_3g"> 
		and IS_3G = '$is_3g$' 
		</isNotEmpty>
		and IS_OCCUPIED = '0'
	</select>
	
	<!-- 查询用户area_no 和 dept_code -->
	<select id="getUserArea" resultClass="java.util.HashMap">
		select t.area_no, t.dept_code from pure_user t where login_id = '$staff_id$'
	</select>
	<!-- 查询pool_id -->
	<select id="getPoolId" resultClass="java.lang.String">
		select pool_id
		  from jike.INF_NUMBER_POOL_TYPE
		 where pool_type = (select case
		                             when org_category = '1' or org_category = '2' then
		                              '1'
		                             when org_category = '3' then
		                              '2'
		                           end
		                      from org_organization_mod
		                     where id = '$dept_code$')
	</select>
	
	<!-- 查询号码是否占用 -->
	<select id="isOccpeidNumber" resultClass="java.lang.String">
		select t.SERIAL_NUMBER from jike.INF_NUMBER_POOL t 
		where 1 = 1
		<isNotEmpty property = "nbr"> 
		and t.SERIAL_NUMBER = '$nbr$'
		</isNotEmpty>
		and t.IS_OCCUPIED = '1'
	</select>
	
	<!-- 占用号码 更新号码池-->
	<update id="updateNumberPool">
		update jike.INF_NUMBER_POOL  set IS_OCCUPIED = '1'
		where  SERIAL_NUMBER = '$nbr$'
	</update>
	
	<!-- 添加占用号码 -->
	<insert id="addOccpiedNumber">
		insert into jike.INF_OCCUPIED_NUMBER values('$staff_id$', '$cust_id$', '$nbr$')
	</insert>
	
	<!-- 查询维系任务列表 -->
	<select id="getMainTaskList" resultClass="java.util.HashMap">
		select * from (
		select t.TASK_ID          "task_id",
		case when t.type_id = '4' then '协议到期类'
			 when t.type_id = '6' then '消费行为类'
			 when t.type_id = '7' then '客户关怀类'
	    end  "task_type",
	       t.TASK_TITLE        "task_name",
         to_char(t.CREATE_DATE,'YYYY-MM-DD') "task_create_time",
         to_char(t.COMM_END_DATE ,'YYYY-MM-DD')   "task_end_time"
         from jike.dcd_task_data t
         where  1 = 1
         and not exists (select 1 from jike.maintain_task_feekback m where m.task_id = t.task_id)
  	   <isNotEmpty property = "staff_id"> 
  	   and t.STAFF_ID = '$staff_id$'
  	   </isNotEmpty>
  	   ) where rownum <![CDATA[<]]> 1000
	</select>
	
	<!-- 查询维系任务详请 -->
	<select id="getMainTaskDetail" resultClass="java.util.HashMap">
		select t.TASK_ID       "task_id",
       t.TASK_TITLE     "task_name",
       to_char(t.COMM_END_DATE,'YYYY-MM-DD') "task_end_time",
       t.ALERT_INFO   "task_detail",
       nvl(t.acc_nbr,'') "device_number"
      from JIKE.DCD_TASK_DATA t
  		where 1 = 1 
  		<isNotEmpty property = "task_id" prepend="and"> 
  		t.TASK_ID = '$task_id$'
  		</isNotEmpty>
	</select>
	
	<!-- 插入维系反馈 -->
	<insert id="insertTaskFeedback">
		insert into jike.maintain_task_feekback
		values('$task_id$', '$staff_id$', '$task_done$', '$task_feedback$')
		
	</insert>
	
	<!-- 查询未完成任务-->
	<select id="getUnfinishedTask" resultClass="java.util.HashMap">
		select t.task_id from jike.dcd_task_data t
	    where t.STAFF_ID = '$staff_id$'
	    and not exists (select 1 from jike.maintain_task_feekback m where m.task_id = t.task_id)
	</select>
	
	
	<!-- 获取品牌列表 -->
	<select id="getTerminalBrandList" resultClass="java.lang.String">
		select distinct t.mobile_brand from jike.inf_terminal_device t
	</select>
	<!-- 获取型号列表 -->
	<select id="getTerminalModelList" resultClass="java.lang.String">
		select distinct t.MOBILE_MODEL from jike.inf_terminal_device t
		where t.mobile_brand = '$terminal$'
		and nvl(t.MOBILE_MODEL, 'null') != 'null'
	</select>
	<!-- 获取内存列表 -->
	<select id="getTerminalMemoryList" resultClass="java.lang.String">
		select distinct t.MOBILE_MEMORY from jike.inf_terminal_device t  
		where t.mobile_brand = '$terminal$'
		and nvl(t.MOBILE_MEMORY, 'null') != 'null'
	</select>
	<!-- 获取单/双卡类型列表 -->
	<select id="getTerminalCardList" resultClass="java.lang.String">
		select distinct t.SIMPLE_DOUBLE_CARDS from jike.inf_terminal_device t
		where t.mobile_brand = '$terminal$'
		and nvl(t.SIMPLE_DOUBLE_CARDS, 'null') != 'null'
	</select>
	<!-- 查询颜色-->
	<select id="getTerminalColorList" resultClass="java.lang.String">
		select distinct t.color from jike.inf_terminal_device t
    		where t.mobile_brand = '$terminal$'
    	and nvl(t.color, 'null') != 'null'
	</select>
	
	
	
	<!-- 产品查询标准产品 -->
	<select id="getStandardForProductQuery" resultClass="java.util.HashMap">
		select t.pid "id",
	       t.product_name "name",
	       t.icon "icon",
	       t2.URL_PATH   "attachment" ,
           t1.url_PATH   "picture"
	  from jike.INF_STD_PRODUCTS t,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2
	  where 1 = 1 
	    and t.pid= t1.link_id(+)
	    and t.pid = t2.link_id(+)
        and t.IS_PRIMARY_SHOW = '1'
		<isNotEmpty property="net_type" prepend="and">
	  	t.NET_TYPE_NAME = '$net_type$' 
		</isNotEmpty>
		<isNotEmpty property="tele_type_lv1" prepend="and">
	  	t.TELE_TYPE_NAME = '$tele_type_lv1$' 
		</isNotEmpty>
		<isNotEmpty property="tele_type_lv2" prepend="and">
	  	t.TELE_SUBTYPE_NAME = '$tele_type_lv2$' 
		</isNotEmpty>
		<isNotEmpty property="tele_type_lv3" prepend="and">
	  	t.BRAND_NAME = '$tele_type_lv3$' 
		</isNotEmpty>
	</select>
	
	<!-- 产品查询.终端设备 -->
	<select id="getTerminalForProductQuery" resultClass="java.util.HashMap">
		select t.ID "id",
		       t.MOBILE_MODEL "name",
           t.ICON "icon",
           t2.URL_PATH   "attachment" ,
           t1.url_PATH   "picture"
	      from jike.inf_terminal_device t,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=1) t1,(select URL_PATH,link_id from  jike.inf_resource_library  where view_flag=2)t2
	      where   t.ID= t1.link_id(+)
	              and t.ID = t2.link_id(+)
			<isNotEmpty property="brand" prepend="and">
			  	t.MOBILE_BRAND = '$brand$' 
			</isNotEmpty>
			<isNotEmpty property="model" prepend="and">
			  	t.MOBILE_MODEL = '$model$' 
			</isNotEmpty>
			<isNotEmpty property="memory" prepend="and">
			  	t.MOBILE_MEMORY = '$memory$' 
			</isNotEmpty>
			<isNotEmpty property="cards" prepend="and">
			  	t.SIMPLE_DOUBLE_CARDS = '$cards$' 
			</isNotEmpty>
	</select>
	
	<!-- 销售管理.产品筛选.标准产品.产品大类-->
	<select id="getProductClass" resultClass="java.lang.String">
		select distinct t.NET_TYPE_NAME from jike.INF_STD_PRODUCTS t
	</select>
	<!-- 销售管理.产品筛选.标准产品.设备大类-->
	<select id="getDeviceType" resultClass="java.lang.String">
		select distinct t.TELE_TYPE_NAME from jike.INF_STD_PRODUCTS t where t.NET_TYPE_NAME = '$net_type$' and t.TELE_TYPE_NAME is not null
	</select>
	<!-- 销售管理.产品筛选.标准产品.业务大类-->
	<select id="getTeleType" resultClass="java.lang.String">
		select distinct t.TELE_SUBTYPE_NAME
		  from jike.INF_STD_PRODUCTS t
			 where t.NET_TYPE_NAME = '$net_type$'
			   and TELE_TYPE_NAME = '$type1$'
			   and t.TELE_SUBTYPE_NAME is not null
	</select>
	<!-- 销售管理.产品筛选.标准产品.品牌大类-->
	<select id="getBrandType" resultClass="java.lang.String">
		select distinct t.BRAND_NAME
		  from jike.INF_STD_PRODUCTS t
			 where t.NET_TYPE_NAME = '$net_type$'
			   and t.TELE_TYPE_NAME = '$type1$'
         and t.TELE_SUBTYPE_NAME = '$typelv2$'
			   and t.BRAND_NAME is not null
	</select>
	
	
	<!-- 终端对比. 终端列表 -->
	<select id="contrastTerminalList" resultClass="java.util.HashMap">
		select t.ID "id", t.MOBILE_MODEL "name", t.icon "icon" from jike.inf_terminal_device t 
		where 1 = 1
		<isNotEmpty property="brand" prepend="and">
		  	t.MOBILE_BRAND = '$brand$' 
		</isNotEmpty>
		<isNotEmpty property="model" prepend="and">
		  	t.MOBILE_MODEL = '$model$' 
		</isNotEmpty>
		<isNotEmpty property="memory" prepend="and">
		  	t.MOBILE_MEMORY = '$memory$' 
		</isNotEmpty>
		<isNotEmpty property="cards" prepend="and">
		  	t.SIMPLE_DOUBLE_CARDS = '$cards$' 
		</isNotEmpty>
	</select>
	
	<!-- 终端对比. 终端详情 -->
	<select id="contrastTerminalDetail" resultClass="java.util.HashMap">
		select t.ID "id",
	       t.MOBILE_MODEL "name",
	       t.MOBILE_BRAND "mobile_brand",
	         t.MOBILE_MODEL "mobile_model",
	         t.SIMPLE_DOUBLE_CARDS "simple_double_cards",
	         t.CPU "cpu",
	         t.SIM_TYPE "sim_type",
	         t.RESOLUTION "resolution",
	         t.SCREEN_SIZE "screen_size",
	         t.CAMERA "camera",
	         t.OS "os",
	         t.ROM "rom"
	    from jike.inf_terminal_device t
	    where 1 = 1
	  <isNotEmpty property="id1">
	  		<isNotEmpty property="id2" prepend="and">
	  			t.ID in ('$id1$', '$id2$')
	  		</isNotEmpty>
	  </isNotEmpty>
	</select>
	
	
	<insert id="insertAcceOrder">
		insert into  jike.inf_acce_order 
		values (
		   '$group_cert_type$',
		   '$group_cert_image_path$',
		   '$group_cust_name$'     ,
		   '$group_cert_id$'       ,
		   '$place_business$'     ,
		   '$legal_person$'        ,
		   '$legal_demicile$'      ,
		   '$org_addr$'            ,
		   '$oper_image_path$'   ,
		   '$oper_name$'           ,
		   '$oper_phone$'          ,
		   '$oper_cert_type$'      ,
		   '$oper_cert_id$'       ,
		   '$oper_cert_addr$'      ,
		   '$busi_card_image_path$' ,
		   '$post_addr$'          ,
		   '$zip_code$'           ,
		   '$group_contact_phone$'  ,
		   '$order_type$'        ,
		   '$cust_id$',
		   '$cust_name$'           ,
		   '$install_address$'    ,
		   '$contact$'             ,
		   '$contact_phone$'       ,
		   '$is_group_busi$'       ,
		   '$cert_id$'            ,
		   '$cert_image_path$'    ,
		   '$gsv_id$'             ,
		   '$busi_class$'         ,
		   '$busi_type$'          ,
		   '$bus_child_class$'    ,
		   '$busi_sub_class$'      ,
		   '$discnt_code$'         ,
		   '$serial_number$'       ,
		   '$terminal$'            ,
		   '$contract_fee$'       ,
		   '$oper_fee$'             ,
		   '$pay_acct$'            ,
		   '$fee_image_path$'     ,
		   '$sign_image_path$'     ,
		   '$rece_name$'            ,
		   '$rece_phoe$'           ,
		   '$rece_addr$'          ,
		   '$staff_id$',
		   '$remarks$',
		   '$product_type$',
		   '$product_id$',
		   '$order_id$',
		   TO_CHAR(SYSDATE,'YYYYMMDD HH24:mi:ss')           
		)
		
		
	</insert>
	
	<!-- Socket 查询关系-->
	<select id="getRelationForSocket" resultClass="java.lang.String">
		<!-- SELECT t.relation_type_name FROM DIM.DIM_RELATION_TYPE_CODE t where t.relation_type_code = '$relationTypeCode$' -->
		SELECT '测试关系类型' "relation_type_name" FROM DUAL
	</select>
	
	<!-- Socket 查询服务-->
	<select id="getServiceForSocket" resultClass="java.lang.String">
		SELECT * FROM  ods.td_s_servicestate_d T WHERE t.service_id = '$serviceId$'
		<!--  SELECT t.service_name FROM dim.DIM_SERVICE T where t.service_id = '$serviceId$' -->
	</select>
	
	<!-- Socket 查询账户类型-->
	<select id="getPayModeForSocket" resultClass="java.lang.String">
		SELECT '测试账号类型' "pay_mode" FROM DUAL
		<!-- select t.pay_mode from dim.dim_pay_fee_mode_code t where t.pay_mode_code = '$payModeCode$'  -->
	</select>
	
	<!-- 查询客户经理信息-->
	<select id="getManagerInfo" resultClass="java.util.HashMap">
		select t.user_name, t.telephone from pure_user t where t.login_id = '$staff_id$'
	</select>
	
	<!-- 保存业务受理处理详细信息，原项目以调用亚信WS接口，本地化方案暂且以存入本地数据库  <NAME_EMAIL> -->
	<insert id="insertAccePreaccept" >
		INSERT INTO VASS.VASS_DY_PREACCEPT_GROUP
		  (pspt_type_code_g,
			photo_way_g,
			cust_name_g,
			pspt_id_g,
			place_business,
			legal_person,
			legal_demicile,
			org_addr,
			oper_photo_way,
			oper_name,
			oper_phone,
			oper_pspt_type,
			oper_pspt_id,
			oper_pspt_addr,
			busi_card_photo_way,
			post_addr_g,
			zip_code,
			contact_phone_g,
			order_type,
			trade_depart_id,
			cust_id,
			cust_name,
			instail_address,
			contact,
			contact_phone,
			is_unit_busi,
			pspt_id,
			pspt_photo_way,
			group_id,
			busi_class,
			busi_type,
			bus_child_class,
			busi_sub_class,
			discnt_code,
			serial_number,
			pre_terminal,
			contract_fee,
			oper_fee,
			acct_id,
			fee_photo_way,
			sign_photo_way,
			rece_name,
			rece_phone,
			rece_addr,
			cust_manag_name,
			cust_manag_phone,
			cust_manag_id,
			accept_date,
			remark,
			develop_depart_id,
			develop_staff_id,
			user_id,
			rsrv_str1,
			rsrv_str2,
			rsrv_str3,
			rsrv_str4,
			rsrv_str5
)
		VALUES
		  ('$psptTypeCodeG$',
		   '$photoWayG$',
		   '$custNameG$',
		   '$psptIdG$',
		   '$placeBusiness$',
		   '$legalPerson$',
		   '$legalDemicile$',
		   '$orgAddr$',
		   '$operPhotoWay$',
		   '$operName$',
		   '$operPhone$',
		   '$operPsptType$',
		   '$operPsptId$',
		   '$operPsptAddr$',
		   '$busiCardPhotoWay$',
		   '$postAddrG$',
		   '$zipCode$',
		   '$contactPhoneG$',
		   '$orderType$',
		   '$tradeDepartId$',
		   '$custId$',
		   '$custName$',
		   '$instailAddress$',
		   '$contact$',
		   '$contactPhone$',
		   '$isUnitBusi$',
		   '$psptId$',
		   '$psptPhotoWay$',
		   '$groupId$',
		   '$busiClass$',
		   '$busiType$',
		   '$busChildClass$',
		   '$busiSubClass$',
		   '$discntCode$',
		   '$serialNumber$',
		   '$preTerminal$',
		   '$contractFee$',
		   '$operFee$',
		   '$acctId$',
		   '$feePhotoWay$',
		   '$signPhotoWay$',
		   '$receName$',
		   '$recePhone$',
		   '$receAddr$',
		   '$custManagname$',
		   '$custManagPhone$',
		   '$custManagId$',
		   '$acceptDate$',
		   '$remark$',
		   '$developDepartId$',
		   '$developStaffId$',
		   '$userId$',
		   '$rsrvStr1$',
		   '$rsrvStr2$',
		   '$rsrvStr3$',
		   '$rsrvStr4$',
		   '$rsrvStr5$')
	</insert>
	
	<!-- 用户信息查询  系统原从亚信WS接口查询，现修改为从网格数据库中查询  add  by <EMAIL> 20140716 -->
	<select id="queryUserInfoByPhoneNo" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
		SELECT SERIAL_NUMBER,
	       CUST_NAME,
	       PSPT_ID,
	       PAY_NAME,
	       PAY_MODE_CODE,
	       IN_DATE,
	       DETAIL_ADDRESS,
	       PRODUCT_NAME,
	       BRAND,
	       PRODUCT_EXPLAIN,
	       PRODUCT_START_DATE,
	       PRODUCT_END_DATE,
	       SERVICE_NAME,
	       SERVICE_ID,
	       SERVICE_START_DATE,
	       SERVICE_END_DATE,
	       DISCNT_NAME,
	       DISCNT_EXPLAIN,
	       DISCNT_START_DATE,
	       DISCNT_END_DATE,
	       RELATION_TYPE_COD,
	       GROUP_ID,
	       RELATION_START_DATE,
	       RELATION_END_DATE,
	       SCORE_VALUE,
	       STAFF_ID
	  FROM VASS.VASS_USER_INFO_D A
	  WHERE A.SERIAL_NUMBER='$serviceNumber$'   <!-- 号码条件，由APP传入 -->
		AND CUST_NAME='$userName$'			      <!-- 用户姓名 -->		
		<isNotEmpty property="cardNumber">		<!-- 用户证件号码，个人用户查询时需要 -->
			AND A.PSPT_ID='$cardNumber$'
		</isNotEmpty>
	</select>
	
	<!-- 接口改造：获得用户成员信息数据 <NAME_EMAIL> 20140717-->
	<select id="getUserMemberList" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
	SELECT A.BRAND,
	       A.CUST_NAME,
	       A.DETAIL_ADDRESS,
	       A.DISCNT_END_DATE,
	       A.DISCNT_EXPLAIN,
	       A.DISCNT_NAME,
	       A.DISCNT_START_DATE,
	       A.GROUP_ID,
	       A.IN_DATE,
	       A.PAY_MODE_CODE,
	       A.PAY_NAME,
	       A.PRODUCT_END_DATE,
	       A.PRODUCT_EXPLAIN,
	       A.PRODUCT_NAME,
	       A.PRODUCT_START_DATE,
	       A.PSPT_ID,
	       A.RELATION_END_DATE,
	       A.RELATION_START_DATE,
	       A.RELATION_TYPE_COD,
	       A.SCORE_VALUE,
	       A.SERIAL_NUMBERK,
	       A.SERVICE_END_DATE,
	       A.SERVICE_ID,
	       A.SERVICE_NAME,
	       A.SERVICE_START_DATE,
	       B.START_DATE,
	       B.END_DATE
	  FROM VASS.VASS_USER_INFO_D A, VASS.VASS_USER_INFO_MEM_D B
	 WHERE B.MAIN_SERIAL_NUMBER = '$serviceNumber$'
	   AND B.SERIAL_NUMBER = A.SERIAL_NUMBER
	</select>
	<!-- 获得系统业务类型数据，为APP提供接口  <NAME_EMAIL> 20140717 -->
	<select id="getSystemSvcTypes" resultClass="java.util.HashMap">
		SELECT '-1' "SVC_TYPE", '所有' "SVC_DESC"
		  FROM DUAL
		  UNION ALL
		SELECT T.SVC_TYPE, T.SVC_DESC
		  FROM DMCODE.G_DMCODE_SVC_TYPE T
		 WHERE T.PARENT_ID = 'root'
	</select>
	
	<!-- 接口改造，通过条件判断查询客户信息  <NAME_EMAIL>  -->
	<select id="getCustInfoByDyn" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
	SELECT A.CUST_ID,
	       A.CUST_TYPE,
	       A.CERT_TYPE,
	       A.VIP_CODE,
	       A.AREA_ID,
	       A.IMPORT_LEVEL,
	       A.ASSESS_LEVEL,
	       A.POSTAL_CODE,
	       A.ADD_FLAG,
	       A.MAINT_LEVEL,
	       A.SERV_LEVEL,
	       A.ADDRESS,
	       A.STS,
	       A.CUST_RESOURCE,
	       A.CREATE_DATE,
	       A.GSV_NAME,
	       A.STAFF_ID,
	       A.M_CUST_ID,
	       A.NAME,
	       A.CUST_CAT_ID,
	       A.LOCAL_NET_ID,
	       A.VERTICAL_VOCAL_NAME,
	       A.SERV_DEPT_ID,
	       A.CERT_ID,
	       A.MAIN_PAY_NO,
	       A.GSV_LIST,
	       A.PHOTO_PATH,
	       A.IN_DATE,
	       A.CUST_NAME,
	       A.PSPT_ADDR
	  FROM G_GI.GI_CUST_INFO_CRM A
	 WHERE 1=1
	 <!-- 工号条件 -->
	 <isNotNull property="staff_id" prepend="AND">
	 	A.STAFF_ID='$staff_id$'
	 </isNotNull>
	 <!-- 客户编码条件  -->
	 <isNotNull property="custId" prepend="AND">
	 	A.CUST_ID = '$custId$' 
	 </isNotNull>
	 <!-- 客户名称条件 -->
	 <isNotNull property="cust_name" prepend="AND">
	 	A.NAME='$cust_name$'
	 </isNotNull>
	 <!-- 关键条件查询 -->
	 <isNotNull property="keyWord" prepend="AND">
	 	(A.VIP_CODE = '$keyWord$' OR A.M_CUST_ID = '$keyWord$' OR A.CUST_ID='$keyWord$'
	 	 OR A.NAME LIKE '%'||'$keyWord$'||'%%' OR A.CUST_NAME LIKE '%'||'$keyWord$'||'%')
	 </isNotNull>
	</select>
</sqlMap>