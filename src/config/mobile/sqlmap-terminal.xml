<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="terminal">
	<select id="getTerminalList" resultClass="java.util.HashMap" remapResults="true">
		select * from jike.inf_terminal_device t
		where 1=1
		<isNotEmpty property="MOBILE_BRAND" prepend="and">
			t.MOBILE_BRAND like '%' || '$MOBILE_BRAND$' || '%'
		</isNotEmpty>
		<isNotEmpty property="MOBILE_MODEL" prepend="and">
			t.MOBILE_MODEL like '%' || '$MOBILE_MODEL$' || '%'
		</isNotEmpty>
		<isNotEmpty property="SIMPLE_DOUBLE_CARDS" prepend="and">
			<isNotEqual property="SIMPLE_DOUBLE_CARDS"  compareValue="-1">
			t.SIMPLE_DOUBLE_CARDS ='$SIMPLE_DOUBLE_CARDS$'
			</isNotEqual>
		</isNotEmpty>
		<isNotEmpty property="SIM_TYPE" prepend="and">
			t.SIM_TYPE like '%' || '$SIM_TYPE$' || '%'
		</isNotEmpty>
	</select>
	<insert id="insertTerminal">
		insert into jike.inf_terminal_device
			
			 (
				mobile_brand, 
				mobile_model, 
				icon, 
				simple_double_cards,
				cpu, 
				sim_type, 
				resolution, 
				screen_size, 
				camera, 
				os, 
				rom, 
				id,
				ICON_THUMB
		) values(
				'$MOBILE_BRAND$',
				'$MOBILE_MODEL$',
				'$ICON$',
				'$SIMPLE_DOUBLE_CARDS$',
				'$CPU$',
				'$SIM_TYPE$',
				'$RESOLUTION$',
				'$SCREEN_SIZE$',
				'$CAMERA$',
				'$OS$',
				'$ROM$',
				'$id$',
				'$ICON_THUMB$'
		)
	</insert>
	<delete id="deleteTerminal" >
		delete from jike.inf_terminal_device t
		where t.id='$id$'
	</delete>
	<delete id="deleteResource">
		delete from jike.inf_resource_library t
		where t.link_id='$id$'
	</delete>
	<select id="getTerminal" resultClass="java.util.HashMap" remapResults="true">
		select * from jike.inf_terminal_device t
		where t.id='$id$'
	</select>
	<select id="getAttachmentList" resultClass="java.util.HashMap">
		select * from jike.inf_resource_library t where t.link_id='$id$'
	</select>
	<update id="updateTerminal">
		update jike.inf_terminal_device t
				set
				mobile_brand='$MOBILE_BRAND$', 
				mobile_model='$MOBILE_MODEL$',
				<isNotEmpty property="ICON">
				icon='$ICON$',
				</isNotEmpty>
				simple_double_cards='$SIMPLE_DOUBLE_CARDS$',
				cpu='$CPU$', 
				sim_type='$SIM_TYPE$', 
				resolution='$RESOLUTION$', 
				screen_size='$SCREEN_SIZE$', 
				camera='$CAMERA$', 
				os='$OS$', 
				<isNotEmpty property="ICON_THUMB">
					ICON_THUMB='$ICON_THUMB$',
				</isNotEmpty>
				rom='$ROM$'
				where t.id='$id$'
	</update>
</sqlMap>
