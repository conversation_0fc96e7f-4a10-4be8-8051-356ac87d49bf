<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="industry.application">
<!-- 列表展示查询 -->
<select id="getIndustryInfo"  resultClass="java.util.HashMap"> 
	select product_type  "product_type",
	       industry_type  "industry_type",
	       product_name  "product_name",
	       plans_name  "plans_name",
	       plans_price  "plans_price",
	       plans_content  "plans_content",
	       plans_explain   "plans_explain",
	       id   "id"
	from   jike.inf_industry_application   
	where 1=1
	<dynamic>
		<isNotEmpty property="product_type">
		    and product_type like '%$product_type$%'
		</isNotEmpty>
		<isNotEmpty property="industry_type">
		    and industry_type like '%$industry_type$%'
		</isNotEmpty>
		<isNotEmpty property="product_name">
		    and product_name like '%$product_name$%'
		</isNotEmpty>
		<isNotEmpty property="plans_name">
			and plans_name like '%$plans_name$%'
		</isNotEmpty>
	</dynamic> 
	order by to_number(id) desc
</select>
<!-- 新增 -->
<insert  id="addProduct">
insert into  jike.inf_industry_application  (product_type,
                                             industry_type,
                                             product_name,
                                             plans_name,
                                             product_code,
                                             plans_code,
                                             plans_price,
                                             plans_content,
                                             plans_explain,
                                             icon,
                                             icon_thumb,
                                             id,
                                             cust_visible
                                            )
                                      values('$protype$',
                                      		 '$indtype$',
                                      		 '$product$',
                                      		 '$taocan$',
                                      		 '$productid$',
                                      		 '$taocanid$',
                                      		 '$taocanprice$',
                                      		 '$taocancontent$',
                                      		 <isNotEmpty  property="sendexplain">
                                      		 '$sendexplain$',
                                      		 </isNotEmpty>
                                      		 <isEmpty  property="sendexplain">
                                      		  '',
                                      		 </isEmpty>
                                      		 '$ICON$',
                                      		 '$ICON_THUMB$',
                                      		 '$id$',
                                      		 '$show$'
                                            )
</insert>
<!--获取要修改产品信息-->
<select id="getmodifyList"  resultClass="java.util.HashMap"> 
	select  *   from jike.inf_industry_application
   where id='$id$'			
</select>
<!-- 获取要修改的图片信息 -->
<select id="getResourceByLinkId" resultClass="java.util.HashMap">
	select * from jike.inf_resource_library where LINK_ID = '$id$' order by IS_IMAGE
</select>
<!-- 修改产品信息 -->
<update  id="DoModifyProduct">
update  jike.inf_industry_application  set   product_type  = '$protype$', 
											industry_type = '$indtype$', 
											product_name = '$product$', 
											product_code = '$productid$', 
											plans_name = '$taocan$', 
											plans_code = '$taocanid$', 
											plans_price = '$taocanprice$', 
											plans_content = '$taocancontent$', 
			 								plans_explain = '$sendexplain$',
			 								cust_visible ='$show$'
			       	<isNotNull property="ICON">
		                          <isNotEqual property="ICON" compareValue="">
	   	   		                         ,ICON = '$ICON$'
	   	                          </isNotEqual>
  	                </isNotNull> 
  	   
			  	   <isNotNull property="ICON_THUMB">
					   <isNotEqual property="ICON_THUMB" compareValue="">
				   	   		,ICON_THUMB = '$ICON_THUMB$'
				   	   </isNotEqual>
			  	   </isNotNull> 
											
  where   id = '$id$'										
</update>
<delete  id="deleteRecord">
delete from  jike.inf_industry_application  where   id = '$id$'	
</delete>
<select id="getNextProductId" resultClass="java.lang.Object">
	SELECT jike.SEQ_PRODUCT_ID.NEXTVAL FROM DUAL
</select>
<insert id="insertResource">
insert into jike.inf_resource_library (
RID,URL_PATH, REAL_PATH, IMAGE_THUMB_PATH, 
IMAGE_THUMB_REAL_PATH, VIEW_FLAG, LINK_ID,
 CREATE_DATE, IS_IMAGE,ORIGINAL_FILENAME)
values ( jike.SEQ_RESOURCE_ID.NEXTVAL,'$URL_PATH$', '$REAL_PATH$', '$IMAGE_THUMB_PATH$',
 '$IMAGE_THUMB_REAL_PATH$', '$VIEW_FLAG$', '$LINK_ID$', sysdate, '$IS_IMAGE$','$ORIGINAL_FILENAME$')
</insert>
<update id="updateResource">
	update jike.inf_resource_library SET VIEW_FLAG = '$VIEW_FLAG$' where RID = '$RID$'
</update>
<delete id="deleteResourceByRId">
	delete jike.inf_resource_library where RID = '$value$'
</delete>

<select id="getResourceByRId" resultClass="java.util.HashMap">
	select * from jike.inf_resource_library where RID = '$value$'
</select>
<!-- 获取要修改的行业应用信息 -->
<select id="getStandardProductById" resultClass="java.util.HashMap">
	select * from jike.inf_industry_application where id = '$id$'
</select>
<delete id="deleteProduct">
	delete jike.inf_industry_application where id = '$value$'
</delete>
<delete id="deleteResourceByLinkId">
	delete jike.inf_resource_library where LINK_ID = '$value$'
</delete>
</sqlMap>
