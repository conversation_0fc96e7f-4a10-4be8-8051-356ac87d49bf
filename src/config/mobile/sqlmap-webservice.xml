<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="webservice">
	<!-- 获得常量表中的配置信息  -->
	<select id="login" resultClass="java.util.HashMap">
	 select t1.LOGIN_ID   "id",
	             t1.USER_NAME "name",
	             t1.LOGIN_ID   "staff",
	             t1.password   "psd",
	             t1.DEPT_CODE "dept",
	             t1.USER_ID "lgId",
	             ADMIN  "ismaster",
	             '-1' "init_pwd",
	             '2' "staff_type",
	             '' "role_id",
	             t1.AREA_NO "CITY_NO",
	             '甘肃' || '-' || nvl(t2.AREA_DESC ,'兰州') AREA_DESC
	          from PURE_USER       t1,
	             CODE_AREA T2
	       where t1.area_no = t2.area_no(+)
	     and t1.login_id = #username#
			 and t1.STATE = '1'
		     and rownum = '1'
	</select>
	
		<select id="loginNew" resultClass="java.util.HashMap">
	  select t1.LOGIN_ID   "id",
	             t1.USER_NAME "name",
	             t1.LOGIN_ID   "staff",
	             t1.password   "psd",
	             t1.DEPT_CODE "dept",
	             t1.USER_ID "lgId",
	             ADMIN  "ismaster",
	             '-1' "init_pwd",
	             '2' "staff_type",
	             '' "role_id",
	             t1.AREA_NO "CITY_NO",
	             '甘肃' || '-' || nvl(t2.AREA_DESC ,'兰州') AREA_DESC
	          from PURE_USER       t1,
	             CODE_AREA T2
	       where t1.area_no = t2.area_no(+)
	     and t1.login_id = #username#
			 and t1.STATE = '1'
		     and rownum = '1'
	</select> 
	
	<select  id="selectLoginPost" parameterClass="java.lang.String" resultClass="java.util.HashMap" >
		select *
	  from vass.sc_log_user_login t
	 where t.client_type='1'
	   and to_char(login_date, 'yyyymmdd') = to_char(sysdate, 'yyyymmdd')
	   and t.login_id= #username#
	</select>
	
	<select id="channel" resultClass="java.util.HashMap">
		SELECT t.AGENT_ID,t.SOCIAL_AGENT_ID
		  FROM VAAS.AGENT_OPERATOR_INFO T
		 WHERE T.OPERRATOR_ACCOUNTNUM = #username#
	 </select>
	 
	 	<update id="updateOnlineUser" parameterClass="java.lang.String">
	 update vass.SC_LOG_LOGIN_CLIENT t
    set t.state=1,t.logout_date=sysdate
     where t.session_id='$value$'

	</update>
	<insert id="insertOnlineUser" parameterClass="java.util.HashMap">
		insert into vass.SC_LOG_LOGIN_CLIENT
  		values(#sessionId#,#loginId#,#clientIp#,#mac#,0, sysdate,null, sysdate)
	</insert>
	<select id="getUserInfos" resultClass="java.util.HashMap" >
	  <!--  	select t1.user_id   "id",
	           t1.login_name "name",
	           t1.staff_id   "staff",
	           t1.password   "psd",
	           t1.department "dept",
	           t2.ismaster   "ismaster",
	           t2.agent_id   "agent_id",
	           t2.SOCIAL_AGENT_ID "society_agent_id",
	           t1.init_pwd   "init_pwd",
	           t1.STAFF_TYPE "staff_type",
	           t4.AGENT_TYPE_ID "role_id"
	      from vass.sc_login_user       t1,
	           vaas.agent_operator_info t2,
	           vaas.CHANNEL_AGENT_INFO  t3,
	           vaas.CHANNEL_TYPE        t4
	     where (t1.staff_id = #username# or t1.pc_alias = #username#)
	       and t1.login_id = t2.operrator_id
	       自有与社渠的部门编号不同 channelType 1：自有, 0：社渠
       <isEqual property="channelType" compareValue="1">
       		and t2.AGENT_ID = t3.AGENT_ID
       </isEqual>
        <isEqual property="channelType" compareValue="0">
       		and t2.SOCIAL_AGENT_ID = t3.AGENT_ID
       </isEqual>
	       and t3.AGENT_TYPE = t4.AGENT_TYPE_ID
	     and t1.STATE = '1'
	     and rownum = '1' -->
		
		   select t1.user_id   "id",
             t1.login_name "name",
             t1.staff_id   "staff",
             t1.password   "psd",
             t1.department "dept",
             CASE WHEN t1.IS_ADMIN='Y' THEN '1' ELSE '0' END    "ismaster",
             t1.init_pwd   "init_pwd",
             t1.STAFF_TYPE "staff_type",
             t1.CHANNEL_TYPE "role_id",
             t1.CITY_NO
        from vass.sc_login_user       t1
		where (t1.staff_id = #username#)
		<!--  where (t1.staff_id = #username# or t1.pc_alias = #username#) -->
		  and t1.STATE = '1'
	     and rownum = '1'
   	</select>
   	
	<select id="isNumberExist" resultClass="java.util.HashMap">
		select t.phone_no as "phoneNo"
		  from vaas.user_info t
		 where t.phone_no = '$tel$'	
	</select>
	
	<!-- 判断手机是否3G 0是1不是-->
	<select id="is3GTele" resultClass="String">
			 select /*+INDEX (T INDEX_FLOW_COACH_USER_INFO)*/DECODE(T.IS_3G, '30', '2', '20', '1', '0') IS3G from vaas.FLOW_COACH_USER_INFO t where t.PHONE = '$tel$' 
			  <isNotNull property="areaNo">
			   <isNotEqual property="areaNo" prepend="and"  compareValue="-1">
		   	   		t.area_no = '$areaNo$'
		   	   </isNotEqual>
	   	   </isNotNull> 
	<!--  
		SELECT DECODE(T.GENT_TAG, '30', '2', '20', '1', '0') IS3G
		  FROM VAAS.USER_INFO T
		 WHERE T.PHONE_NO  = '$tel$'-->
	</select>
	<select id="selectStaffID" resultClass="java.util.HashMap">	
		SELECT DISTINCT T1.LOGIN_ID	"STAFF_ID",
                T1.DEPT_CODE        "DEPARTMENT",
                T2.CHANNEL_KIND 	"CHANNEL_TYPE",
                T3.CHNL_KIND_NAME	"CHANNEL_SUBPAR_TYPE"
	  FROM PURE_USER                      T1,
	       DMCODE.DMCODE_CHANNEL_NO     T2,
	       DMCODE.G_DMCODE_CHANNEL_TYPE T3
	 WHERE T1.DEPT_CODE = T2.CHANNEL_NO(+)
	   AND T2.CHANNEL_KIND = T3.CHNL_KIND_ID(+)
	   AND T1.MOBILE_STATE = '1'
	   AND T1.LOGIN_ID = '$loginId$'
	</select>
	<select id="selectStaffIDNew" resultClass="java.util.HashMap">	
		SELECT DISTINCT T1.LOGIN_ID	"STAFF_ID",
                T1.DEPT_CODE        "DEPARTMENT",
                T2.CHANNEL_KIND 	"CHANNEL_TYPE",
                T3.CHNL_KIND_NAME	"CHANNEL_SUBPAR_TYPE"
	  FROM PURE_USER                      T1,
	       DMCODE.DMCODE_CHANNEL_NO     T2,
	       DMCODE.G_DMCODE_CHANNEL_TYPE T3
	 WHERE T1.DEPT_CODE = T2.CHANNEL_NO(+)
	   AND T2.CHANNEL_KIND = T3.CHNL_KIND_ID(+)
	   AND T1.MOBILE_STATE = '1'
	   AND T1.LOGIN_ID = '$loginId$'
	</select>
</sqlMap>