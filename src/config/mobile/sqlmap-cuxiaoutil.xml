<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="cuxiao_util">
	<!-- 终端分类（不包括root,TERMINAL_KIND_ID名字换成D_TERMINAL_KIND_ID，与表区分）通用 （推荐）-->
	<sql id="selectItem_termKindId_D_TERM_KIND_ID">
        (SELECT T.TERMINAL_KIND_ID D_TERM_KIND_ID
	       FROM g_ga.dim_model_kind_tree T
				CONNECT BY T.PARENT_KIND = PRIOR T.TERMINAL_KIND_ID
				START WITH T.TERMINAL_KIND_ID = '$termKindId$')
	</sql>	
	
	<!-- 合作伙伴 -->
	<select id="partnerList" resultClass="java.util.HashMap">
		  SELECT T.PARTER_DEPT_ID  K, T.PARTER_DEPT_DESC V
        FROM SALE.DM_CHNL_PARTER_DEPT T
       WHERE T.PARTER_DEPT_LVL = 1
       ORDER BY T.ORD
	</select>
	
	<!-- 终端分类 s -->
	
	<!-- 终端范围 s -->
	<select id="termRangeListBak" resultClass="java.util.HashMap">
	 	  SELECT * FROM (
    SELECT TERM_RANGE K, TERM_RANGE_DESC V FROM DIM.DIM_TERMINAL_RANGE 
    UNION ALL 
    SELECT '19' K,'本省合约销售（共）' FROM DUAL 
       ) WHERE K IS NOT NULL AND V IS NOT NULL ORDER BY K
	</select>
	
	<!-- 终端范围 s -->
	<select id="termRangeList" resultClass="java.util.HashMap">
		 	  
	
	SELECT '1' O, '产业链' V, '2' K
	  FROM DUAL
	UNION ALL
	SELECT '2' O, '非产业链' V, '134' K
	  FROM DUAL
	UNION ALL
	SELECT '3' O, '非产业链--总部合约' V, '1' K
	  FROM DUAL
	UNION ALL
	SELECT '4' O, '非产业链--本省合约' V, '3' K
	  FROM DUAL
	UNION ALL
	SELECT '5' O, '非产业链--裸机销售' V, '4' K
	  FROM DUAL
	UNION ALL
	SELECT '6' O, '本省合约合计' V, '23' K FROM DUAL ORDER BY O

	</select>
	
	
	
	
	
	
	<!-- 终端品牌 -->
	<select id="termBrandList" resultClass="java.util.HashMap">
		SELECT * FROM (
    SELECT DISTINCT TERM_BRAND K, TERM_BRAND_DESC V
      FROM SALE.DM_SOCIAL_IPHONE_TERM_D
      ) WHERE K IS NOT NULL AND V IS NOT NULL
	</select>
	
	
	
	
	
	<!-- 终端合约类型 (暂停使用)-->
	<select id="schemeTypeListbak" resultClass="java.util.HashMap">
		  SELECT * FROM (
    SELECT DISTINCT STAT_TYPE K, STAT_TYPE_NAME V
      FROM SALE.DM_SOCIAL_IPHONE_TERM_D
      ) WHERE K IS NOT NULL AND V IS NOT NULL
	</select>
	
	<!-- 终端合约类型 (使用中)-->
	<select id="schemeTypeList" resultClass="java.util.HashMap">
		 SELECT T.STAT_TYPE K,T.STAT_TYPE_NAME V FROM  DIM.DIM_STAT_TYPE T
	</select>
	
		<!--通用联动 3级  -->
	
	<!-- 联动 终端品牌 -->
	<select id="LD_termBrandList" resultClass="java.util.HashMap">
	  SELECT DISTINCT TERM_BRAND K, TERM_BRAND V
      FROM $linkTable$
	</select>
	
	
	
	<!--联动 终端型号 （终端类型，品牌 决定手机型号 ）-->
  <select id="LD_termModelList" resultClass="java.util.HashMap">
    SELECT DISTINCT T.term_model AS "K", T.term_model_desc AS "V"　FROM $linkTable$ T,
                                    <include refid="selectItem_termKindId_D_TERM_KIND_ID"/> D 
    where t.TERM_KIND_ID = D.D_TERM_KIND_ID(+)
    <isNotNull property="termBrand">
           <isNotEqual property="termBrand" compareValue="-1">
                   AND T.TERM_BRAND = '$termBrand$'
           </isNotEqual>
    </isNotNull>           
    ORDER BY T.term_model
  </select>
	
	<!-- 终端型号 -->
  <select id="termModelList" resultClass="java.util.HashMap">
    SELECT DISTINCT T.term_model AS "K", T.term_model_desc AS "V"　FROM DIM.DIM_SOCIAL_IPHONE T,
                                    <include refid="selectItem_termKindId_D_TERM_KIND_ID"/> D 
    where t.TERM_KIND_ID = D.D_TERM_KIND_ID
    <isNotNull property="termBrand">
           <isNotEqual property="termBrand" compareValue="-1">
                   AND T.TERM_BRAND = '$termBrand$'
           </isNotEqual>
    </isNotNull>           
    ORDER BY T.term_model
  </select>
  
  
  <!-- 活动列表 -->
  <select id="schemeList" resultClass="java.util.HashMap">
    SELECT DISTINCT T.SCHEME_ID AS "K", T.SCHEME_DESC AS "V"　
      FROM DIM.DIM_SOCIAL_IPHONE T
          where 1=1
               <isNotNull property="termBrand">
				   <isNotEqual property="termBrand" compareValue="-1">
				           AND T.TERM_BRAND = '$termBrand$'
				   </isNotEqual>
				</isNotNull>   
				
		        <isNotNull property="schemeType">
				   <isNotEqual property="schemeType" compareValue="-1">
				           AND T.STAT_TYPE = '$schemeType$'
				   </isNotEqual>								   
		        </isNotNull>	
		 ORDER BY T.SCHEME_ID
		
	</select>
	
	 <!-- 获取终端分类名称 -->
    <select id="getTermKindName" resultClass="java.lang.String">
		select t.TERMINAL_KIND_DESC from dim.dim_model_kind_tree t where t.TERMINAL_KIND_ID='$termKindId$'
	</select>	
	
	<!-- 销售模式(暂停使用)-->
	<select id="schemeType2Listbak" resultClass="java.util.HashMap">
		SELECT * FROM (
			select DISTINCT t.scheme_type2 K,t.scheme_type2_name V from sale.dm_social_iphone_term_d t
      ) WHERE K IS NOT NULL AND V IS NOT NULL
	</select>

	<!-- 销售模式(不用了)-->
	<select id="schemeType2Listbak2" resultClass="java.util.HashMap">
	 SELECT  '01' K,'存费送机' V FROM DUAL 
      UNION ALL
      SELECT  '02' K,'购机送费' V FROM DUAL 
       UNION ALL
      SELECT  '03' K,'存费送费' V FROM DUAL 
       UNION ALL
      SELECT  '04' K,'其他' V FROM DUAL 
	</select>
	
	<!-- 销售模式(使用中)-->
	<select id="schemeType2List" resultClass="java.util.HashMap">
	  select distinct scheme_type2 K, scheme_type2_name V
     from dim.dim_dim_scheme_type_type_rel ORDER BY K
	</select>


	
	
</sqlMap>