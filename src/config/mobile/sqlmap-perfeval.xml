<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="mobile.perfeval">
	 <!-- 绩效任务
	<select id="list" resultClass="java.util.HashMap">
	  select t.new_grid_id  id,
			 t.new_grid_name  name,
		     nvl(T.TOWN_RATE,0) a1,
	         nvl(T.TC_RATE,0) a2,
	         nvl(round(sum(T.CM_ROYALTY_FEE), 4),0) a3,
	         nvl(round(sum(T.CM_ROYALTY_COMM_FEE), 4),0) a4,
	         nvl(round(sum(T.CM_TOGE_SCHEME_FEE), 4),0) v1,
	         nvl(round(sum(T.CM_TOGE_PRETTY_FEE), 4),0) v2,
	         nvl(round(sum(T.CM_TOGE_EXTRA_FEE), 4),0) v3,
	         nvl(round(sum(T.CM_NOTOGE_SCHEME_FEE), 4),0) v4,
	         nvl(round(sum(T.CM_NOTOGE_PRETTY_FEE), 4),0) v5,
	         nvl(round(sum(T.CM_EXTRA_FEE), 4),0) v6,
	         nvl(round(sum(T.CM_INIT_FEE), 4),0) v7
	    FROM (SELECT *
	            FROM g_ga.DM_GM_ROYALTY_PAYMENT_DS T
	           WHERE T.ACCT_DATE = '$dateId$'
	             AND t.GM_ID = '$GMId$') T
    GROUP BY T.NEW_GRID_ID,T.NEW_GRID_NAME,T.TOWN_RATE, T.TC_RATE
	</select>
	 -->
	
	<select id="list" resultClass="java.util.HashMap">
	  select t.new_grid_id  id,
			 t.new_grid_name  name,
		     nvl(sum(T.DEV_2G),0) a1,
	         nvl(sum(T.DEV_3G),0) a2,
	         round(decode(sum(nvl(T.ONNET_USER,0)),0,0,
		     sum(nvl(T.OWE_USER,0))/sum(nvl(T.ONNET_USER,0))),4) a3,
	         round(decode(sum(nvl(T.ONNET_USER,0)),0,0,
		     sum(nvl(T.BREAK_USER,0))/sum(nvl(T.ONNET_USER,0))),4) a4,
		     nvl(round(sum(T.DEV_FIX), 4),0) v1,
	         nvl(round(sum(T.DEV_PHS), 4),0) v2,
	         nvl(round(sum(T.DEV_PAYPHONE), 4),0) v3,
	         nvl(round(sum(T.DEV_ADSL), 4),0) v4,
	         nvl(round(sum(T.DEV_2G), 4),0) v5,
	         nvl(round(sum(T.DEV_3G1200), 4),0) v6,
	         nvl(round(sum(T.DEV_3G1300), 4),0) v7,
	         nvl(round(sum(T.DEV_3G1400), 4),0) v8 
	         FROM g_ga.DM_GM_ROYALTY_PAYMENT_DS T
	         WHERE T.ACCT_DATE = '$dateId$' 
	         and new_grid_id in (
	       SELECT DISTINCT CHANNEL_NO CHAN_ID
  FROM GBS_GI.G_GI_CHANNEL_EXT T
 WHERE T.CHANNEL_MANAGER = '$GMId$'
	<!-- 	  	  SELECT distinct a.chnl_code CHAN_ID
    			FROM PURE.CODE_CHNL_DEPART_patrol a,
    			(SELECT id from (SELECT * FROM PURE.ORG_ORGANIZATION_MOD_ACCT T WHERE T.MONTH_ID = (SELECT DECODE('$dayId$',NULL,(SELECT SUBSTR(T.CONST_VALUE, 0, 6) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'),SUBSTR('$dayId$', 0, 6)) FROM DUAL) AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8) FROM PURE.SYS_CONST_TABLE T WHERE T.CONST_TYPE = 'var.dss' AND T.CONST_NAME = 'calendar.curdate'), SUBSTR('$dayId$', 7, 8)) FROM DUAL)) start with id = (select dept_code from PURE.pure_user where login_id='$GMId$') connect BY prior id = parent_id) b 
   				where a.sale_id=b.id --> 
	         )
    GROUP BY T.NEW_GRID_ID,T.NEW_GRID_NAME
	</select>
	
	<select id="getPerfEvalTitle" resultClass="java.util.HashMap" remapResults="true">
		select 'name' key_, '网点名称' desc_, '*s' format
		  from dual
		union all
		select 'a1' key_, '2G销售' desc_, '*d' format
		  from dual
		union all
		select 'a2' key_, '3G销售' desc_, '*d' format
		  from dual
		union all
		select 'a3' key_, '欠费率' desc_, '*d' format
		  from dual
		union all
		select 'a4' key_, '离网率' desc_, '*d' format
		  from dual
	</select>
	
	<!-- 佣金统计 -->
	<select id="channelCommList" resultClass="java.util.HashMap">
		SELECT NVL(A.CHAN_ID, '') ID,
		       CASE
		         WHEN A.CHAN_ID IS NULL THEN
		          '合计'
		         ELSE
		          MAX(A.CHAN_NAME)
		       END NAME,
		       SUM(DECODE(A.KPI_CODE, '999', A.CM_KPI_VALUE, 0)) AS A1,
		       SUM(DECODE(A.KPI_CODE, '1014', A.CM_KPI_VALUE, 0)) AS A2,
		       ROUND(DECODE(SUM(DECODE(A.KPI_CODE, '999', A.CM_KPI_VALUE, 0)),
		                    0,
		                    0,
		                    SUM(DECODE(A.KPI_CODE, '1014', A.CM_KPI_VALUE, 0)) /
		                    SUM(DECODE(A.KPI_CODE, '999', A.CM_KPI_VALUE, 0))),
		             4) A3,
		       SUM(DECODE(A.KPI_CODE, '1001', A.CM_KPI_VALUE, 0)) AS V1,
		       SUM(DECODE(A.KPI_CODE, '1009', A.CM_KPI_VALUE, 0)) AS V2,
		       SUM(DECODE(A.KPI_CODE, '1005', A.CM_KPI_VALUE, 0)) AS V3,
		       SUM(DECODE(A.KPI_CODE, '1006', A.CM_KPI_VALUE, 0)) AS V4,
		       SUM(DECODE(A.KPI_CODE, '1008', A.CM_KPI_VALUE, 0)) AS V5,
		       SUM(DECODE(A.KPI_CODE, '1012', A.CM_KPI_VALUE, 0)) AS V6,
		       SUM(DECODE(A.KPI_CODE, '1013', A.CM_KPI_VALUE, 0)) AS V7,
		       SUM(DECODE(A.KPI_CODE, '1099', A.CM_KPI_VALUE, 0)) AS V8
		  FROM G_GA.DM_CHNL_CHIEF_COMM_MS A
		 WHERE A.ACCT_MONTH = '$monthId$'
		   AND CHIEF_ID IS NOT NULL
		   AND CHAN_ID IN (SELECT DISTINCT CHANNEL_NO CHAN_ID
		                     FROM GBS_GI.G_GI_CHANNEL_EXT T
		                    WHERE T.CHANNEL_MANAGER = '$GMId$')
		    <!--业务类型；-->
			<isNotNull property="svcType" >
				  <isNotEqual property="svcType" compareValue="-1">
				        and a.service_type = '$svcType$'  
				  </isNotEqual>
				  <isEqual  property="svcType" compareValue="-1">
				        and ( a.service_type = '13'  or a.service_type = '14')
				  </isEqual>
			</isNotNull>
		  group by rollup(a.chan_id)
		  order by $sortCol$ $sortOrder$
	</select>
	
	<select id="getTitles" resultClass="java.util.HashMap">
		select 'name' key_, '网点名称' desc_, '*s' format , '1' id
		  from dual
		union all
		select 'a1' key_, '出账收入' desc_, '*d' format , '2' id
		  from dual
		union all
		select 'a2' key_, '佣金' desc_, '*d' format , '3' id
		  from dual
		union all
		select 'a3' key_, '佣金占比' desc_, '*d' format , '4' id
		  from dual
	</select>
     
</sqlMap>
