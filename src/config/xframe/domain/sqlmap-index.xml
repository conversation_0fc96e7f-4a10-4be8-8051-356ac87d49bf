<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="xframe.domain.index">
	
	<select id="selectMenus" resultClass="java.util.HashMap">
		SELECT distinct t.resources_id as "id",
                t.resources_name as "text",
                t.parent_id as "pid",
                nvl(t.url, ' ') as "url",
                t.ord,
                CASE
                  WHEN EXISTS (SELECT 1
                          FROM pure_gs.pure_resources A
                         WHERE A.PARENT_ID = T.resources_id) THEN
                   '1'
                  ELSE
                   '0'
                END IS_LEAF,
                case
                  when t.ext4 is null then
                   ''
                  else
                   t.ext4
                end as icon
		  FROM pure_gs.pure_resources t
		 START WITH t.resources_id in
            (select distinct t3.resources_id
               from (select t1.*
                       from pure_gs.pure_USER_ROLE t1
                      where t1.user_id = '$userId$') t1,
                    pure_gs.pure_ROLE_PERMISSION t2,
                    pure_gs.pure_resources t3
              where t1.role_id = t2.role_id
                and t2.resources_id = t3.resources_id
                and t3.resources_type = '1'
                and t3.app_system_id = 'PURE'
             union
             select distinct t3.resources_id
               from (select t1.*
                       from pure_gs.Pure_User_Permission t1
                      where t1.user_id = '$userId$') t1,
                    pure_gs.pure_resources t3
              where t1.RESOURCES_ID = t3.resources_id
                and t3.resources_type = '1'
                and t3.app_system_id = 'PURE')
		CONNECT BY t.resources_id = PRIOR t.parent_id
	</select>
	
</sqlMap>
