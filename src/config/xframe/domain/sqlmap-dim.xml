<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="xframe.domain.dim">
    <!-- 一级地域 -->
    <select id="getAreaOptions" resultClass="java.util.HashMap">
        select area_no "vkey", area_desc "vdesc"
        from code_area
        order by ord
    </select>

    <!-- 部门 -->
    <select id="getDepartmentOptions" resultClass="java.util.HashMap">
        select department "vkey", department_desc "vdesc"
        from code_department
        where state = '1'
    </select>
    <!-- 单一部门信息 -->
    <select id="getDepartment" resultClass="java.util.HashMap">
        select department "department", department_desc "deptName", parent_dept "parentNo"
        from code_department
        where department = '$value$'
          and state = '1'
    </select>
    <!-- 部门树 -->
    <select id="getDepartmentChild" resultClass="java.util.HashMap">
        select department "id",department_desc "text",
        (case when exists (select 1 from code_department b where a.department=b.parent_dept and b.state='1') then 0 else
        1 end) "leaf"
        from code_department a
        where 1=1 and a.state='1'
        <isEqual property="department" compareValue="__unknow" prepend="and">
            region_id=#regionNo#
        </isEqual>
        <isNotEqual property="department" compareValue="__unknow" prepend="and">
            parent_dept=#department#
        </isNotEqual>
        order by department
    </select>

    <!-- 地域联动 -->
    <select id="getRegionOptions" resultClass="java.util.HashMap">
        select region_no "vkey",region_desc "vdesc" from code_region
        where region_level=#level# and parent_no=#parentValue#
        <isPropertyAvailable property="regionNo">
            and region_no=#regionNo#
        </isPropertyAvailable>
        order by ord
    </select>

    <!-- 地域联动元数据 -->
    <select id="getRegionMetaData" resultClass="java.util.HashMap">
        select max(region_level)             "maxLevel",
               'areaId|cityId|townId|sectId' "defaultIds",
               'areaId|cityId|townId|sectId' "defaultNames",
               'root'                        "rootRegionNo"
        from code_region
    </select>


    <!-- wzg add 登录后页面弹出tab相关sql -->
    <select id="getLoginbyOA" resultClass="java.lang.String">
        select *
        from (select t.login_id
              from pure_gs.pure_user t
              where t.zb_email = '$value$'
                and t.is_mp = '1')
        where rownum = 1
    </select>

    <select id="getTopmenuFlag" resultClass="java.lang.String">
        select const_value
        from sys_const_table t
        where t.const_type = 'var.dss'
          and t.const_name = 'topmenu.flag'
    </select>

    <select id="getTopmenuCount" resultClass="java.lang.String">
        select t.const_value
        from sys_const_table t
        where t.const_type = 'var.dss'
          and t.const_name = 'topmenu.count'
    </select>

    <select id="getTopmenuList" resultClass="java.util.HashMap">
		<![CDATA[
        select "resourcesId", "resourceName", "url"
        from (select t.resources_id                                                          "resourcesId",
                     t1.resources_name                                                       "resourceName",
                     t1.url                                                                  "url",
                     RANK() OVER (PARTITION BY t1.resources_name ORDER BY t.visit_date DESC) ord
              from pure_log_visit t,
                   pure_resources t1
              where t.user_id = #userId#
                and t.resources_id = t1.resources_id
              order by t.visit_date desc)
        where ord = 1
          and rownum <= #rownum#
        ]]>
	</select>

    <select id="getPositionOptions" resultClass="java.util.HashMap">
        SELECT POSITION_ID "vkey", POSITION_DESC "vdesc"
        FROM PURE_USER_POSITION
        ORDER BY P_ORD
    </select>

    <select id="getChannelPermission" resultClass="java.util.HashMap">
        SELECT CHANNEL_ID "vkey", CHANNEL_DESC "vdesc"
        FROM PURE.PURE_USER_CHANNEL
        ORDER BY ORD
    </select>

    <select id="getAreaOptionsN" resultClass="java.util.HashMap">
        SELECT T.AREA_NO "vkey", T.AREA_DESC "vdesc",T.ORD
        FROM CODE_AREA T
        <isNotEqual property="areaNo" compareValue="087" prepend="">
            where T.AREA_NO = '$areaNo$'
        </isNotEqual>
        order by t.ord
    </select>

    <select id="userGetDept" resultClass="java.util.HashMap">
        SELECT M.DEPT_CODE          "dept_code",
               N1.NAME              "dept_desc",
               M.POSITION           "pid",
               M.SALE_AREA          "sellArea",
               NVL(N2.NAME, '个人') "sellAreaDesc"
        FROM PURE_USER M,
             PURE.ORG_ORGANIZATION_MOD N1,
             ORG_ORGANIZATION_MOD N2
        WHERE M.DEPT_CODE = N1.ID(+)
          AND M.SALE_AREA = N2.ID(+)
          AND M.USER_ID = $value$

    </select>

    <select id="getDeptPath" resultClass="java.util.HashMap">
        SELECT '|' || replace(ORGRANK_NAME, '&gt;', '|') "deptPath"
        from (SELECT *
              FROM PURE.ORG_ORGANIZATION_MOD_ACCT T
              WHERE T.MONTH_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 0, 6)
                                                                  FROM PURE.SYS_CONST_TABLE T
                                                                  WHERE T.CONST_TYPE = 'var.dss'
                                                                    AND T.CONST_NAME = 'calendar.curdate'),
                                                SUBSTR('$dayId$', 0, 6))
                                  FROM DUAL)
                AND T.DAY_ID = (SELECT DECODE('$dayId$', NULL, (SELECT SUBSTR(T.CONST_VALUE, 7, 8)
                                                                FROM PURE.SYS_CONST_TABLE T
                                                                WHERE T.CONST_TYPE = 'var.dss'
                                                                  AND T.CONST_NAME = 'calendar.curdate'),
                                              SUBSTR('$dayId$', 7, 8))
                                FROM DUAL)) t
        where t.id = '$saleArea$'
          and instr(t.orgrank, 'root') > 0
    </select>

    <select id="isDutyFlag" resultClass="java.lang.Integer">
        SELECT COUNT(1)
        FROM (SELECT T.LOGIN_ID
              FROM PURE.GBS_ORGANIZATION_DUTY T,
                   PURE.ORG_ORGANIZATION_MOD B
              WHERE T.ID = B.ID
                AND B.DELFLAG = '0'
              UNION ALL
              SELECT T.VILLAGE_MANAGER_ID LOGIN_ID
              FROM GBS_GI.G_GI_VILLAGE_INFO_ORG T,
                   GBS_GI.G_GI_VILLAGE_INFO B
              WHERE T.VILLAGE_NO = B.VILLAGE_NO
                AND B.VILLAGE_SATE = '1') T
        WHERE LOGIN_ID = '$value$'
    </select>

    <select id="getPositionList" resultClass="java.util.HashMap">
        SELECT POSITION_ID, POSITION_DESC
        FROM PURE_USER_POSITION $value$
    </select>

    <select id="getPosiOptions" resultClass="java.util.HashMap">

        select POSITION_ID as "id", POSITION_DESC as "text", '' as "selected"
        FROM PURE_USER_POSITION
    </select>

    <select id="getSysLoginId" resultClass="java.lang.String">
        SELECT 'gs_' || SEQ_SYS_LOGIN_ID.Nextval
        from dual
    </select>


    <select id="selectMenus" resultClass="java.util.HashMap">
        SELECT distinct t.resources_id   as "id",
                        t.resources_name as "text",
                        t.parent_id      as "pid",
                        nvl(t.url, ' ')  as "url",
                        t.ord,
                        CASE
                            WHEN EXISTS
                                    (SELECT 1 FROM pure_resources A WHERE A.PARENT_ID = T.resources_id) THEN
                                '1'
                            ELSE
                                '0'
                            END             IS_LEAF,
                        case
                            when t.ext4 is null then
                                ''
                            else
                                t.ext4
                            end          as icon
        FROM pure_resources t
        START WITH t.resources_id != 'root'
               and t.resources_id in
                   (select distinct t3.resources_id
                    from (select t1.* from pure_USER_ROLE t1 where t1.user_id = '$userId$') t1,
                         pure_ROLE_PERMISSION t2,
                         pure_resources t3
                    where t1.role_id = t2.role_id
                      and t2.resources_id = t3.resources_id
                      and t3.resources_type = '1'
                      and t3.app_system_id = 'PURE'
                    union
                    select distinct t3.resources_id
                    from (select t1.*
                          from Pure_User_Permission t1
                          where t1.user_id = '$userId$') t1,
                         pure_resources t3
                    where t1.RESOURCES_ID = t3.resources_id
                      and t3.resources_type = '1'
                      and t3.app_system_id = 'PURE')
        CONNECT BY t.resources_id = PRIOR t.parent_id
               and t.resources_id != 'root'
               and t.resources_type = '1'
               and t.app_system_id = 'PURE'
        order by ord
    </select>

    <!-- 通过工号查找拥有用户 -->
    <select id="selectUsersByLoginid" resultClass="java.util.HashMap">
        select distinct t.login_id,
                        t.user_name,
                        (select o.name from pure_gs.org_organization_mod o where t.dept_code = o.id) org_name,
                        (select o.orgrank_name
                         from pure_gs.org_organization_mod o
                         where t.dept_code = o.id)                                                   org_name_detail
        from pure_gs.pure_user t
        where t.mobile in
              (select nvl(u.telephone, u.mobile)
               from pure_gs.pure_user u
               where u.login_id = '$Login_Id$')
           or t.telephone in
              (select nvl(u.telephone, u.mobile)
               from pure_gs.pure_user u
               where u.login_id = '$Login_Id$')
           or t.login_id = '$Login_Id$'
        order by t.area_no
    </select>


    <insert id="insertVisitLog">
        insert into pure_gs.pure_gs_visit_log
        (resources_id,
         login_id,
         visit_time)
        values (#resourcesId#,
                #loginId#,
                to_char(sysdate, 'yyyy-mm-dd hh24:mi:ss'))
    </insert>

    <!-- 通过类名和方法名查询配置表 -->
    <select id="selectLogConf" resultClass="java.util.HashMap">
        select *
        from PURE_GS.SC_LN_MODULE_LOG_CONF t
        where t.CLASS_NAME = '$className$'
          and t.MEHTOD_NAME = '$methodName$'
          and t.STATUS = '1'
    </select>


</sqlMap>
