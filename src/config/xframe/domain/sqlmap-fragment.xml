<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="xframe.domain.fragment">

	<!-- 获取唯一编号，推荐在少量关键数据中使用 -->
	<select id="getSmallId" resultClass="java.lang.String">
		select to_char(sq_sc_id.nextval) from dual
	</select>
	<!-- 获取唯一编号，推荐在大量数据中使用 -->
	<select id="getId" resultClass="java.lang.String">
		select to_char(seq_uid.nextval) from dual
	</select>
	<!-- 获取唯一编号，推荐在少量关键数据中使用 -->
	<select id="getDepartmentId" resultClass="java.lang.String">
		select to_char(seq_departmentManage.nextval) from dual
	</select>
	<!-- 查询销售单元名称 -->
	<select id="getSaleAreaName" resultClass="java.lang.String" parameterClass="java.lang.String">
		select NAME from  PURE.ORG_ORGANIZATION_MOD  where id='$value$'
	</select>
	<!-- 查询部门名称 -->
	<select id="getDeptName" resultClass="java.lang.String" parameterClass="java.lang.String">
		select DEPARTMENT_DESC from code_department where DEPARTMENT='$value$'
	</select>
		<!-- 查询 login_id_old-->
	<select id="getLoginIdOld" resultClass="java.lang.String" parameterClass="java.lang.String">
		SELECT login_id_old  FROM  PURE.pure_user WHERE login_id=#loginId#
	</select>
</sqlMap>
