# BCSHB Maven化改造项目

## 项目简介

本项目是联通甘肃分公司综合支撑系统的Maven化改造版本。原项目使用传统的Java Web开发模式，通过Maven化改造实现：

- 标准化项目结构
- 自动化依赖管理  
- 多环境配置支持
- 持续集成基础
- 云原生改造准备

## 技术栈

### 核心框架
- **Spring Framework 3.0.3** - IoC容器和AOP框架
- **Struts2 ********** - MVC框架
- **Hibernate 3.6.10** - ORM框架
- **iBatis 2.3.4** - SQL映射框架

### 数据库
- **Oracle 11g** - 主数据库
- **C3P0** - 数据库连接池

### 前端技术
- **JSP + JSTL** - 视图层技术
- **JavaScript/jQuery** - 前端交互
- **Bootstrap** - UI框架
- **ECharts** - 图表组件

### 构建工具
- **Maven 3.6+** - 项目构建和依赖管理
- **JDK 1.8** - Java开发环境

## 快速开始

### 1. 环境准备

确保已安装以下软件：
- JDK 1.8+
- Maven 3.6+
- Oracle数据库客户端

### 2. 项目初始化

如果这是首次从传统项目结构迁移到Maven，请执行：

```bash
# Windows环境
migrate-to-maven.bat

# Linux/Mac环境
chmod +x migrate-to-maven.sh
./migrate-to-maven.sh
```

### 3. 安装自定义依赖

项目包含一些自定义的JAR包，需要安装到本地Maven仓库：

```bash
chmod +x install-custom-jars.sh
./install-custom-jars.sh
```

### 4. 验证改造结果

```bash
chmod +x verify-maven-migration.sh
./verify-maven-migration.sh
```

### 5. 编译和运行

```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package

# 本地运行（使用Jetty）
mvn jetty:run
```

访问应用：http://localhost:8080/bcshb

## 项目结构

```
bcshb-mvn/
├── pom.xml                                    # Maven项目配置
├── src/
│   ├── main/
│   │   ├── java/                             # Java源代码
│   │   │   ├── com/bonc/                     # 主要业务代码
│   │   │   │   ├── product/gbs/              # GBS产品模块
│   │   │   │   ├── pure/                     # Pure框架相关
│   │   │   │   └── demo/                     # 示例代码
│   │   │   └── cn/                           # 其他包
│   │   ├── resources/                        # 配置文件和资源
│   │   │   ├── config/                       # 业务配置文件
│   │   │   │   ├── gbs/                      # GBS模块配置
│   │   │   │   ├── gms/                      # GMS模块配置
│   │   │   │   └── mobile/                   # 移动端配置
│   │   │   ├── pfcAcctConfig/                # PFC核算配置
│   │   │   ├── struts*.xml                   # Struts2配置
│   │   │   ├── sqlmap-config.xml             # iBatis配置
│   │   │   ├── log4j.properties              # 日志配置
│   │   │   ├── application-dev.properties     # 开发环境配置
│   │   │   ├── application-test.properties    # 测试环境配置
│   │   │   └── application-prod.properties    # 生产环境配置
│   │   └── webapp/                           # Web应用文件
│   │       ├── WEB-INF/                      # Web配置
│   │       │   ├── web.xml                   # Web应用配置
│   │       │   ├── applicationContext*.xml   # Spring配置
│   │       │   └── lib/                      # 依赖库（已迁移到Maven）
│   │       ├── pages/                        # JSP页面
│   │       ├── css/                          # 样式文件
│   │       ├── js/                           # JavaScript文件
│   │       ├── images/                       # 图片资源
│   │       └── resources/                    # 其他静态资源
│   └── test/
│       ├── java/                             # 测试代码
│       └── resources/                        # 测试资源
├── target/                                   # Maven构建输出
├── doc/                                      # 项目文档
├── pfcJar/                                   # PFC独立程序
└── README.md                                 # 项目说明
```

## 多环境配置

项目支持三种环境配置：

### 开发环境（默认）
```bash
mvn clean package
```

### 测试环境
```bash
mvn clean package -Ptest
```

### 生产环境
```bash
mvn clean package -Pprod
```

## 主要模块说明

### 1. GBS模块 (com.bonc.product.gbs)
- 绩效考核系统
- 营销活动管理
- 数据分析和报表

### 2. GMS模块 (com.bonc.product.gms)
- 组织机构管理
- 建筑信息管理
- BOM管理

### 3. Pure框架 (com.bonc.pure)
- 用户权限管理
- 系统基础功能
- SSO单点登录

### 4. 移动端模块 (config.mobile)
- 移动端API
- 签到管理
- 客户管理

## 开发指南

### 1. IDE配置

**IntelliJ IDEA:**
1. File → Open → 选择项目根目录
2. 等待Maven导入完成
3. 配置JDK 1.8
4. 配置Tomcat运行环境

**Eclipse:**
1. File → Import → Existing Maven Projects
2. 选择项目根目录
3. 配置JDK 1.8
4. 配置Tomcat服务器

### 2. 数据库配置

修改对应环境的配置文件：
```properties
# src/main/resources/application-dev.properties
database.driver=oracle.jdbc.driver.OracleDriver
database.url=***************************************
database.username=your_username
database.password=your_password
```

### 3. 添加新依赖

在pom.xml中添加依赖：
```xml
<dependency>
    <groupId>group.id</groupId>
    <artifactId>artifact-id</artifactId>
    <version>version</version>
</dependency>
```

### 4. 代码规范

- 遵循Java编码规范
- 使用UTF-8编码
- 添加适当的注释
- 编写单元测试

## 部署指南

### 1. 本地部署
```bash
mvn clean package
cp target/bcshb-mvn.war $TOMCAT_HOME/webapps/
```

### 2. 服务器部署
```bash
# 打包
mvn clean package -Pprod

# 上传war包到服务器
scp target/bcshb-mvn.war user@server:/path/to/tomcat/webapps/

# 重启Tomcat
ssh user@server "sudo systemctl restart tomcat"
```

## 故障排除

### 1. 编译错误
- 检查JDK版本是否为1.8
- 确认所有依赖已正确安装
- 运行 `mvn clean` 清理缓存

### 2. 运行时错误
- 检查数据库连接配置
- 确认配置文件路径正确
- 查看日志文件定位问题

### 3. 依赖问题
```bash
# 查看依赖树
mvn dependency:tree

# 强制更新依赖
mvn clean install -U
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目为内部项目，版权归联通甘肃分公司所有。

## 联系方式

如有问题，请联系项目维护团队。

---

**注意：** 本项目正在进行云原生改造，后续将支持Docker容器化部署和Kubernetes编排。
