@echo off
echo ========================================
echo Maven化改造脚本 - Windows版本
echo ========================================

echo 1. 创建Maven标准目录结构...

:: 创建主要目录结构
mkdir src\main\java 2>nul
mkdir src\main\resources 2>nul
mkdir src\main\webapp 2>nul
mkdir src\test\java 2>nul
mkdir src\test\resources 2>nul
mkdir target 2>nul

echo 2. 迁移Java源代码...
:: 迁移Java源代码 (保留原有的包结构)
if exist src\com (
    echo 迁移 src\com 到 src\main\java\com
    xcopy src\com src\main\java\com /E /I /Y
)

if exist src\cn (
    echo 迁移 src\cn 到 src\main\java\cn
    xcopy src\cn src\main\java\cn /E /I /Y
)

if exist src\org (
    echo 迁移 src\org 到 src\main\java\org
    xcopy src\org src\main\java\org /E /I /Y
)

echo 3. 迁移配置文件...
:: 迁移配置文件到resources目录
if exist src\config (
    echo 迁移 src\config 到 src\main\resources\config
    xcopy src\config src\main\resources\config /E /I /Y
)

:: 迁移根目录下的配置文件
if exist src\*.xml (
    echo 迁移XML配置文件到resources目录
    copy src\*.xml src\main\resources\ /Y
)

if exist src\*.properties (
    echo 迁移properties配置文件到resources目录
    copy src\*.properties src\main\resources\ /Y
)

:: 迁移静态资源
if exist src\static (
    echo 迁移静态资源到resources目录
    xcopy src\static src\main\resources\static /E /I /Y
)

echo 4. 迁移Web应用文件...
:: 迁移Web应用相关文件
if exist app (
    echo 迁移Web应用文件到src\main\webapp
    xcopy app src\main\webapp /E /I /Y
)

echo 5. 创建测试目录结构...
:: 创建测试相关目录
mkdir src\test\java\com\bonc\bcshb 2>nul
mkdir src\test\resources 2>nul

echo 6. 处理特殊配置文件...
:: 处理pfcAcctConfig目录
if exist pfcAcctConfig (
    echo 迁移pfcAcctConfig到resources目录
    xcopy pfcAcctConfig src\main\resources\pfcAcctConfig /E /I /Y
)

echo 7. 创建环境配置文件...
:: 创建不同环境的配置文件
echo # 开发环境配置 > src\main\resources\application-dev.properties
echo database.driver=oracle.jdbc.driver.OracleDriver >> src\main\resources\application-dev.properties
echo database.url=*************************************** >> src\main\resources\application-dev.properties
echo database.username=pure_gs >> src\main\resources\application-dev.properties
echo database.password=pure_gs >> src\main\resources\application-dev.properties

echo # 测试环境配置 > src\main\resources\application-test.properties
echo database.driver=oracle.jdbc.driver.OracleDriver >> src\main\resources\application-test.properties
echo database.url=************************************* >> src\main\resources\application-test.properties
echo database.username=pure_gs_test >> src\main\resources\application-test.properties
echo database.password=pure_gs_test >> src\main\resources\application-test.properties

echo # 生产环境配置 > src\main\resources\application-prod.properties
echo database.driver=oracle.jdbc.driver.OracleDriver >> src\main\resources\application-prod.properties
echo database.url=**************************************** >> src\main\resources\application-prod.properties
echo database.username=pure_gs >> src\main\resources\application-prod.properties
echo database.password=pure_gs >> src\main\resources\application-prod.properties

echo 8. 创建Maven包装器...
:: 下载Maven Wrapper (如果需要)
echo 建议使用Maven Wrapper确保构建环境一致性

echo 9. 创建.gitignore文件...
echo # Maven > .gitignore
echo target/ >> .gitignore
echo pom.xml.tag >> .gitignore
echo pom.xml.releaseBackup >> .gitignore
echo pom.xml.versionsBackup >> .gitignore
echo pom.xml.next >> .gitignore
echo release.properties >> .gitignore
echo dependency-reduced-pom.xml >> .gitignore
echo buildNumber.properties >> .gitignore
echo .mvn/timing.properties >> .gitignore
echo .mvn/wrapper/maven-wrapper.jar >> .gitignore
echo. >> .gitignore
echo # IDE >> .gitignore
echo .idea/ >> .gitignore
echo *.iml >> .gitignore
echo .eclipse/ >> .gitignore
echo .metadata/ >> .gitignore
echo .settings/ >> .gitignore
echo .project >> .gitignore
echo .classpath >> .gitignore
echo. >> .gitignore
echo # OS >> .gitignore
echo .DS_Store >> .gitignore
echo Thumbs.db >> .gitignore
echo. >> .gitignore
echo # Logs >> .gitignore
echo *.log >> .gitignore
echo logs/ >> .gitignore

echo ========================================
echo Maven化改造完成！
echo ========================================
echo.
echo 下一步操作：
echo 1. 检查迁移后的文件结构
echo 2. 运行 mvn clean compile 验证编译
echo 3. 运行 mvn clean package 打包测试
echo 4. 配置IDE导入Maven项目
echo 5. 逐步验证各模块功能
echo.
echo 注意事项：
echo - 原始文件已保留，请确认迁移无误后再删除
echo - 检查所有配置文件路径是否正确
echo - 验证依赖jar包是否完整
echo - 测试数据库连接配置
echo ========================================

pause
