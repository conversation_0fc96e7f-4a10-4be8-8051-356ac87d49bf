#!/bin/bash

echo "========================================"
echo "Maven化改造脚本 - Linux版本"
echo "========================================"

echo "1. 创建Maven标准目录结构..."

# 创建主要目录结构
mkdir -p src/main/java
mkdir -p src/main/resources
mkdir -p src/main/webapp
mkdir -p src/test/java
mkdir -p src/test/resources
mkdir -p target

echo "2. 迁移Java源代码..."
# 迁移Java源代码 (保留原有的包结构)
if [ -d "src/com" ]; then
    echo "迁移 src/com 到 src/main/java/com"
    cp -r src/com src/main/java/
fi

if [ -d "src/cn" ]; then
    echo "迁移 src/cn 到 src/main/java/cn"
    cp -r src/cn src/main/java/
fi

if [ -d "src/org" ]; then
    echo "迁移 src/org 到 src/main/java/org"
    cp -r src/org src/main/java/
fi

echo "3. 迁移配置文件..."
# 迁移配置文件到resources目录
if [ -d "src/config" ]; then
    echo "迁移 src/config 到 src/main/resources/config"
    cp -r src/config src/main/resources/
fi

# 迁移根目录下的配置文件
if ls src/*.xml 1> /dev/null 2>&1; then
    echo "迁移XML配置文件到resources目录"
    cp src/*.xml src/main/resources/
fi

if ls src/*.properties 1> /dev/null 2>&1; then
    echo "迁移properties配置文件到resources目录"
    cp src/*.properties src/main/resources/
fi

# 迁移静态资源
if [ -d "src/static" ]; then
    echo "迁移静态资源到resources目录"
    cp -r src/static src/main/resources/
fi

echo "4. 迁移Web应用文件..."
# 迁移Web应用相关文件
if [ -d "app" ]; then
    echo "迁移Web应用文件到src/main/webapp"
    cp -r app/* src/main/webapp/
fi

echo "5. 创建测试目录结构..."
# 创建测试相关目录
mkdir -p src/test/java/com/bonc/bcshb
mkdir -p src/test/resources

echo "6. 处理特殊配置文件..."
# 处理pfcAcctConfig目录
if [ -d "pfcAcctConfig" ]; then
    echo "迁移pfcAcctConfig到resources目录"
    cp -r pfcAcctConfig src/main/resources/
fi

echo "7. 创建环境配置文件..."
# 创建不同环境的配置文件
cat > src/main/resources/application-dev.properties << EOF
# 开发环境配置
database.driver=oracle.jdbc.driver.OracleDriver
database.url=***************************************
database.username=pure_gs
database.password=pure_gs
EOF

cat > src/main/resources/application-test.properties << EOF
# 测试环境配置
database.driver=oracle.jdbc.driver.OracleDriver
database.url=*************************************
database.username=pure_gs_test
database.password=pure_gs_test
EOF

cat > src/main/resources/application-prod.properties << EOF
# 生产环境配置
database.driver=oracle.jdbc.driver.OracleDriver
database.url=****************************************
database.username=pure_gs
database.password=pure_gs
EOF

echo "8. 创建Maven包装器..."
# 下载Maven Wrapper (如果需要)
echo "建议使用Maven Wrapper确保构建环境一致性"

echo "9. 创建.gitignore文件..."
cat > .gitignore << EOF
# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE
.idea/
*.iml
.eclipse/
.metadata/
.settings/
.project
.classpath

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/
EOF

echo "10. 设置执行权限..."
chmod +x migrate-to-maven.sh

echo "========================================"
echo "Maven化改造完成！"
echo "========================================"
echo
echo "下一步操作："
echo "1. 检查迁移后的文件结构"
echo "2. 运行 mvn clean compile 验证编译"
echo "3. 运行 mvn clean package 打包测试"
echo "4. 配置IDE导入Maven项目"
echo "5. 逐步验证各模块功能"
echo
echo "注意事项："
echo "- 原始文件已保留，请确认迁移无误后再删除"
echo "- 检查所有配置文件路径是否正确"
echo "- 验证依赖jar包是否完整"
echo "- 测试数据库连接配置"
echo "========================================"
